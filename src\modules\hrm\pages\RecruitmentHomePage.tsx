import React from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Trang chính của module Recruitment
 */
import { ModuleCard } from '@/modules/components/card';
import { ResponsiveGrid } from '@/shared/components/common';

interface RecruitmentModule {
  id: string;
  title: string;
  description: string;
  icon: string;
  countLabel: string;
  linkTo: string;
  linkText: string;
  disabled?: boolean;
}

/**
 * Component trang chính Recruitment
 */
const RecruitmentHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'hrm']);

  // Định nghĩa các module con của Recruitment
  const modules: RecruitmentModule[] = [
    // {
    //   id: 'job-positions',
    //   title: t('hrm:recruitment.modules.jobPositions.title', 'Vị trí tuyển dụng'),
    //   description: t(
    //     'hrm:recruitment.modules.jobPositions.description',
    //     'Quản lý các vị trí tuyển dụng'
    //   ),
    //   icon: 'briefcase',
    //   countLabel: t('hrm:recruitment.modules.jobPositions.countLabel', 'Vị trí'),
    //   linkTo: '/hrm/recruitment/job-positions',
    //   linkText: t('common:view', 'Xem'),
    // },
    {
      id: 'candidates',
      title: t('hrm:recruitment.modules.candidates.title', 'Ứng viên'),
      description: t('hrm:recruitment.modules.candidates.description', 'Quản lý hồ sơ ứng viên'),
      icon: 'users',
      countLabel: t('hrm:recruitment.modules.candidates.countLabel', 'Ứng viên'),
      linkTo: '/hrm/recruitment/candidates',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'recruitment-process',
      title: t('hrm:recruitment.modules.process.title', 'Quy trình tuyển dụng'),
      description: t(
        'hrm:recruitment.modules.process.description',
        'Thiết lập quy trình tuyển dụng'
      ),
      icon: 'workflow',
      countLabel: t('hrm:recruitment.modules.process.countLabel', 'Quy trình'),
      linkTo: '/hrm/recruitment/process',
      linkText: t('common:view', 'Xem'),
    },
    {
      id: 'interviews',
      title: t('hrm:recruitment.modules.interviews.title', 'Lịch phỏng vấn'),
      description: t('hrm:recruitment.modules.interviews.description', 'Quản lý lịch phỏng vấn'),
      icon: 'calendar',
      countLabel: t('hrm:recruitment.modules.interviews.countLabel', 'Cuộc phỏng vấn'),
      linkTo: '/hrm/recruitment/interviews',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'reports',
      title: t('hrm:recruitment.modules.reports.title', 'Báo cáo tuyển dụng'),
      description: t('hrm:recruitment.modules.reports.description', 'Thống kê và báo cáo'),
      icon: 'reports',
      countLabel: t('hrm:recruitment.modules.reports.countLabel', 'Tuyển dụng tháng này'),
      linkTo: '/hrm/recruitment/reports',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
    {
      id: 'settings',
      title: t('hrm:recruitment.modules.settings.title', 'Cài đặt'),
      description: t(
        'hrm:recruitment.modules.settings.description',
        'Cấu hình hệ thống tuyển dụng'
      ),
      icon: 'settings',
      countLabel: t('hrm:recruitment.modules.settings.countLabel', 'Cấu hình'),
      linkTo: '/hrm/recruitment/settings',
      linkText: t('common:view', 'Xem'),
      disabled: true,
    },
  ];

  return (
    <div className="w-full">
      {/* Modules Grid */}
      <div>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 3 }}>
          {modules.map(module => (
            <ModuleCard
              key={module.id}
              title={module.title}
              description={module.description}
              icon={module.icon}
              linkTo={module.linkTo}
            />
          ))}
        </ResponsiveGrid>
      </div>
    </div>
  );
};

export default RecruitmentHomePage;
