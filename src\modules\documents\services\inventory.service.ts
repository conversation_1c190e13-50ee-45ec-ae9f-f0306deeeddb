import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  InventoryItemDto,
  InventoryQueryParams,
  UpdateInventoryQuantityDto,
  CreateInventoryDto,
} from '../types/inventory.types';

/**
 * Service xử lý API liên quan đến inventory
 */
export const InventoryService = {
  /**
   * L<PERSON>y danh sách sản phẩm trong kho
   * @param params Tham số truy vấn
   * @returns Danh sách sản phẩm trong kho với phân trang
   */
  getInventoryItems: async (params?: InventoryQueryParams): Promise<ApiResponseDto<PaginatedResult<InventoryItemDto>>> => {
    return apiClient.get('/user/inventories', { params });
  },

  /**
   * <PERSON><PERSON><PERSON> danh sách sản phẩm trong kho theo warehouseId
   * @param warehouseId ID của kho
   * @param params Tham số truy vấn bổ sung
   * @returns Danh sách sản phẩm trong kho với phân trang
   */
  getInventoryItemsByWarehouse: async (
    warehouseId: number,
    params?: Omit<InventoryQueryParams, 'warehouseId'>
  ): Promise<ApiResponseDto<PaginatedResult<InventoryItemDto>>> => {
    return apiClient.get('/user/inventories', {
      params: {
        ...params,
        warehouseId,
      },
    });
  },

  /**
   * Lấy chi tiết một item inventory theo ID
   * @param id ID của inventory item
   * @returns Chi tiết inventory item
   */
  getInventoryItemById: async (id: number): Promise<ApiResponseDto<InventoryItemDto>> => {
    return apiClient.get(`/user/inventories/${id}`);
  },

  /**
   * Cập nhật số lượng sản phẩm trong kho
   * @param id ID của inventory item
   * @param data Dữ liệu cập nhật
   * @returns Inventory item đã cập nhật
   */
  updateInventoryQuantity: async (
    id: number,
    data: UpdateInventoryQuantityDto
  ): Promise<ApiResponseDto<InventoryItemDto>> => {
    return apiClient.put(`/user/inventories/${id}`, data);
  },

  /**
   * Tạo mới inventory item
   * @param data Dữ liệu tạo mới
   * @returns Inventory item đã tạo
   */
  createInventoryItem: async (data: CreateInventoryDto): Promise<ApiResponseDto<InventoryItemDto>> => {
    return apiClient.post('/user/inventories', data);
  },

  /**
   * Xóa inventory item
   * @param id ID của inventory item
   * @returns Kết quả xóa
   */
  deleteInventoryItem: async (id: number): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/user/inventories/${id}`);
  },

  /**
   * Cập nhật số lượng hàng loạt
   * @param updates Danh sách cập nhật
   * @returns Kết quả cập nhật
   */
  bulkUpdateQuantities: async (
    updates: Array<{ id: number; data: UpdateInventoryQuantityDto }>
  ): Promise<ApiResponseDto<InventoryItemDto[]>> => {
    return apiClient.put('/user/inventories/bulk-update', { updates });
  },
};
