import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { AttachmentService, TodoAttachmentService } from '../services/attachment.service';
import {
  AttachmentQueryDto,
  CreateAttachmentDto,
  UpdateAttachmentDto,
} from '../types/attachment.types';

// Key for React Query
const ATTACHMENTS_QUERY_KEY = 'attachments';
const TODO_ATTACHMENTS_QUERY_KEY = 'todo-attachments';

/**
 * Hook to get attachments for a task
 * @param taskId Task ID
 * @param params Query parameters
 * @returns Query result with attachments
 */
export const useAttachments = (taskId: number, params?: AttachmentQueryDto) => {
  return useQuery({
    queryKey: [ATTACHMENTS_QUERY_KEY, taskId, params],
    queryFn: () => AttachmentService.getAttachments(taskId, params),
    select: data => data.result,
    enabled: !!taskId,
  });
};

/**
 * Hook to get an attachment by ID
 * @param taskId Task ID
 * @param attachmentId Attachment ID
 * @returns Query result with the attachment
 */
export const useAttachment = (taskId: number, attachmentId: number) => {
  return useQuery({
    queryKey: [ATTACHMENTS_QUERY_KEY, taskId, attachmentId],
    queryFn: () => AttachmentService.getAttachment(taskId, attachmentId),
    select: data => data.result,
    enabled: !!taskId && !!attachmentId,
  });
};

/**
 * Hook to get todo attachments for a specific todo
 * @param todoId Todo ID
 * @returns Query result with todo attachments
 */
export const useTodoAttachments = (todoId: number) => {
  return useQuery({
    queryKey: [TODO_ATTACHMENTS_QUERY_KEY, 'by-todo', todoId],
    queryFn: () => TodoAttachmentService.getAttachmentsByTodoId(todoId),
    select: data => data.result,
    enabled: !!todoId,
  });
};

/**
 * Hook to create a new attachment
 * @returns Mutation result for creating an attachment
 */
export const useCreateAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAttachmentDto) => AttachmentService.createAttachment(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};

/**
 * Hook to update an attachment
 * @returns Mutation result for updating an attachment
 */
export const useUpdateAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      taskId,
      attachmentId,
      data,
    }: {
      taskId: number;
      attachmentId: number;
      data: UpdateAttachmentDto;
    }) => AttachmentService.updateAttachment(taskId, attachmentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId] });
      queryClient.invalidateQueries({
        queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId, variables.attachmentId],
      });
    },
  });
};

/**
 * Hook to delete an attachment
 * @returns Mutation result for deleting an attachment
 */
export const useDeleteAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, attachmentId }: { taskId: number; attachmentId: number }) =>
      AttachmentService.deleteAttachment(taskId, attachmentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [ATTACHMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};

/**
 * Hook to get upload URL for an attachment
 * @returns Mutation result for getting an upload URL
 */
export const useGetUploadUrl = () => {
  return useMutation({
    mutationFn: ({
      taskId,
      fileName,
      fileType,
    }: {
      taskId: number;
      fileName: string;
      fileType: string;
    }) => AttachmentService.getUploadUrl(taskId, fileName, fileType),
  });
};

/**
 * Hook to create upload URL for todo attachment
 * @returns Mutation result for creating upload URL
 */
export const useCreateTodoUploadUrl = () => {
  return useMutation({
    mutationFn: ({
      todoId,
      fileName,
      mimeType,
      fileSize,
    }: {
      todoId: number;
      fileName: string;
      mimeType: string;
      fileSize: number;
    }) => TodoAttachmentService.createUploadUrl(todoId, fileName, mimeType, fileSize),
  });
};

/**
 * Hook to confirm todo attachment upload
 * @returns Mutation result for confirming upload
 */
export const useConfirmTodoUpload = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      todoId,
      s3Key,
      fileName,
      contentType,
      size,
      uploadId,
    }: {
      todoId: number;
      s3Key: string;
      fileName: string;
      contentType?: string;
      size?: number;
      uploadId?: string;
    }) => TodoAttachmentService.confirmUpload(todoId, s3Key, fileName, contentType, size, uploadId),
    onSuccess: (_, variables) => {
      // Invalidate todo attachments query to refresh the list
      queryClient.invalidateQueries({ queryKey: [TODO_ATTACHMENTS_QUERY_KEY, 'by-todo', variables.todoId] });
    },
  });
};

/**
 * Hook to delete todo attachment
 * @returns Mutation result for deleting attachment
 */
export const useDeleteTodoAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ attachmentId }: { attachmentId: number }) =>
      TodoAttachmentService.deleteAttachment(attachmentId),
    onSuccess: () => {
      // Invalidate all todo attachments queries to refresh the lists
      queryClient.invalidateQueries({ queryKey: [TODO_ATTACHMENTS_QUERY_KEY] });
    },
  });
};
