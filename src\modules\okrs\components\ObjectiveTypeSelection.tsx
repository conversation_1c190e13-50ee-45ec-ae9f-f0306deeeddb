import { Building2, User, Users } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { ListOverviewCard } from '@/shared/components/widgets';
import { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';

import { ObjectiveType } from '../types/objective.types';

export interface ObjectiveTypeSelectionProps {
  /**
   * Callback khi chọn loại mục tiêu
   */
  onSelectType: (type: ObjectiveType) => void;

  /**
   * Class name bổ sung
   */
  className?: string;
}

/**
 * Component chọn loại mục tiêu cho quy trình tạo mục tiêu 2 bước
 */
const ObjectiveTypeSelection: React.FC<ObjectiveTypeSelectionProps> = ({ onSelectType }) => {
  const { t } = useTranslation(['okrs', 'common']);

  // Tạo dữ liệu cho các card lựa chọn
  const objectiveTypeCards: OverviewCardProps[] = [
    {
      title: t('okrs:objective.type.individual', 'Cá nhân'),
      value: t('okrs:objective.typeSelection.individual.value', 'Mục tiêu cá nhân'),
      description: t(
        'okrs:objective.typeSelection.individual.description',
        'Tạo mục tiêu cho bản thân'
      ),
      icon: User,
      color: 'blue',
      hoverable: true,
      onClick: () => onSelectType(ObjectiveType.INDIVIDUAL),
    },
    {
      title: t('okrs:objective.type.department', 'Phòng ban'),
      value: t('okrs:objective.typeSelection.department.value', 'Mục tiêu phòng ban'),
      description: t(
        'okrs:objective.typeSelection.department.description',
        'Tạo mục tiêu cho phòng ban'
      ),
      icon: Users,
      color: 'green',
      hoverable: true,
      onClick: () => onSelectType(ObjectiveType.DEPARTMENT),
    },
    {
      title: t('okrs:objective.type.company', 'Công ty'),
      value: t('okrs:objective.typeSelection.company.value', 'Mục tiêu công ty'),
      description: t(
        'okrs:objective.typeSelection.company.description',
        'Tạo mục tiêu cho toàn công ty'
      ),
      icon: Building2,
      color: 'purple',
      hoverable: true,
      onClick: () => onSelectType(ObjectiveType.COMPANY),
    },
  ];

  return (
    <div className={`w-full`}>
      <ListOverviewCard
        items={objectiveTypeCards}
        maxColumns={{ xs: 1, sm: 1, md: 3, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={4}
      />
    </div>
  );
};

export default ObjectiveTypeSelection;
