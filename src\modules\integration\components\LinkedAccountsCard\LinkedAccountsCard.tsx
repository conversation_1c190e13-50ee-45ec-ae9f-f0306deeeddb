import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Button, Typography, Icon, Badge } from '@/shared/components/common';

import { fetchLinkedAccounts } from '../../api/accountMockData';
import { BankAccount } from '../../types/account';

// Bank logo component
interface BankLogoProps {
  bankCode: string;
}

const BankLogo: React.FC<BankLogoProps> = ({ bankCode }) => {
  // Màu sắc cho từng ngân hàng
  const bankColors: Record<string, string> = {
    MB: 'bg-destructive',
    VCB: 'bg-primary',
    TCB: 'bg-purple-600',
    ACB: 'bg-success',
    VPB: 'bg-warning',
  };

  const bgColor = bankColors[bankCode] || 'bg-muted-foreground';

  return (
    <div className={`flex items-center justify-center w-8 h-8 ${bgColor} rounded-full`}>
      <span className="text-white text-xs font-bold">{bankCode}</span>
      {bankCode === 'MB' && <span className="text-white text-xs absolute ml-4 mt-1">★</span>}
    </div>
  );
};

interface AccountItemProps {
  account: BankAccount;
  onDelete?: (id: string) => void;
  showDeleteButton?: boolean;
  customLogo?: React.ReactNode;
  actionIcon?: 'trash' | 'power' | string;
  actionTitle?: string;
}

/**
 * Component to display a single bank account item
 */
const AccountItem: React.FC<AccountItemProps> = ({
  account,
  onDelete,
  showDeleteButton = false,
  customLogo,
  actionIcon = 'trash',
  actionTitle,
}) => {
  const { t } = useTranslation();

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDelete) {
      onDelete(account.id);
    }
  };

  // Xác định tiêu đề cho nút hành động
  const buttonTitle = actionTitle || t('common.delete', 'Xóa');

  return (
    <div className="flex items-center p-3 border-b border-border last:border-0 relative">
      <div className="mr-3">
        {customLogo ? customLogo : <BankLogo bankCode={account.bankCode} />}
      </div>
      <div className="flex-1">
        <Typography variant="body1" weight="bold">
          {account.accountName}
        </Typography>
        <Typography variant="body2" color="muted">
          {account.accountNumber}
        </Typography>
        {account.isDefault && (
          <div className="mt-1">
            <Badge variant="success" size="sm">
              {t('integration.accounts.defaultAccount', 'Tài khoản mặc định')}
            </Badge>
          </div>
        )}
      </div>
      {showDeleteButton && onDelete && (
        <button
          onClick={handleDelete}
          className="p-2 text-muted-foreground hover:text-destructive rounded-full hover:bg-muted transition-colors absolute top-2 right-2"
          title={buttonTitle}
        >
          <Icon name={actionIcon as 'trash' | 'power'} size="sm" />
        </button>
      )}
    </div>
  );
};

export interface LinkedAccountsCardProps {
  /**
   * Maximum number of accounts to display
   */
  maxAccounts?: number;

  /**
   * Whether to show the "Add" button
   */
  showAddButton?: boolean;

  /**
   * Callback when "Add" button is clicked
   */
  onAddClick?: () => void;

  /**
   * Callback when "Delete" button is clicked
   */
  onDeleteClick?: (id: string) => void;

  /**
   * Whether to show delete buttons
   */
  showDeleteButtons?: boolean;

  /**
   * Custom logo component to use instead of default bank logo
   */
  customLogo?: React.ReactNode;

  /**
   * Icon to use for the action button (default: 'trash')
   */
  actionIcon?: 'trash' | 'power' | string;

  /**
   * Title for the action button
   */
  actionTitle?: string;

  /**
   * List of accounts to display (if not provided, will fetch from API)
   */
  accounts?: BankAccount[];

  /**
   * Additional CSS class
   */
  className?: string;
}

/**
 * Component to display a card with linked bank accounts
 */
const LinkedAccountsCard: React.FC<LinkedAccountsCardProps> = ({
  maxAccounts = 10,
  showAddButton = true,
  onAddClick,
  onDeleteClick,
  showDeleteButtons = false,
  customLogo,
  actionIcon = 'trash',
  actionTitle,
  accounts: providedAccounts,
  className = '',
}) => {
  const { t } = useTranslation();
  const [accounts, setAccounts] = useState<BankAccount[]>([]);
  const [isLoading, setIsLoading] = useState(providedAccounts ? false : true);
  const [error, setError] = useState<Error | null>(null);

  // Fetch accounts data if not provided
  useEffect(() => {
    if (providedAccounts) {
      setAccounts(providedAccounts);
      return;
    }

    const loadAccounts = async () => {
      try {
        setIsLoading(true);
        const data = await fetchLinkedAccounts();
        setAccounts(data);
        setError(null);
      } catch (err) {
        setError(
          err instanceof Error
            ? err
            : new Error(t('integration.accounts.failedToLoad', 'Failed to load accounts'))
        );
      } finally {
        setIsLoading(false);
      }
    };

    loadAccounts();
  }, [providedAccounts, t]);

  // Handle add button click
  const handleAddClick = () => {
    if (onAddClick) {
      onAddClick();
    }
  };

  // Handle delete button click
  const handleDeleteClick = (id: string) => {
    if (onDeleteClick) {
      onDeleteClick(id);
    }
  };

  // Limit the number of accounts to display
  const displayedAccounts = accounts.slice(0, maxAccounts);

  return (
    <Card className={`overflow-hidden ${className}`}>
      {isLoading ? (
        <div className="flex justify-center items-center py-8">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      ) : error ? (
        <div className="p-4 text-center text-destructive">
          <Icon name="alert-circle" size="lg" className="mb-2" />
          <Typography>{t('common.error', 'Đã xảy ra lỗi')}</Typography>
        </div>
      ) : displayedAccounts.length === 0 ? (
        <div className="p-4 text-center">
          <Typography color="muted">{t('integration.accounts.noAccounts')}</Typography>
        </div>
      ) : (
        <div className="divide-y divide-border">
          {displayedAccounts.map(account => (
            <AccountItem
              key={account.id}
              account={account}
              onDelete={handleDeleteClick}
              showDeleteButton={showDeleteButtons}
              customLogo={customLogo}
              actionIcon={actionIcon}
              actionTitle={actionTitle}
            />
          ))}
        </div>
      )}

      {showAddButton && (
        <div className="p-4 pt-2">
          <Button
            variant="primary"
            fullWidth
            onClick={handleAddClick}
            leftIcon={<Icon name="plus" size="sm" />}
          >
            {t('common.add', 'Thêm')}
          </Button>
        </div>
      )}
    </Card>
  );
};

export default LinkedAccountsCard;
