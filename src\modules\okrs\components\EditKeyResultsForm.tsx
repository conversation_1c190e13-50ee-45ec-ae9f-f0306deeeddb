import React, { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { ObjectiveDto } from '@/modules/okrs/types/objective.types';
import {
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Typography,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/useFormErrors';
import { NotificationUtil } from '@/shared/utils/notification';

export interface KeyResultProgressData {
  id: number;
  title: string;
  currentValue: number;
  targetValue: number;
  unit: string;
  progress: number;
}

export interface EditKeyResultsFormProps {
  /**
   * Objective chứa các key results cần chỉnh sửa
   */
  objective: ObjectiveDto;

  /**
   * Callback khi form được submit thành công
   */
  onSuccess?: () => void;

  /**
   * Callback khi form bị đóng
   */
  onCancel?: () => void;
}

/**
 * Form chỉnh sửa tiến độ của các Key Results
 */
const EditKeyResultsForm: React.FC<EditKeyResultsFormProps> = ({ 
  objective, 
  onSuccess, 
  onCancel 
}) => {
  const { t } = useTranslation();
  const formRef = useRef<any>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [keyResults, setKeyResults] = useState<KeyResultProgressData[]>([]);
  
  const { formRef: errorFormRef, setFormErrors } = useFormErrors<Record<string, any>>();

  // Schema validation cho form
  const keyResultsSchema = z.object({
    keyResults: z.array(z.object({
      id: z.number(),
      currentValue: z.number().min(0, {
        message: t('okrs:keyResult.validation.currentValueMin', 'Giá trị hiện tại phải >= 0'),
      }),
    })),
  });

  // Mock data - trong thực tế sẽ lấy từ API
  useEffect(() => {
    // TODO: Gọi API để lấy danh sách key results của objective
    const mockKeyResults: KeyResultProgressData[] = [
      {
        id: 1,
        title: 'Tăng doanh thu lên 100 triệu',
        currentValue: 75,
        targetValue: 100,
        unit: 'triệu VND',
        progress: 75,
      },
      {
        id: 2,
        title: 'Tăng số lượng khách hàng lên 1000',
        currentValue: 650,
        targetValue: 1000,
        unit: 'khách hàng',
        progress: 65,
      },
      {
        id: 3,
        title: 'Giảm thời gian phản hồi xuống 2 giờ',
        currentValue: 3.5,
        targetValue: 2,
        unit: 'giờ',
        progress: 43, // Tính ngược vì là giảm
      },
    ];
    
    setKeyResults(mockKeyResults);
  }, [objective.id]);

  // Xử lý khi submit form
  const handleSubmit = async (values: unknown) => {
    try {
      setIsSubmitting(true);
      const formValues = values as {
        keyResults: Array<{
          id: number;
          currentValue: number;
        }>;
      };

      // TODO: Gọi API để cập nhật tiến độ key results
      console.log('Updating key results progress:', formValues.keyResults);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('okrs:keyResult.updateProgressSuccess', 'Cập nhật tiến độ Key Results thành công'),
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      // Xử lý lỗi
      console.error('Error updating key results progress:', error);
      NotificationUtil.error({
        message: t('okrs:keyResult.updateProgressError', 'Có lỗi xảy ra khi cập nhật tiến độ'),
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Xử lý khi nhấn nút hủy
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // Tính toán tiến độ dựa trên giá trị hiện tại
  const calculateProgress = (currentValue: number, targetValue: number, unit: string): number => {
    if (targetValue === 0) {return 0;}
    
    // Đối với các đơn vị thời gian (giờ, phút, giây), tiến độ tính ngược
    const isTimeUnit = ['giờ', 'phút', 'giây', 'hour', 'minute', 'second'].includes(unit.toLowerCase());
    
    if (isTimeUnit) {
      // Với thời gian, giá trị nhỏ hơn = tiến độ cao hơn
      const progress = Math.max(0, Math.min(100, ((targetValue - currentValue) / targetValue) * 100));
      return Math.round(progress);
    } else {
      // Với các đơn vị khác, giá trị cao hơn = tiến độ cao hơn
      const progress = Math.min(100, (currentValue / targetValue) * 100);
      return Math.round(progress);
    }
  };

  // Xử lý thay đổi giá trị hiện tại
  const handleCurrentValueChange = (keyResultId: number, newValue: number) => {
    setKeyResults(prev => prev.map(kr => {
      if (kr.id === keyResultId) {
        const progress = calculateProgress(newValue, kr.targetValue, kr.unit);
        return {
          ...kr,
          currentValue: newValue,
          progress,
        };
      }
      return kr;
    }));
  };

  return (
    <Card title={t('okrs:keyResult.editProgressTitle', 'Chỉnh sửa tiến độ Key Results')} className="mx-auto max-w-4xl">
      <div className="mb-6">
        <Typography variant="h6" className="mb-2">
          {objective.title}
        </Typography>
        <Typography variant="body2" className="text-muted-foreground">
          {t('okrs:keyResult.editProgressDescription', 'Cập nhật tiến độ hiện tại cho các Key Results của mục tiêu này')}
        </Typography>
      </div>

      <Form ref={formRef} schema={keyResultsSchema} onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          {keyResults.map((keyResult, index) => (
            <div key={keyResult.id} className="border rounded-lg p-4 bg-muted/20">
              <div className="mb-3">
                <Typography variant="subtitle1" className="font-medium">
                  {keyResult.title}
                </Typography>
                <div className="flex items-center justify-between mt-2 text-sm text-muted-foreground">
                  <span>
                    {t('okrs:keyResult.target', 'Mục tiêu')}: {keyResult.targetValue} {keyResult.unit}
                  </span>
                  <span className="font-medium">
                    {t('okrs:keyResult.progress', 'Tiến độ')}: {keyResult.progress}%
                  </span>
                </div>
              </div>

              <FormItem 
                name={`keyResults.${index}.currentValue`} 
                label={t('okrs:keyResult.currentValue', 'Giá trị hiện tại')}
                required
              >
                <div className="flex items-center space-x-2">
                  <Input
                    type="number"
                    step="0.1"
                    min="0"
                    value={keyResult.currentValue}
                    onChange={(e) => {
                      const newValue = parseFloat(e.target.value) || 0;
                      handleCurrentValueChange(keyResult.id, newValue);
                      if (formRef.current) {
                        formRef.current.setValues({
                          [`keyResults.${index}.currentValue`]: newValue,
                        });
                      }
                    }}
                    placeholder={t('okrs:keyResult.currentValuePlaceholder', 'Nhập giá trị hiện tại')}
                    className="flex-1"
                  />
                  <span className="text-sm text-muted-foreground min-w-fit">
                    {keyResult.unit}
                  </span>
                </div>
              </FormItem>

              {/* Progress bar */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500 transition-all duration-300"
                    style={{ width: `${keyResult.progress}%` }}
                  ></div>
                </div>
              </div>

              {/* Hidden fields for form submission */}
              <input type="hidden" name={`keyResults.${index}.id`} value={keyResult.id} />
            </div>
          ))}
        </div>

        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} disabled={isSubmitting}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isSubmitting}>
            {t('okrs:keyResult.updateProgress', 'Cập nhật tiến độ')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditKeyResultsForm;
