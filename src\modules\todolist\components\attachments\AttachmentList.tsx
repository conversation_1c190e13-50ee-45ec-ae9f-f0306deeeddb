import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';

import { useAuth } from '@/shared';
import { Typography, IconButton, Tooltip, Card, Loading, IconCard } from '@/shared/components/common';
import { formatDate } from '@/shared/utils/date';
import { NotificationUtil } from '@/shared/utils/notification';
import { useTodoAttachmentQueue } from '@/shared/hooks/common/useTodoAttachmentQueue';

import { useDeleteTodoAttachment } from '../../hooks/useAttachments';
import { AttachmentDto, AttachmentType, TodoAttachmentResponseDto } from '../../types/attachment.types';

interface AttachmentListProps {
  taskId: number;
  attachments: TodoAttachmentResponseDto[];
  isLoading: boolean;
  onRefresh: () => void;
}

/**
 * Component to display a list of attachments for a task
 */
const AttachmentList: React.FC<AttachmentListProps> = ({
  taskId,
  attachments,
  isLoading,
  onRefresh,
}) => {
  const { t } = useTranslation(['common', 'todolist']);
  const { user } = useAuth();
  const { mutateAsync: deleteTodoAttachment } = useDeleteTodoAttachment();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Task queue for file uploads - sẽ được đẩy vào QueuePanel
  const attachmentQueue = useTodoAttachmentQueue({
    concurrency: 3,
    autoRemoveCompletedAfter: 30000, // 30 seconds
  });

  // Handle delete attachment
  const handleDeleteAttachment = async (attachmentId: number) => {
    try {
      await deleteTodoAttachment({ attachmentId });
      NotificationUtil.success({
        message: t(
          'todolist:attachment.notifications.deleteSuccess',
          'Attachment deleted successfully'
        ),
      });
      onRefresh();
    } catch (error) {
      console.error('Error deleting attachment:', error);
      NotificationUtil.error({
        message: t('todolist:attachment.notifications.deleteError', 'Error deleting attachment'),
      });
    }
  };

  // Get attachment icon based on content type
  const getAttachmentIcon = (contentType?: string) => {
    if (!contentType) return 'file';

    if (contentType.startsWith('image/')) return 'image';
    if (contentType.startsWith('video/')) return 'video';
    if (contentType.startsWith('audio/')) return 'music';
    if (contentType.includes('pdf')) return 'file-text';
    if (contentType.includes('word') || contentType.includes('document')) return 'file-text';
    if (contentType.includes('excel') || contentType.includes('spreadsheet')) return 'table';
    if (contentType.includes('powerpoint') || contentType.includes('presentation')) return 'presentation';

    return 'file';
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) {return '0 Bytes';}
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))  } ${  sizes[i]}`;
  };

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) {return;}

    // Add files to task queue for upload - sẽ hiển thị trong QueuePanel
    Array.from(files).forEach(file => {
      attachmentQueue.addTodoAttachmentUpload({
        todoId: taskId,
        file,
        onSuccess: () => {
          // Refresh attachments list
          onRefresh();
        },
        onError: (error) => {
          console.error('Error uploading attachment:', error);
          NotificationUtil.error({
            message: t('todolist:attachment.notifications.uploadError', 'Error uploading file'),
          });
        },
      });
    });

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle click upload IconCard
  const handleClickUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  if (isLoading) {
    return <Loading />;
  }

  return (
    <div className="space-y-6">
      {/* Upload section with IconCard */}
      <div className="flex items-center justify-between">
        <Typography variant="h6">{t('todolist:attachment.title', 'Attachments')}</Typography>
        <div className="flex items-center space-x-2">
          <IconCard
            icon="upload"
            onClick={handleClickUpload}
            title={t('todolist:attachment.actions.upload', 'Upload File')}
            variant="primary"
            size="md"
          />
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileSelect}
            className="hidden"
            multiple
          />
        </div>
      </div>

      {attachments.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          {t(
            'todolist:attachment.empty',
            'No attachments yet. Upload files to share with the team.'
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {attachments.map(attachment => (
            <Card key={attachment.id} className="p-4 hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-lg">
                    {attachment.contentType?.startsWith('image/') ? (
                      <img
                        src={attachment.url}
                        alt={attachment.filename}
                        className="w-10 h-10 object-cover rounded-lg"
                      />
                    ) : (
                      <IconButton
                        icon={getAttachmentIcon(attachment.contentType)}
                        size="md"
                        variant="ghost"
                      />
                    )}
                  </div>
                  <div>
                    <Typography variant="subtitle2" className="font-medium line-clamp-1">
                      {attachment.filename}
                    </Typography>
                    <Typography variant="caption" className="text-gray-500">
                      {formatFileSize(attachment.size || 0)}
                    </Typography>
                  </div>
                </div>

                <div className="flex space-x-1">
                  <Tooltip content={t('todolist:attachment.actions.download', 'Download')}>
                    <IconButton
                      icon="download"
                      size="sm"
                      variant="ghost"
                      onClick={() => window.open(attachment.url, '_blank')}
                    />
                  </Tooltip>
                  {user?.id && user.id.toString() === attachment.createdBy.toString() && (
                    <Tooltip content={t('common:delete', 'Delete')}>
                      <IconButton
                        icon="trash"
                        size="sm"
                        variant="ghost"
                        className="text-red-500"
                        onClick={() => handleDeleteAttachment(attachment.id)}
                      />
                    </Tooltip>
                  )}
                </div>
              </div>

              <div className="mt-2 pt-2 border-t border-gray-100 flex justify-between items-center">
                <Typography variant="caption" className="text-gray-500">
                  {t('todolist:attachment.uploadedBy', 'Uploaded by')}:{' '}
                  {`User ${attachment.createdBy}`}
                </Typography>
                <Typography variant="caption" className="text-gray-500">
                  {formatDate(attachment.createdAt || 0)}
                </Typography>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default AttachmentList;
