import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Typo<PERSON>, Divide<PERSON>, Textarea, Button, DatePicker, IconButton } from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/form-date-utils';

import { TaskDto } from '../../types/task.types';

interface TaskDetailInfoProps {
  task: TaskDto;
  onUpdateDescription?: (description: string) => void;
  onUpdateStartDate?: (startDate: number | null) => void;
  onUpdateDeadline?: (deadline: number | null) => void;
}

/**
 * Task detail information component
 */
const TaskDetailInfo: React.FC<TaskDetailInfoProps> = ({
  task,
  onUpdateDescription,
  onUpdateStartDate,
  onUpdateDeadline
}) => {
  const { t } = useTranslation(['todolist']);
  const [isEditing, setIsEditing] = useState(false);
  const [description, setDescription] = useState(task.description || '');
  const [isEditingStartDate, setIsEditingStartDate] = useState(false);
  const [isEditingDeadline, setIsEditingDeadline] = useState(false);
  const [startDate, setStartDate] = useState<Date | null>(
    task.startDate ? new Date(task.startDate) : null
  );
  const [deadline, setDeadline] = useState<Date | null>(
    task.deadline ? new Date(task.deadline) : null
  );

  const handleSave = () => {
    if (onUpdateDescription) {
      onUpdateDescription(description);
    }
    setIsEditing(false);
  };

  const handleCancel = () => {
    setDescription(task.description || '');
    setIsEditing(false);
  };

  const handleSaveStartDate = () => {
    if (onUpdateStartDate) {
      onUpdateStartDate(startDate ? startDate.getTime() : null);
    }
    setIsEditingStartDate(false);
  };

  const handleCancelStartDate = () => {
    setStartDate(task.startDate ? new Date(task.startDate) : null);
    setIsEditingStartDate(false);
  };

  const handleSaveDeadline = () => {
    if (onUpdateDeadline) {
      onUpdateDeadline(deadline ? deadline.getTime() : null);
    }
    setIsEditingDeadline(false);
  };

  const handleCancelDeadline = () => {
    setDeadline(task.deadline ? new Date(task.deadline) : null);
    setIsEditingDeadline(false);
  };

  return (
    <div className="space-y-4">
      <div>
        <div className="flex justify-between items-center mb-2">
          <Typography variant="h6">
            {t('todolist:task.fields.description', 'Description')}
          </Typography>
          {!isEditing && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              {t('common:edit', 'Edit')}
            </Button>
          )}
        </div>

        {isEditing ? (
          <div className="space-y-3">
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder={t('todolist:task.descriptionPlaceholder', 'Enter task description...')}
              rows={4}
              autoSize={{ minRows: 4, maxRows: 10 }}
              fullWidth
            />
            <div className="flex justify-end space-x-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                {t('common:cancel', 'Cancel')}
              </Button>
              <Button variant="primary" size="sm" onClick={handleSave}>
                {t('common:save', 'Save')}
              </Button>
            </div>
          </div>
        ) : (
          <div className="min-h-[100px] p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
            <Typography variant="body1" className="whitespace-pre-wrap">
              {task.description || t('todolist:task.noDescription', 'No description provided')}
            </Typography>
          </div>
        )}
      </div>

      <Divider />

      <div className="grid grid-cols-2 gap-4">
        {/* Ngày bắt đầu */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <Typography variant="subtitle2" className="text-gray-500">
              {t('todolist:task.fields.startDate', 'Start Date')}
            </Typography>
            {!isEditingStartDate && (
              <IconButton
                icon="edit"
                size="sm"
                variant="ghost"
                onClick={() => setIsEditingStartDate(true)}
              />
            )}
          </div>
          {isEditingStartDate ? (
            <div className="space-y-2">
              <DatePicker
                value={startDate}
                onChange={setStartDate}
                placeholder={t('todolist:task.selectStartDate', 'Select start date')}
                showTime
                format="DD/MM/YYYY HH:mm"
              />
              <div className="flex justify-end space-x-1">
                <Button variant="outline" size="xs" onClick={handleCancelStartDate}>
                  {t('common:cancel', 'Cancel')}
                </Button>
                <Button variant="primary" size="xs" onClick={handleSaveStartDate}>
                  {t('common:save', 'Save')}
                </Button>
              </div>
            </div>
          ) : (
            <Typography variant="body2">
              {task.startDate ? formatTimestamp(task.startDate) : t('todolist:task.noStartDate', 'Not set')}
            </Typography>
          )}
        </div>

        {/* Ngày hết hạn */}
        <div>
          <div className="flex justify-between items-center mb-1">
            <Typography variant="subtitle2" className="text-gray-500">
              {t('todolist:task.fields.deadline', 'Deadline')}
            </Typography>
            {!isEditingDeadline && (
              <IconButton
                icon="edit"
                size="sm"
                variant="ghost"
                onClick={() => setIsEditingDeadline(true)}
              />
            )}
          </div>
          {isEditingDeadline ? (
            <div className="space-y-2">
              <DatePicker
                value={deadline}
                onChange={setDeadline}
                placeholder={t('todolist:task.selectDeadline', 'Select deadline')}
                showTime
                format="DD/MM/YYYY HH:mm"
              />
              <div className="flex justify-end space-x-1">
                <Button variant="outline" size="xs" onClick={handleCancelDeadline}>
                  {t('common:cancel', 'Cancel')}
                </Button>
                <Button variant="primary" size="xs" onClick={handleSaveDeadline}>
                  {t('common:save', 'Save')}
                </Button>
              </div>
            </div>
          ) : (
            <Typography variant="body2" className={task.deadline && task.deadline < Date.now() ? 'text-red-500' : ''}>
              {task.deadline ? formatTimestamp(task.deadline) : t('todolist:task.noDeadline', 'Not set')}
            </Typography>
          )}
        </div>

        <div>
          <Typography variant="subtitle2" className="text-gray-500">
            {t('todolist:task.fields.createdAt', 'Created At')}
          </Typography>
          <Typography variant="body2">{formatTimestamp(task.createdAt || 0)}</Typography>
        </div>
        <div>
          <Typography variant="subtitle2" className="text-gray-500">
            {t('todolist:task.fields.updatedAt', 'Updated At')}
          </Typography>
          <Typography variant="body2">{formatTimestamp(task.updatedAt || 0)}</Typography>
        </div>
        {task.completedAt && (
          <div>
            <Typography variant="subtitle2" className="text-gray-500">
              {t('todolist:task.fields.completedAt', 'Completed At')}
            </Typography>
            <Typography variant="body2">{formatTimestamp(task.completedAt)}</Typography>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskDetailInfo;
