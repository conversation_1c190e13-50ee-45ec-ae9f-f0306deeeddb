import React from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Icon, Button } from '@/shared/components/common';

export interface BankAccountInfoProps {
  /**
   * Tên ngân hàng
   */
  bankName: string;

  /**
   * Tên tài khoản
   */
  accountName: string;

  /**
   * Số tài khoản
   */
  accountNumber: string;

  /**
   * Phương thức kết nối
   */
  connectionMethod: string;

  /**
   * Số dư tạm tính
   */
  balance: string;

  /**
   * Callback khi click vào nút kết nối
   */
  onConnect?: () => void;

  /**
   * Trạng thái loading
   */
  isLoading?: boolean;
}

/**
 * Component hiển thị thông tin tài khoản ngân hàng
 */
const BankAccountInfo: React.FC<BankAccountInfoProps> = ({
  bankName,
  accountName,
  accountNumber,
  connectionMethod,
  balance,
  onConnect,
  isLoading = false,
}) => {
  const { t } = useTranslation();
  return (
    <Card className="p-6">
      {/* Tiêu đề */}
      <div className="mb-6">
        <Typography variant="h4" className="text-primary font-bold mb-2">
          {t('integration.banking.bankAccount', 'Tài khoản ngân hàng')}
        </Typography>
      </div>

      {/* Thông tin ngân hàng */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Cột bên trái */}
        <div className="space-y-6">
          {/* Ngân hàng */}
          <div className="flex items-center">
            <div className="w-8 h-8 flex items-center justify-center mr-3">
              <Icon name="building" size="md" />
            </div>
            <div>
              <Typography variant="body2" color="muted">
                {t('integration.banking.bank', 'Ngân hàng')}
              </Typography>
              <div className="bg-warning/10 px-2 py-1 rounded mt-1 inline-block">
                <Typography variant="body1" className="font-medium">
                  {bankName}
                </Typography>
              </div>
            </div>
          </div>

          {/* Phương thức kết nối */}
          <div className="flex items-center">
            <div className="w-8 h-8 flex items-center justify-center mr-3">
              <Icon name="power" size="md" color="orange" />
            </div>
            <div>
              <Typography variant="body2" color="muted">
                {t('integration.banking.connectionMethod', 'Phương thức kết nối')}
              </Typography>
              <Typography variant="body1" className="font-medium text-warning">
                {connectionMethod}
              </Typography>
            </div>
          </div>

          {/* Số dư tạm tính */}
          <div className="flex items-center">
            <div className="w-8 h-8 flex items-center justify-center mr-3">
              <Icon name="document" size="md" />
            </div>
            <div>
              <Typography variant="body2" color="muted">
                {t('integration.banking.estimatedBalance', 'Số dư tạm tính')}
              </Typography>
              <Typography variant="body1" className="font-medium text-primary">
                {balance}
              </Typography>
            </div>
          </div>
        </div>

        {/* Cột bên phải */}
        <div className="space-y-6">
          {/* Tên tài khoản */}
          <div className="flex items-center">
            <div className="w-8 h-8 flex items-center justify-center mr-3">
              <Icon name="user" size="md" />
            </div>
            <div>
              <Typography variant="body2" color="muted">
                {t('integration.banking.accountName', 'Tên tài khoản')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {accountName}
              </Typography>
            </div>
          </div>

          {/* Số tài khoản */}
          <div className="flex items-center">
            <div className="w-8 h-8 flex items-center justify-center mr-3">
              <Icon name="list" size="md" />
            </div>
            <div>
              <Typography variant="body2" color="muted">
                {t('integration.banking.accountNumber', 'Số tài khoản')}
              </Typography>
              <Typography variant="body1" className="font-medium">
                {accountNumber}
              </Typography>
            </div>
          </div>
        </div>
      </div>

      {/* Nút kết nối */}
      <Button
        className="w-full bg-gradient-to-r from-red-600 to-red-400 hover:from-red-700 hover:to-red-500 border-none"
        onClick={onConnect}
        isLoading={isLoading}
      >
        {t('integration.banking.connectApi', 'Kết nối API Banking')}
      </Button>
    </Card>
  );
};

export default BankAccountInfo;
