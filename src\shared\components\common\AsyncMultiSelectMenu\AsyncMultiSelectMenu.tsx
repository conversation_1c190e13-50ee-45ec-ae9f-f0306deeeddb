import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { createPortal } from 'react-dom';

import { 
  Chip, 
  Icon, 
  Input, 
  ScrollArea, 
  Checkbox, 
  Button, 
  Typography 
} from '@/shared/components/common';

export interface AsyncMultiSelectOption {
  id: string | number;
  label: string;
  value: string | number;
  icon?: string;
  disabled?: boolean;
}

export interface SelectedOption {
  id: string | number;
  label: string;
  value: string | number;
}

export interface AsyncMultiSelectMenuProps {
  /**
   * Trạng thái mở/đóng menu
   */
  isOpen: boolean;
  
  /**
   * Callback khi đóng menu
   */
  onClose: () => void;
  
  /**
   * Danh sách options đã chọn
   */
  selectedOptions: SelectedOption[];
  
  /**
   * Callback khi thay đổi selection
   */
  onSelectionChange: (options: SelectedOption[]) => void;
  
  /**
   * Hàm load options từ API với pagination
   */
  loadOptions: (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => Promise<{
    items: AsyncMultiSelectOption[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }>;
  
  /**
   * Reference đến trigger element để positioning
   */
  triggerRef?: React.RefObject<HTMLElement>;
  
  /**
   * Tiêu đề menu
   */
  title?: string;
  
  /**
   * Placeholder cho search input
   */
  searchPlaceholder?: string;
  
  /**
   * Số items mỗi trang
   */
  itemsPerPage?: number;
  
  /**
   * Thời gian debounce cho search (ms)
   */
  debounceTime?: number;
  
  /**
   * Message khi không có options
   */
  noOptionsMessage?: string;
  
  /**
   * Message khi đang loading
   */
  loadingMessage?: string;
  
  /**
   * Tự động load trang đầu tiên khi mở
   */
  autoLoadInitial?: boolean;
}

/**
 * Component AsyncMultiSelectMenu - Menu multi-select với khả năng tải dữ liệu từ API có phân trang
 */
const AsyncMultiSelectMenu: React.FC<AsyncMultiSelectMenuProps> = ({
  isOpen,
  onClose,
  selectedOptions,
  onSelectionChange,
  loadOptions,
  triggerRef,
  title,
  searchPlaceholder,
  itemsPerPage = 20,
  debounceTime = 300,
  noOptionsMessage,
  loadingMessage,
  autoLoadInitial = true,
}) => {
  const { t } = useTranslation(['common', 'todolist']);
  const [searchTerm, setSearchTerm] = useState('');
  const [options, setOptions] = useState<AsyncMultiSelectOption[]>([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  // Load data function
  const loadData = useCallback(async (params: {
    search?: string;
    page?: number;
    reset?: boolean;
  }) => {
    const { search = '', page = 1, reset = false } = params;
    
    setLoading(true);
    try {
      const result = await loadOptions({
        search,
        page,
        limit: itemsPerPage,
      });

      if (reset || page === 1) {
        setOptions(result.items);
      } else {
        setOptions(prev => [...prev, ...result.items]);
      }

      setCurrentPage(result.currentPage);
      setHasMore(result.currentPage < result.totalPages);
    } catch (error) {
      console.error('Error loading options:', error);
      if (reset || page === 1) {
        setOptions([]);
      }
    } finally {
      setLoading(false);
    }
  }, [loadOptions, itemsPerPage]);

  // Debounced search
  const debouncedSearch = useCallback((searchValue: string) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    
    debounceTimeoutRef.current = setTimeout(() => {
      setSearchTerm(searchValue);
      setCurrentPage(1);
      loadData({ search: searchValue, page: 1, reset: true });
    }, debounceTime);
  }, [loadData, debounceTime]);

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    debouncedSearch(value);
  };

  // Load more data when scrolling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    
    if (
      scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
      hasMore &&
      !loading
    ) {
      loadData({ search: searchTerm, page: currentPage + 1 });
    }
  }, [hasMore, loading, searchTerm, currentPage, loadData]);

  // Check if option is selected
  const isOptionSelected = (option: AsyncMultiSelectOption) => {
    return selectedOptions.some(selected => selected.id === option.id);
  };

  // Handle option toggle
  const handleOptionToggle = (option: AsyncMultiSelectOption) => {
    if (option.disabled) return;
    
    const isSelected = isOptionSelected(option);
    
    if (isSelected) {
      // Remove from selection
      const newSelection = selectedOptions.filter(selected => selected.id !== option.id);
      onSelectionChange(newSelection);
    } else {
      // Add to selection
      const newOption: SelectedOption = {
        id: option.id,
        label: option.label,
        value: option.value,
      };
      onSelectionChange([...selectedOptions, newOption]);
    }
  };

  // Handle clear all
  const handleClearAll = () => {
    onSelectionChange([]);
  };

  // Calculate menu position
  const getMenuPosition = () => {
    if (triggerRef?.current) {
      const triggerRect = triggerRef.current.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const menuWidth = 320;
      const menuHeight = 400;
      const margin = 8;

      let top = triggerRect.bottom + margin;
      let left = triggerRect.left;

      // Adjust if menu overflows viewport
      if (left + menuWidth > viewportWidth - margin) {
        left = viewportWidth - menuWidth - margin;
      }
      if (left < margin) {
        left = margin;
      }

      if (top + menuHeight > viewportHeight - margin) {
        top = triggerRect.top - menuHeight - margin;
      }
      if (top < margin) {
        top = margin;
      }

      return { top, left };
    }

    // Fallback positioning
    return { top: 100, left: 100 };
  };

  // Update menu position when opened
  useEffect(() => {
    if (isOpen) {
      const position = getMenuPosition();
      setMenuPosition(position);
      
      // Focus search input
      if (searchInputRef.current) {
        searchInputRef.current.focus();
      }
      
      // Load initial data if needed
      if (autoLoadInitial && options.length === 0) {
        loadData({ search: '', page: 1, reset: true });
      }
    }
  }, [isOpen, autoLoadInitial, options.length, loadData]);

  // Cleanup debounce timeout
  useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  if (!isOpen) {
    return null;
  }

  const handleClickOutside = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  };

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const menuContent = (
    <>
      <div className="fixed inset-0 z-[99999]" onClick={handleClickOutside} />
      <div
        ref={menuRef}
        className="fixed bg-card rounded-lg shadow-xl z-[100000] overflow-hidden animate-fade-in"
        style={{
          width: '320px',
          top: `${menuPosition.top}px`,
          left: `${menuPosition.left}px`,
          maxHeight: '400px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        }}
        onClick={handleMenuClick}
      >
        <div className="p-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-3">
            <Typography variant="subtitle2" weight="medium">
              {title || t('todolist:task.filters.tags.title', 'Lọc theo tag')}
            </Typography>
            {selectedOptions.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAll}
                className="text-destructive hover:text-destructive/80"
              >
                {t('common:clearAll', 'Xóa tất cả')}
              </Button>
            )}
          </div>

          {/* Search */}
          <div className="mb-3">
            <Input
              ref={searchInputRef}
              type="text"
              placeholder={searchPlaceholder || t('common:search', 'Tìm kiếm...')}
              onChange={handleSearchChange}
              className="text-sm w-full"
              leftIcon={<Icon name="search" size="sm" />}
            />
          </div>

          {/* Selected Options */}
          {selectedOptions.length > 0 && (
            <div className="mb-3">
              <Typography variant="caption" color="muted" className="mb-2">
                {t('common:selected', 'Đã chọn')} ({selectedOptions.length})
              </Typography>
              <div className="flex flex-wrap gap-1">
                {selectedOptions.map(option => (
                  <Chip
                    key={option.id}
                    size="sm"
                    variant="primary"
                    closable
                    onClose={() => handleOptionToggle({
                      id: option.id,
                      label: option.label,
                      value: option.value,
                    })}
                  >
                    {option.label}
                  </Chip>
                ))}
              </div>
            </div>
          )}

          {/* Options List */}
          <ScrollArea className="max-h-64" onScroll={handleScroll}>
            <div className="space-y-1">
              {loading && options.length === 0 ? (
                <div className="flex items-center justify-center p-4">
                  <Icon name="loading" className="animate-spin mr-2" size="sm" />
                  <Typography variant="body2" color="muted">
                    {loadingMessage || t('common:loading', 'Đang tải...')}
                  </Typography>
                </div>
              ) : options.length > 0 ? (
                <>
                  {options.map(option => {
                    const isSelected = isOptionSelected(option);
                    return (
                      <div
                        key={option.id}
                        className={`flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 cursor-pointer ${
                          option.disabled ? 'opacity-50 cursor-not-allowed' : ''
                        }`}
                        onClick={() => handleOptionToggle(option)}
                      >
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleOptionToggle(option)}
                          size="sm"
                          disabled={option.disabled}
                        />
                        {option.icon && (
                          <Icon name={option.icon as any} size="sm" className="text-muted-foreground" />
                        )}
                        <Typography variant="body2">
                          {option.label}
                        </Typography>
                      </div>
                    );
                  })}

                  {/* Loading more indicator */}
                  {loading && options.length > 0 && (
                    <div className="flex items-center justify-center p-2">
                      <Icon name="loading" className="animate-spin mr-2" size="sm" />
                      <Typography variant="caption" color="muted">
                        {t('common:loadingMore', 'Đang tải thêm...')}
                      </Typography>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-4">
                  <Typography variant="body2" color="muted">
                    {noOptionsMessage || t('common:noResults', 'Không có kết quả')}
                  </Typography>
                </div>
              )}
            </div>
          </ScrollArea>
        </div>
      </div>
    </>
  );

  // Use portal to render menu at body level
  return createPortal(menuContent, document.body);
};

export default AsyncMultiSelectMenu;
