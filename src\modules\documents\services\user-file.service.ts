
import { apiClient } from '@/shared/api/axios';
import { QueryDto } from '@/shared/dto/request/query.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

// Types
export interface FileResponseDto {
  id: number;
  name: string;
  folderId: number;
  storageKey: string;
  size: number | null;
  viewUrl: string;
  createdAt: number;
  updatedAt: number;
}

/**
 * Response khi tạo file mới - API trả về string URL upload
 */
export type CreateFileResponseDto = string;

export interface QueryFileDto extends QueryDto {
  userId?: number;
  warehouseId?: number;
  folderId?: number;
  sortBy?: 'id' | 'name' | 'size' | 'createdAt' | 'updatedAt';
}

export interface CreateFileDto {
  /**
   * ID người dùng (sẽ được set tự động từ JWT)
   */
  userId?: number;

  /**
   * Tên file
   */
  name: string;

  /**
   * ID kho ảo
   */
  warehouseId?: number;

  /**
   * ID thư mục cha
   */
  folderId?: number | null;

  /**
   * Kích thước file (bytes)
   */
  size: number;
}

export interface UpdateFileDto {
  name?: string;
  folderId?: number | null;
}

/**
 * Service xử lý API cho file của người dùng
 */
export class UserFileService {
  private static readonly BASE_URL = '/user/files';

  /**
   * Lấy danh sách file với phân trang và lọc
   */
  static async getFiles(params: QueryFileDto): Promise<PaginatedResult<FileResponseDto>> {
    const response = await apiClient.get<PaginatedResult<FileResponseDto>>(
      this.BASE_URL,
      { params }
    );
    return response.result;
  }

  /**
   * Lấy danh sách file theo ID thư mục
   */
  static async getFilesByFolderId(folderId: number): Promise<FileResponseDto[]> {
    const response = await apiClient.get<FileResponseDto[]>(
      `${this.BASE_URL}/folder/${folderId}`
    );
    return response.result;
  }

  /**
   * Lấy thông tin file theo ID
   */
  static async getFileById(id: number): Promise<FileResponseDto> {
    const response = await apiClient.get<FileResponseDto>(
      `${this.BASE_URL}/${id}`
    );
    return response.result;
  }

  /**
   * Tạo mới file - trả về URL upload
   */
  static async createFile(data: CreateFileDto): Promise<CreateFileResponseDto> {
    const response = await apiClient.post<CreateFileResponseDto>(
      this.BASE_URL,
      data
    );
    return response.result;
  }

  /**
   * Cập nhật file
   */
  static async updateFile(id: number, data: UpdateFileDto): Promise<FileResponseDto> {
    const response = await apiClient.put<FileResponseDto>(
      `${this.BASE_URL}/${id}`,
      data
    );
    return response.result;
  }

  /**
   * Xóa file
   */
  static async deleteFile(id: number): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Tạo file và lấy URL upload để upload lên cloud
   * @param file File cần upload
   * @param folderId ID thư mục cha
   * @param warehouseId ID kho ảo
   * @returns Thông tin file và URL upload
   */
  static async createFileForUpload(
    file: File,
    folderId?: number,
    warehouseId?: number
  ): Promise<CreateFileResponseDto> {
    const createFileData: CreateFileDto = {
      name: file.name,
      size: file.size,
      folderId: folderId || null,
      warehouseId,
    };

    return this.createFile(createFileData);
  }
}
