/**
 * Chat WebSocket Service
 * Handles real-time communication for chat functionality
 */

import { io, Socket } from 'socket.io-client';

import { ChatMessage } from '../websocket/types/chat-message.types';
import { 
  ClientToServerEvents, 
  ServerToClientEvents,
  EventAcknowledgment 
} from '../websocket/types/websocket-events.types';

// Service configuration
export interface ChatWebSocketConfig {
  url: string;
  autoConnect?: boolean;
  reconnection?: boolean;
  reconnectionAttempts?: number;
  reconnectionDelay?: number;
  timeout?: number;
  auth?: {
    token?: string;
    userId?: string;
  };
}

// Connection status
export type ConnectionStatus = 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error';

// Event listener type
export type EventListener<T = unknown> = (data: T) => void;

// Event unsubscribe function
export type EventUnsubscribe = () => void;

/**
 * Chat WebSocket Service Class
 */
export class ChatWebSocketService {
  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;
  private config: ChatWebSocketConfig;
  private connectionStatus: ConnectionStatus = 'disconnected';
  private eventListeners: Map<string, Set<EventListener>> = new Map();
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  constructor(config: ChatWebSocketConfig) {
    this.config = {
      autoConnect: true,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      timeout: 20000,
      ...config,
    };

    if (this.config.autoConnect) {
      this.connect();
    }
  }

  /**
   * Connect to WebSocket server
   */
  public connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve();
        return;
      }

      this.setConnectionStatus('connecting');

      this.socket = io(this.config.url, {
        autoConnect: true,
        reconnection: this.config.reconnection,
        reconnectionAttempts: this.config.reconnectionAttempts,
        reconnectionDelay: this.config.reconnectionDelay,
        timeout: this.config.timeout,
        auth: this.config.auth,
      });

      // Connection events
      this.socket.on('connect', () => {
        console.log('[ChatWebSocket] Connected to server');
        this.setConnectionStatus('connected');
        this.reconnectAttempts = 0;
        resolve();
      });

      this.socket.on('disconnect', (reason) => {
        console.log('[ChatWebSocket] Disconnected:', reason);
        this.setConnectionStatus('disconnected');
        this.emit('connection_status', { 
          status: 'disconnected', 
          timestamp: new Date().toISOString(),
          reason 
        });
      });

      this.socket.on('connect_error', (error) => {
        console.error('[ChatWebSocket] Connection error:', error);
        this.setConnectionStatus('error');
        this.emit('error', {
          code: 'CONNECTION_ERROR',
          message: error.message,
          timestamp: new Date().toISOString(),
        });
        reject(error);
      });

      this.socket.on('reconnect', (attemptNumber) => {
        console.log('[ChatWebSocket] Reconnected after', attemptNumber, 'attempts');
        this.setConnectionStatus('connected');
        this.reconnectAttempts = 0;
      });

      this.socket.on('reconnect_attempt', (attemptNumber) => {
        console.log('[ChatWebSocket] Reconnection attempt', attemptNumber);
        this.setConnectionStatus('reconnecting');
        this.reconnectAttempts = attemptNumber;
      });

      this.socket.on('reconnect_error', (error) => {
        console.error('[ChatWebSocket] Reconnection error:', error);
        this.setConnectionStatus('error');
      });

      this.socket.on('reconnect_failed', () => {
        console.error('[ChatWebSocket] Reconnection failed');
        this.setConnectionStatus('error');
        this.emit('error', {
          code: 'RECONNECTION_FAILED',
          message: 'Failed to reconnect to server',
          timestamp: new Date().toISOString(),
        });
      });

      // Set up server event listeners
      this.setupServerEventListeners();
    });
  }

  /**
   * Disconnect from WebSocket server
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.setConnectionStatus('disconnected');
    }
  }

  /**
   * Check if connected
   */
  public isConnected(): boolean {
    return this.socket?.connected ?? false;
  }

  /**
   * Get connection status
   */
  public getConnectionStatus(): ConnectionStatus {
    return this.connectionStatus;
  }

  /**
   * Join a chat room
   */
  public async joinRoom(roomId: string, userId: string, userInfo?: { name: string; avatar?: string }): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    return new Promise((resolve, reject) => {
      this.socket!.emit('join_room', { roomId, userId, userInfo }, (ack: EventAcknowledgment) => {
        if (ack.success) {
          resolve();
        } else {
          reject(new Error(ack.error || 'Failed to join room'));
        }
      });
    });
  }

  /**
   * Leave a chat room
   */
  public async leaveRoom(roomId: string, userId: string): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    return new Promise((resolve, reject) => {
      this.socket!.emit('leave_room', { roomId, userId }, (ack: EventAcknowledgment) => {
        if (ack.success) {
          resolve();
        } else {
          reject(new Error(ack.error || 'Failed to leave room'));
        }
      });
    });
  }

  /**
   * Send a message
   */
  public async sendMessage(
    roomId: string, 
    content: ChatMessage['content'], 
    replyTo?: string,
    tempId?: string
  ): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    return new Promise((resolve, reject) => {
      this.socket!.emit('send_message', { roomId, content, replyTo, tempId }, (ack: EventAcknowledgment) => {
        if (ack.success) {
          resolve();
        } else {
          reject(new Error(ack.error || 'Failed to send message'));
        }
      });
    });
  }

  /**
   * Start typing indicator
   */
  public startTyping(roomId: string, userId: string): void {
    if (this.isConnected()) {
      this.socket!.emit('typing_start', { roomId, userId });
    }
  }

  /**
   * Stop typing indicator
   */
  public stopTyping(roomId: string, userId: string): void {
    if (this.isConnected()) {
      this.socket!.emit('typing_stop', { roomId, userId });
    }
  }

  /**
   * Submit form data
   */
  public async submitForm(
    formId: string,
    messageId: string,
    formData: Record<string, unknown>,
    roomId: string
  ): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Not connected to server');
    }

    return new Promise((resolve, reject) => {
      this.socket!.emit('form_submit', { 
        formId, 
        messageId, 
        formData, 
        validation: true,
        roomId 
      }, (ack: EventAcknowledgment) => {
        if (ack.success) {
          resolve();
        } else {
          reject(new Error(ack.error || 'Failed to submit form'));
        }
      });
    });
  }

  /**
   * Update form field
   */
  public updateFormField(
    formId: string,
    messageId: string,
    fieldName: string,
    fieldValue: unknown,
    roomId: string
  ): void {
    if (this.isConnected()) {
      this.socket!.emit('form_field_update', {
        formId,
        messageId,
        fieldName,
        fieldValue,
        roomId,
      });
    }
  }

  /**
   * Subscribe to server events
   */
  public on<K extends keyof ServerToClientEvents>(
    event: K,
    listener: EventListener<Parameters<ServerToClientEvents[K]>[0]>
  ): EventUnsubscribe {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }

    this.eventListeners.get(event)!.add(listener);

    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(event);
      if (listeners) {
        listeners.delete(listener);
        if (listeners.size === 0) {
          this.eventListeners.delete(event);
        }
      }
    };
  }

  /**
   * Emit event to listeners
   */
  private emit<K extends keyof ServerToClientEvents>(
    event: K,
    data: Parameters<ServerToClientEvents[K]>[0]
  ): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      for (const listener of listeners) {
        try {
          listener(data);
        } catch (error) {
          console.error(`[ChatWebSocket] Error in event listener for ${event}:`, error);
        }
      }
    }
  }

  /**
   * Set connection status and emit event
   */
  private setConnectionStatus(status: ConnectionStatus): void {
    if (this.connectionStatus !== status) {
      this.connectionStatus = status;
      this.emit('connection_status', {
        status,
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Set up server event listeners
   */
  private setupServerEventListeners(): void {
    if (!this.socket) {return;}

    // Message events
    this.socket.on('message_received', (data) => this.emit('message_received', data));
    this.socket.on('message_updated', (data) => this.emit('message_updated', data));
    this.socket.on('message_deleted', (data) => this.emit('message_deleted', data));

    // Typing events
    this.socket.on('user_typing', (data) => this.emit('user_typing', data));

    // Stream events
    this.socket.on('stream_start', (data) => this.emit('stream_start', data));
    this.socket.on('stream_chunk', (data) => this.emit('stream_chunk', data));
    this.socket.on('stream_complete', (data) => this.emit('stream_complete', data));
    this.socket.on('stream_error', (data) => this.emit('stream_error', data));

    // Form events
    this.socket.on('form_prefill', (data) => this.emit('form_prefill', data));
    this.socket.on('form_field_sync', (data) => this.emit('form_field_sync', data));
    this.socket.on('form_validation_result', (data) => this.emit('form_validation_result', data));
    this.socket.on('form_submit_result', (data) => this.emit('form_submit_result', data));

    // Room events
    this.socket.on('room_joined', (data) => this.emit('room_joined', data));
    this.socket.on('room_left', (data) => this.emit('room_left', data));
    this.socket.on('user_joined', (data) => this.emit('user_joined', data));
    this.socket.on('user_left', (data) => this.emit('user_left', data));

    // Error events
    this.socket.on('error', (data) => this.emit('error', data));

    // System events
    this.socket.on('system_notification', (data) => this.emit('system_notification', data));
  }

  /**
   * Destroy the service
   */
  public destroy(): void {
    this.disconnect();
    this.eventListeners.clear();
  }
}

// Singleton instance
let chatWebSocketService: ChatWebSocketService | null = null;

/**
 * Get or create ChatWebSocket service instance
 */
export function getChatWebSocketService(config?: ChatWebSocketConfig): ChatWebSocketService {
  if (!chatWebSocketService && config) {
    chatWebSocketService = new ChatWebSocketService(config);
  }
  
  if (!chatWebSocketService) {
    throw new Error('ChatWebSocket service not initialized. Please provide config.');
  }
  
  return chatWebSocketService;
}

/**
 * Initialize ChatWebSocket service
 */
export function initializeChatWebSocket(config: ChatWebSocketConfig): ChatWebSocketService {
  if (chatWebSocketService) {
    chatWebSocketService.destroy();
  }
  
  chatWebSocketService = new ChatWebSocketService(config);
  return chatWebSocketService;
}

export default ChatWebSocketService;
