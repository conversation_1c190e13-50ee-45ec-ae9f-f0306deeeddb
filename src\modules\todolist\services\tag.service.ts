import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import { AsyncMultiSelectOption } from '@/shared/components/common';

import {
  TagDto,
  TagQueryDto,
  CreateTagDto,
  UpdateTagDto,
  BulkDeleteTagDto,
  BulkDeleteTagResponseDto,
} from '../types/task.types';

/**
 * Service xử lý các API liên quan đến tag
 */
export class TagService {
  private static readonly BASE_URL = '/todolists/tags';

  /**
   * L<PERSON>y danh sách tag có phân trang
   */
  static async getTags(params?: TagQueryDto): Promise<PaginatedResult<TagDto>> {
    const response = await apiClient.get<PaginatedResult<TagDto>>(
      this.BASE_URL,
      { params }
    );
    return response.result;
  }

  /**
   * <PERSON><PERSON><PERSON> tất cả tag không phân trang (dùng cho dropdown, select)
   */
  static async getAllTags(search?: string): Promise<ApiResponseDto<TagDto[]>> {
    const response = await apiClient.get<ApiResponseDto<TagDto[]>>(
      `${this.BASE_URL}/all`,
      { params: { search } }
    );
    return response.result;
  }

  /**
   * Lấy thông tin tag theo ID
   */
  static async getTagById(id: number): Promise<ApiResponseDto<TagDto>> {
    const response = await apiClient.get<ApiResponseDto<TagDto>>(`${this.BASE_URL}/${id}`);
    return response.result;
  }

  /**
   * Tạo tag mới
   */
  static async createTag(data: CreateTagDto): Promise<ApiResponseDto<TagDto>> {
    const response = await apiClient.post<ApiResponseDto<TagDto>>(this.BASE_URL, data);
    return response.result;
  }

  /**
   * Cập nhật tag
   */
  static async updateTag(id: number, data: UpdateTagDto): Promise<ApiResponseDto<TagDto>> {
    const response = await apiClient.patch<ApiResponseDto<TagDto>>(`${this.BASE_URL}/${id}`, data);
    return response.result;
  }

  /**
   * Xóa tag
   */
  static async deleteTag(id: number): Promise<ApiResponseDto<boolean>> {
    const response = await apiClient.delete<ApiResponseDto<boolean>>(`${this.BASE_URL}/${id}`);
    return response.result;
  }

  /**
   * Xóa nhiều tag
   */
  static async bulkDeleteTags(data: BulkDeleteTagDto): Promise<ApiResponseDto<BulkDeleteTagResponseDto>> {
    const response = await apiClient.delete<ApiResponseDto<BulkDeleteTagResponseDto>>(
      this.BASE_URL,
      { data }
    );
    return response.result;
  }

  /**
   * Lấy danh sách tag cho AsyncMultiSelectMenu
   */
  static async getTagsForMultiSelect(params: {
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    items: AsyncMultiSelectOption[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }> {
    const queryParams: TagQueryDto = {
      page: params.page || 1,
      limit: params.limit || 20,
      search: params.search,
    };

    const result = await this.getTags(queryParams);

    return {
      items: result.items.map(tag => ({
        id: tag.id,
        label: tag.name,
        value: tag.id,
        icon: 'tag',
      })),
      totalItems: result.meta.totalItems,
      totalPages: result.meta.totalPages,
      currentPage: result.meta.currentPage,
    };
  }
}
