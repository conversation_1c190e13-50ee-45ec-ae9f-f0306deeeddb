/**
 * Navigation Message Component
 * Renders navigation actions and page redirects
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { Typography, Button, Icon, Modal } from '@/shared/components/common';
import { NavigationContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface NavigationMessageProps {
  data: NavigationContentData;
  className?: string;
}

/**
 * Navigation Message Component
 */
const NavigationMessage: React.FC<NavigationMessageProps> = ({ 
  data, 
  className = '' 
}) => {
  const {
    action,
    url,
    title,
    description,
    target = '_self',
    params = {},
    confirmation,
    icon,
    buttonVariant = 'primary',
  } = data;

  const navigate = useNavigate();
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  // Build URL with parameters
  const buildUrl = () => {
    if (Object.keys(params).length === 0) {
      return url;
    }

    const urlObj = new URL(url, window.location.origin);
    for (const [key, value] of Object.entries(params)) {
      urlObj.searchParams.set(key, String(value));
    }

    return urlObj.toString();
  };

  // Handle navigation action
  const handleNavigation = async () => {
    if (confirmation?.required) {
      setShowConfirmation(true);
      return;
    }

    await executeNavigation();
  };

  // Execute the actual navigation
  const executeNavigation = async () => {
    setIsNavigating(true);
    const finalUrl = buildUrl();

    try {
      switch (action) {
        case 'redirect':
          if (target === '_blank') {
            window.open(finalUrl, '_blank', 'noopener,noreferrer');
          } else {
            // Check if it's an external URL
            if (finalUrl.startsWith('http')) {
              window.location.href = finalUrl;
            } else {
              navigate(finalUrl);
            }
          }
          break;

        case 'open_modal':
          // This would need to be implemented based on your modal system
          console.log('Open modal:', finalUrl);
          // You might want to emit an event or use a modal context
          break;

        case 'open_sidebar':
          // This would need to be implemented based on your sidebar system
          console.log('Open sidebar:', finalUrl);
          // You might want to emit an event or use a sidebar context
          break;

        case 'open_tab':
          window.open(finalUrl, '_blank', 'noopener,noreferrer');
          break;

        case 'download':
          // Create a temporary link for download
          const link = document.createElement('a');
          link.href = finalUrl;
          link.download = title || 'download';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          break;

        default:
          console.warn('Unknown navigation action:', action);
      }
    } catch (error) {
      console.error('Navigation error:', error);
    } finally {
      setIsNavigating(false);
      setShowConfirmation(false);
    }
  };

  // Get action icon
  const getActionIcon = () => {
    if (icon) {return icon;}

    switch (action) {
      case 'redirect':
        return target === '_blank' ? 'external-link' : 'arrow-right';
      case 'open_modal':
        return 'maximize';
      case 'open_sidebar':
        return 'sidebar';
      case 'open_tab':
        return 'external-link';
      case 'download':
        return 'download';
      default:
        return 'arrow-right';
    }
  };

  // Get action label
  const getActionLabel = () => {
    switch (action) {
      case 'redirect':
        return target === '_blank' ? 'Open Link' : 'Go to Page';
      case 'open_modal':
        return 'Open Modal';
      case 'open_sidebar':
        return 'Open Sidebar';
      case 'open_tab':
        return 'Open in New Tab';
      case 'download':
        return 'Download';
      default:
        return 'Navigate';
    }
  };

  // Render confirmation modal
  const renderConfirmationModal = () => {
    if (!confirmation?.required || !showConfirmation) {return null;}

    return (
      <Modal
        isOpen={showConfirmation}
        onClose={() => setShowConfirmation(false)}
        title="Confirm Navigation"
      >
        <div className="p-4">
          <Typography variant="body2" className="mb-4">
            {confirmation.message}
          </Typography>
          
          <div className="flex gap-2 justify-end">
            <Button
              variant="outline"
              onClick={() => setShowConfirmation(false)}
              disabled={isNavigating}
            >
              {confirmation.cancelText || 'Cancel'}
            </Button>
            <Button
              variant="primary"
              onClick={executeNavigation}
              loading={isNavigating}
            >
              {confirmation.confirmText || 'Confirm'}
            </Button>
          </div>
        </div>
      </Modal>
    );
  };

  // Render URL preview
  const renderUrlPreview = () => {
    const finalUrl = buildUrl();
    const displayUrl = finalUrl.length > 50 ? `${finalUrl.substring(0, 47)}...` : finalUrl;

    return (
      <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
        <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
          URL: {displayUrl}
        </Typography>
      </div>
    );
  };

  // Render parameters
  const renderParameters = () => {
    if (Object.keys(params).length === 0) {return null;}

    return (
      <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
        <Typography variant="caption" className="text-gray-600 dark:text-gray-400 block mb-1">
          Parameters:
        </Typography>
        <div className="space-y-1">
          {Object.entries(params).map(([key, value]) => (
            <div key={key} className="flex justify-between">
              <Typography variant="caption" className="text-gray-700 dark:text-gray-300">
                {key}:
              </Typography>
              <Typography variant="caption" className="text-gray-600 dark:text-gray-400">
                {String(value)}
              </Typography>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className={`p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
        {/* Navigation header */}
        <div className="flex items-start justify-between mb-3">
          <div className="flex items-start">
            <Icon name={getActionIcon()} size="sm" className="text-blue-500 mt-1 mr-3" />
            <div>
              <Typography variant="h6" className="font-medium mb-1">
                {title}
              </Typography>
              {description && (
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  {description}
                </Typography>
              )}
            </div>
          </div>
        </div>

        {/* URL preview */}
        {renderUrlPreview()}

        {/* Parameters */}
        {renderParameters()}

        {/* Navigation button */}
        <div className="mt-4">
          <Button
            variant={buttonVariant}
            onClick={handleNavigation}
            loading={isNavigating}
            className="w-full"
          >
            <Icon name={getActionIcon()} size="sm" className="mr-2" />
            {getActionLabel()}
          </Button>
        </div>

        {/* Additional info */}
        <div className="mt-3 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>Action: {action.replace('_', ' ')}</span>
          <span>Target: {target}</span>
        </div>
      </div>

      {/* Confirmation modal */}
      {renderConfirmationModal()}
    </>
  );
};

export default NavigationMessage;
