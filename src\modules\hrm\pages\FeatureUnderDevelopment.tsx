import React from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';

import { 
  Card, 
  Typography, 
  Button, 
  Alert 
} from '@/shared/components/common';

interface FeatureUnderDevelopmentProps {
  title?: string;
  featureName?: string;
}

/**
 * Trang hiển thị thông báo tính năng đang phát triển
 */
const FeatureUnderDevelopment: React.FC<FeatureUnderDevelopmentProps> = ({ 
  title, 
  featureName 
}) => {
  const { t } = useTranslation(['common', 'hrm']);
  const location = useLocation();
  
  // Tự động xác định tên tính năng từ đường dẫn nếu không được cung cấp
  const getFeatureName = () => {
    if (featureName) return featureName;
    
    const path = location.pathname.split('/');
    return path[path.length - 1]
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Tên hiển thị của tính năng
  const displayName = getFeatureName();
  
  return (
    <div className="container mx-auto p-4 md:p-6">
      <div className="w-full">
        <Card className="p-6">
          <Typography variant="h2" className="mb-6">
            {title || `${t('hrm:feature.title', 'Tính năng')}: ${displayName}`}
          </Typography>
          
          <div className="mb-8 p-4 bg-blue-50 dark:bg-blue-900/20 border-l-4 border-blue-500 rounded">
            <Typography variant="h4" className="text-blue-700 dark:text-blue-300 mb-2">
              {t('hrm:feature.underDevelopment', 'Tính năng đang phát triển')}
            </Typography>
            <Typography variant="body1" className="mt-2">
              {t('hrm:feature.comingSoon', 'Chức năng này đang được phát triển và sẽ sớm được ra mắt.')}
            </Typography>
            <Typography variant="body1" className="mt-2">
              {t('hrm:feature.checkBackLater', 'Vui lòng quay lại sau để trải nghiệm tính năng mới.')}
            </Typography>
          </div>
          
          <div className="flex flex-col md:flex-row gap-4">
            <Button 
              variant="primary" 
              onClick={() => window.history.back()}
            >
              {t('common:back', 'Quay lại')}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/hrm'}
            >
              {t('hrm:goToHrm', 'Về trang Nhân sự')}
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FeatureUnderDevelopment; 