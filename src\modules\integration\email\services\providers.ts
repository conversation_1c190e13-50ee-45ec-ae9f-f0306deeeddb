import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

import { EmailServerTestResult } from '../types';
import {
  EmailProvider,
  ProviderConfigurationData,
  ProviderValidationResult,
  ProviderTestConnectionData,
} from '../types/providers';

/**
 * Email Provider Service
 * Handles provider-related API calls
 */
export class EmailProviderService {
  private static readonly BASE_URL = '/user/integration/email-provider';

  /**
   * L<PERSON>y danh sách tất cả email providers
   */
  static async getProviders(): Promise<ApiResponseDto<EmailProvider[]>> {
    return apiClient.get(`${this.BASE_URL}/list`);
  }

  /**
   * L<PERSON>y thông tin chi tiết của một provider
   */
  static async getProvider(providerId: string): Promise<ApiResponseDto<EmailProvider>> {
    return apiClient.get(`${this.BASE_URL}/${providerId}`);
  }

  /**
   * Validate cấu hình provider
   */
  static async validateProviderConfiguration(
    data: ProviderConfigurationData
  ): Promise<ApiResponseDto<ProviderValidationResult>> {
    return apiClient.post(`${this.BASE_URL}/validate`, data);
  }

  /**
   * Test kết nối với provider configuration
   */
  static async testProviderConnection(
    data: ProviderTestConnectionData
  ): Promise<ApiResponseDto<EmailServerTestResult>> {
    return apiClient.post(`${this.BASE_URL}/test-connection`, data);
  }

  /**
   * Lấy OAuth authorization URL cho provider
   */
  static async getOAuthAuthorizationUrl(
    providerId: string,
    redirectUri: string
  ): Promise<ApiResponseDto<{ authorizationUrl: string; state: string }>> {
    return apiClient.post(`${this.BASE_URL}/${providerId}/oauth/authorize`, {
      redirectUri,
    });
  }

  /**
   * Exchange OAuth authorization code for tokens
   */
  static async exchangeOAuthCode(
    providerId: string,
    code: string,
    state: string
  ): Promise<ApiResponseDto<{
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
    tokenType: string;
    scope?: string;
  }>> {
    return apiClient.post(`${this.BASE_URL}/${providerId}/oauth/exchange`, {
      code,
      state,
    });
  }

  /**
   * Refresh OAuth tokens
   */
  static async refreshOAuthTokens(
    providerId: string,
    refreshToken: string
  ): Promise<ApiResponseDto<{
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
    tokenType: string;
  }>> {
    return apiClient.post(`${this.BASE_URL}/${providerId}/oauth/refresh`, {
      refreshToken,
    });
  }

  /**
   * Lấy setup guide cho provider
   */
  static async getSetupGuide(providerId: string): Promise<ApiResponseDto<{
    title: string;
    description: string;
    steps: Array<{
      title: string;
      description: string;
      imageUrl?: string;
      code?: string;
      tips?: string[];
    }>;
    documentationUrl: string;
    troubleshootingUrl?: string;
  }>> {
    return apiClient.get(`${this.BASE_URL}/${providerId}/setup-guide`);
  }

  /**
   * Lấy provider regions (cho AWS SES, etc.)
   */
  static async getProviderRegions(providerId: string): Promise<ApiResponseDto<Array<{
    id: string;
    name: string;
    endpoint: string;
    isDefault: boolean;
  }>>> {
    return apiClient.get(`${this.BASE_URL}/${providerId}/regions`);
  }

  /**
   * Verify domain cho provider (SendGrid, Mailgun, etc.)
   */
  static async verifyDomain(
    providerId: string,
    domain: string,
    apiKey: string
  ): Promise<ApiResponseDto<{
    isVerified: boolean;
    verificationStatus: string;
    dnsRecords?: Array<{
      type: string;
      name: string;
      value: string;
      priority?: number;
    }>;
  }>> {
    return apiClient.post(`${this.BASE_URL}/${providerId}/verify-domain`, {
      domain,
      apiKey,
    });
  }

  /**
   * Lấy thống kê usage cho provider
   */
  static async getProviderUsage(
    providerId: string,
    apiKey: string,
    period: 'day' | 'week' | 'month' = 'month'
  ): Promise<ApiResponseDto<{
    emailsSent: number;
    emailsDelivered: number;
    emailsBounced: number;
    emailsComplained: number;
    quota?: {
      limit: number;
      used: number;
      remaining: number;
    };
  }>> {
    return apiClient.post(`${this.BASE_URL}/${providerId}/usage`, {
      apiKey,
      period,
    });
  }
}

/**
 * Mock Provider Service for Development
 * Provides mock data when backend is not available
 */
export class MockEmailProviderService {
  /**
   * Mock validate provider configuration
   */
  static async validateProviderConfiguration(
    data: ProviderConfigurationData
  ): Promise<ProviderValidationResult> {
    // Simulate validation delay
    await new Promise(resolve => setTimeout(resolve, 1000));

    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    // Basic validation
    if (!data.credentials.username) {
      errors.push('Username is required');
    }

    if (data.authMethod === 'password' && !data.credentials.password) {
      errors.push('Password is required for password authentication');
    }

    if (data.authMethod === 'apikey' && !data.credentials.apiKey) {
      errors.push('API Key is required for API key authentication');
    }

    // Provider-specific validation
    if (data.providerId === 'gmail' && data.credentials.username) {
      if (!data.credentials.username.endsWith('@gmail.com')) {
        warnings.push('Gmail usually requires @gmail.com email addresses');
      }
    }

    if (data.providerId === 'sendgrid' && data.credentials.apiKey) {
      if (!data.credentials.apiKey.startsWith('SG.')) {
        errors.push('SendGrid API keys should start with "SG."');
      }
    }

    // Suggestions
    if (data.providerId === 'gmail') {
      suggestions.push('Consider using App Passwords instead of your regular password');
      suggestions.push('Enable 2-Factor Authentication for better security');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
    };
  }

  /**
   * Mock test provider connection
   */
  static async testProviderConnection(
    data: ProviderTestConnectionData
  ): Promise<EmailServerTestResult> {
    // Simulate connection test delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock success/failure based on provider and configuration
    const successRate = data.providerId === 'gmail' ? 0.8 : 0.9;
    const isSuccess = Math.random() < successRate;

    if (isSuccess) {
      return {
        success: true,
        message: `Successfully connected to ${data.providerId}. Test email sent to ${data.testEmail.recipientEmail}`,
        details: {
          responseTime: Math.floor(Math.random() * 2000) + 500,
          serverResponse: 'OK',
          statusCode: 200,
        },
      };
    } else {
      const errorMessages = [
        'Authentication failed. Please check your credentials.',
        'Connection timeout. Please check your network connection.',
        'Invalid API key. Please verify your API key is correct.',
        'SMTP server rejected the connection.',
      ];

      return {
        success: false,
        message: errorMessages[Math.floor(Math.random() * errorMessages.length)],
        details: {
          error: 'Connection failed',
          statusCode: 401,
          responseTime: Math.floor(Math.random() * 1000) + 200,
        },
      };
    }
  }
}
