import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Loading,
} from '@/shared/components/common';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { zodResolver } from '@hookform/resolvers/zod';

import { useUpdatePhysicalWarehouse, usePhysicalWarehouse } from '../../hooks/usePhysicalWarehouseQuery';
import { updatePhysicalWarehouseSchema, UpdatePhysicalWarehouseFormValues } from '../../schemas/physical-warehouse.schema';
import { UpdatePhysicalWarehouseDto } from '../../types/physical-warehouse.types';

interface EditPhysicalWarehouseFormProps {
  warehouseId: number;
  initialData?: Partial<UpdatePhysicalWarehouseFormValues>;
  onSubmit?: (data: UpdatePhysicalWarehouseFormValues) => void;
  onCancel?: () => void;
}

/**
 * Component form chỉnh sửa kho vật lý
 */
const EditPhysicalWarehouseForm: React.FC<EditPhysicalWarehouseFormProps> = ({
  warehouseId,
  initialData,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const notification = useSmartNotification();

  // Lấy thông tin kho vật lý hiện tại
  const { data: physicalWarehouseData, isLoading: isLoadingWarehouse } = usePhysicalWarehouse(warehouseId);

  // Mutation để cập nhật kho vật lý
  const { mutateAsync: updatePhysicalWarehouse, isPending: isUpdating } = useUpdatePhysicalWarehouse();

  // Khởi tạo form
  const form = useForm<UpdatePhysicalWarehouseFormValues>({
    resolver: zodResolver(updatePhysicalWarehouseSchema),
    defaultValues: {
      address: initialData?.address || '',
      capacity: initialData?.capacity || undefined,
    },
  });

  // Cập nhật form khi có dữ liệu từ API
  useEffect(() => {
    if (physicalWarehouseData?.result) {
      const warehouse = physicalWarehouseData.result;
      form.reset({
        address: warehouse.address || '',
        capacity: warehouse.capacity || undefined,
      });
    }
  }, [physicalWarehouseData, form]);

  // Xử lý submit form
  const handleSubmit = async (values: UpdatePhysicalWarehouseFormValues) => {
    try {
      // Cập nhật kho vật lý
      await updatePhysicalWarehouse({
        id: warehouseId,
        data: values as UpdatePhysicalWarehouseDto,
      });
      notification.success({ message: t('business:physicalWarehouse.updateSuccess') });

      // Gọi callback onSubmit nếu có
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      console.error('Error updating physical warehouse:', error);
      notification.error({
        message: t('business:physicalWarehouse.updateError')
      });
    }
  };

  if (isLoadingWarehouse) {
    return (
      <Card title={t('business:physicalWarehouse.edit')}>
        <div className="flex justify-center items-center p-8">
          <Loading />
        </div>
      </Card>
    );
  }

  return (
    <Card title={t('business:physicalWarehouse.edit')}>
      <Form
        schema={updatePhysicalWarehouseSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 gap-4">
          <FormItem
            name="address"
            label={t('business:physicalWarehouse.address')}
          >
            <Input
              fullWidth
              placeholder={t('business:physicalWarehouse.form.addressPlaceholder')}
              {...form.register('address')}
            />
          </FormItem>

          <FormItem
            name="capacity"
            label={t('business:physicalWarehouse.capacity')}
          >
            <Input
              type="number"
              fullWidth
              placeholder={t('business:physicalWarehouse.form.capacityPlaceholder')}
              {...form.register('capacity', { valueAsNumber: true })}
            />
          </FormItem>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isUpdating}
          >
            {t('common:update')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditPhysicalWarehouseForm;
