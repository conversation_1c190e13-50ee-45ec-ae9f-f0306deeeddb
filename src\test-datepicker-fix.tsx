import React, { useState } from 'react';

import { DatePicker, DatePickerFormField } from '@/shared/components/common';

/**
 * Test component để kiểm tra DatePicker sau khi sửa lỗi "Invalid time value"
 */
const TestDatePickerFix: React.FC = () => {
  const [basicDate, setBasicDate] = useState<Date | null>(null);
  const [formDate, setFormDate] = useState<string>('');
  const [invalidDate, setInvalidDate] = useState<Date | null>(new Date('invalid'));
  const [nullDate, setNullDate] = useState<Date | null>(null);

  // Test với các giá trị date khác nhau
  const testDates = [
    { label: 'Valid Date', value: new Date('2024-01-15') },
    { label: 'Invalid Date', value: new Date('invalid') },
    { label: 'Null Date', value: null },
    { label: 'Empty String Date', value: new Date('') },
  ];

  // <PERSON>àm xử lý khi thay đổi ngày (read-only cho testing)
  const handleReadOnlyChange = (): void => {
    // TODO: Implement read-only change handler n<PERSON><PERSON> c<PERSON><PERSON>
  };

  return (
    <div className="p-8 space-y-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test DatePicker - Fix "Invalid time value" Error</h1>

      {/* Test Basic DatePicker */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">1. Basic DatePicker</h2>
        <DatePicker
          label="Ngày cơ bản"
          value={basicDate}
          onChange={setBasicDate}
          placeholder="dd/MM/yyyy"
          fullWidth
        />
        <div className="text-sm text-gray-600">
          <p>
            <strong>Selected Date:</strong> {basicDate ? basicDate.toLocaleDateString('vi-VN') : 'Chưa chọn'}
          </p>
          <p>
            <strong>Is Valid:</strong> {basicDate ? (!isNaN(basicDate.getTime()) ? 'Yes' : 'No') : 'N/A'}
          </p>
        </div>
      </div>

      {/* Test DatePickerFormField */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">2. DatePickerFormField (String value)</h2>
        <DatePickerFormField
          label="Ngày form field"
          value={formDate}
          onChange={setFormDate}
          placeholder="dd/MM/yyyy"
          fullWidth
        />
        <div className="text-sm text-gray-600">
          <p>
            <strong>Form Value (string):</strong> {formDate || 'Chưa chọn'}
          </p>
          <p>
            <strong>Parsed Date:</strong>{' '}
            {formDate ? (() => {
              try {
                const date = new Date(formDate);
                return !isNaN(date.getTime()) ? date.toLocaleDateString('vi-VN') : 'Invalid';
              } catch {
                return 'Error';
              }
            })() : 'N/A'}
          </p>
        </div>
      </div>

      {/* Test Invalid Date Handling */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">3. Invalid Date Handling</h2>
        <DatePicker
          label="Invalid Date Test"
          value={invalidDate}
          onChange={setInvalidDate}
          placeholder="dd/MM/yyyy"
          fullWidth
        />
        <div className="text-sm text-gray-600">
          <p>
            <strong>Invalid Date Value:</strong> {invalidDate ? invalidDate.toString() : 'null'}
          </p>
          <p>
            <strong>Is Valid:</strong> {invalidDate ? (!isNaN(invalidDate.getTime()) ? 'Yes' : 'No') : 'N/A'}
          </p>
        </div>
      </div>

      {/* Test Null Date */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">4. Null Date Test</h2>
        <DatePicker
          label="Null Date Test"
          value={nullDate}
          onChange={setNullDate}
          placeholder="dd/MM/yyyy"
          fullWidth
        />
        <div className="text-sm text-gray-600">
          <p>
            <strong>Null Date Value:</strong> {nullDate ? nullDate.toString() : 'null'}
          </p>
        </div>
      </div>

      {/* Test Multiple Date Scenarios */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">5. Multiple Date Scenarios</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {testDates.map((testDate, index) => (
            <div key={index} className="p-4 border rounded-lg">
              <h3 className="font-medium mb-2">{testDate.label}</h3>
              <DatePicker
                label={testDate.label}
                value={testDate.value}
                onChange={handleReadOnlyChange} // Read-only for testing
                placeholder="dd/MM/yyyy"
                fullWidth
              />
              <div className="text-xs text-gray-500 mt-2">
                <p>Value: {testDate.value ? testDate.value.toString() : 'null'}</p>
                <p>Valid: {testDate.value ? (!isNaN(testDate.value.getTime()) ? 'Yes' : 'No') : 'N/A'}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Test Actions */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">6. Test Actions</h2>
        <div className="flex flex-wrap gap-2">
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            onClick={() => setBasicDate(new Date())}
          >
            Set Today
          </button>
          <button
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            onClick={() => setBasicDate(new Date('invalid'))}
          >
            Set Invalid Date
          </button>
          <button
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            onClick={() => setBasicDate(null)}
          >
            Set Null
          </button>
          <button
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
            onClick={() => setFormDate('2024-12-25')}
          >
            Set Form Date
          </button>
          <button
            className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
            onClick={() => setFormDate('invalid-date')}
          >
            Set Invalid Form Date
          </button>
        </div>
      </div>

      {/* Console Log Test */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">7. Console Test</h2>
        <p className="text-sm text-gray-600">
          Check the browser console for any errors when interacting with the DatePickers above.
          The fix should prevent "Invalid time value" errors from appearing.
        </p>
      </div>
    </div>
  );
};

export default TestDatePickerFix;
