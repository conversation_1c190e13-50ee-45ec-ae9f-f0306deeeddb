import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { DOCUMENT_QUERY_KEYS } from '../constants';
import * as DocumentService from '../services/document.service';
import {
  DocumentQueryDto,
  DocumentSearchDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateUploadUrlDto,
  ConfirmUploadDto,
} from '../types/document.types';

/**
 * Hook để lấy danh sách tài liệu
 */
export const useDocuments = (params?: DocumentQueryDto) => {
  return useQuery({
    queryKey: DOCUMENT_QUERY_KEYS.LIST(params || {}),
    queryFn: () => DocumentService.getDocumentsWithBusinessLogic(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để tìm kiếm tài liệu
 */
export const useSearchDocuments = (params: DocumentSearchDto) => {
  return useQuery({
    queryKey: DOCUMENT_QUERY_KEYS.SEARCH(params),
    queryFn: () => DocumentService.searchDocumentsWithBusinessLogic(params),
    enabled: !!params.searchText && params.searchText.trim().length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết tài liệu
 */
export const useDocument = (id: number) => {
  return useQuery({
    queryKey: DOCUMENT_QUERY_KEYS.DETAIL(id),
    queryFn: () => DocumentService.getDocumentByIdWithBusinessLogic(id),
    enabled: !!id && id > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để lấy URL download tài liệu
 */
export const useDocumentDownloadUrl = (id: number) => {
  return useQuery({
    queryKey: DOCUMENT_QUERY_KEYS.DOWNLOAD_URL(id),
    queryFn: () => DocumentService.getDocumentDownloadUrlWithBusinessLogic(id),
    enabled: !!id && id > 0,
    staleTime: 0, // Always fresh
    gcTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook để lấy thống kê tài liệu
 */
export const useDocumentStats = () => {
  return useQuery({
    queryKey: DOCUMENT_QUERY_KEYS.STATS(),
    queryFn: () => DocumentService.getDocumentStatsWithBusinessLogic(),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook để tạo tài liệu mới
 */
export const useCreateDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateDocumentDto) =>
      DocumentService.createDocumentWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate and refetch documents list
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để cập nhật tài liệu
 */
export const useUpdateDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateDocumentDto }) =>
      DocumentService.updateDocumentWithBusinessLogic(id, data),
    onSuccess: (updatedDocument) => {
      // Update specific document in cache
      queryClient.setQueryData(
        DOCUMENT_QUERY_KEYS.DETAIL(updatedDocument.id),
        updatedDocument
      );

      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để xóa tài liệu
 */
export const useDeleteDocument = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => DocumentService.deleteDocumentWithBusinessLogic(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({
        queryKey: DOCUMENT_QUERY_KEYS.DETAIL(deletedId),
      });

      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để xóa nhiều tài liệu
 */
export const useBulkDeleteDocuments = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) =>
      DocumentService.bulkDeleteDocumentsWithBusinessLogic(ids),
    onSuccess: (_, deletedIds) => {
      // Remove from cache
      for (const id of deletedIds) {
        queryClient.removeQueries({
          queryKey: DOCUMENT_QUERY_KEYS.DETAIL(id),
        });
      }

      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_QUERY_KEYS.ALL,
      });
    },
  });
};

/**
 * Hook để tạo URL upload
 */
export const useCreateUploadUrl = () => {
  return useMutation({
    mutationFn: (data: CreateUploadUrlDto) =>
      DocumentService.createUploadUrlWithBusinessLogic(data),
  });
};

/**
 * Hook để xác nhận upload
 */
export const useConfirmUpload = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ConfirmUploadDto) =>
      DocumentService.confirmUploadWithBusinessLogic(data),
    onSuccess: () => {
      // Invalidate documents list
      queryClient.invalidateQueries({
        queryKey: DOCUMENT_QUERY_KEYS.ALL,
      });
    },
  });
};
