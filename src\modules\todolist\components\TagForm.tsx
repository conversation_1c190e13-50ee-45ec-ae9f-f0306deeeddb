import React, { useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Typography,
  Form,
  FormItem,
  Input,
  Button,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/useFormErrors';
import { NotificationUtil } from '@/shared/utils/notification';

import { useCreateTag } from '../hooks/useTags';
import { CreateTagDto } from '../types/task.types';

// Interface cho tag form data
interface TagFormData {
  name: string;
}

interface TagFormProps {
  /**
   * Callback khi submit thành công
   */
  onSubmit: () => void;

  /**
   * Callback khi hủy form
   */
  onCancel: () => void;

  /**
   * Tiêu đề form (optional)
   */
  title?: string;
}

/**
 * Component form tạo tag mới
 */
const TagForm: React.FC<TagFormProps> = ({
  onSubmit,
  onCancel,
  title,
}) => {
  const { t } = useTranslation(['common', 'todolist']);
  
  // Form state
  const [tagFormData, setTagFormData] = useState<TagFormData>({ name: '' });
  
  // Form errors
  const { formRef, setFormErrors } = useFormErrors<TagFormData>();
  
  // Create tag mutation
  const createTagMutation = useCreateTag();

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof TagFormData, value: string) => {
    setTagFormData(prev => ({ ...prev, [field]: value }));
  };

  // Xử lý submit form
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    // Validation
    const errors: Partial<TagFormData> = {};
    if (!tagFormData.name.trim()) {
      errors.name = t('todolist:tag.validation.nameRequired', 'Tên tag là bắt buộc');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    // Submit
    const createData: CreateTagDto = {
      name: tagFormData.name.trim(),
    };

    createTagMutation.mutate(createData, {
      onSuccess: () => {
        NotificationUtil.success({
          message: t('todolist:tag.createSuccess', 'Tạo tag thành công'),
        });
        setTagFormData({ name: '' });
        onSubmit();
      },
      onError: error => {
        console.error('Error creating tag:', error);
        NotificationUtil.error({
          message: t('todolist:tag.createError', 'Tạo tag thất bại'),
        });
      },
    });
  };

  // Xử lý hủy
  const handleCancel = () => {
    setTagFormData({ name: '' });
    onCancel();
  };

  return (
    <div className="p-6">
      <Typography variant="h3" className="mb-4">
        {title || t('todolist:tag.add', 'Thêm Tag')}
      </Typography>
      
      <Form 
        ref={formRef as unknown as React.RefObject<any>} 
        onSubmit={handleSubmit as unknown as any}
      >
        <FormItem 
          label={t('todolist:tag.form.name')} 
          name="name" 
          required
        >
          <Input
            type="text"
            value={tagFormData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder={t('todolist:tag.form.namePlaceholder', 'Nhập tên tag')}
          />
        </FormItem>
        
        <div className="flex justify-end space-x-2 mt-4">
          <Button 
            type="button" 
            variant="secondary" 
            onClick={handleCancel}
            disabled={createTagMutation.isPending}
          >
            {t('common:cancel')}
          </Button>
          <Button 
            type="submit" 
            variant="primary"
            disabled={createTagMutation.isPending}
          >
            {createTagMutation.isPending 
              ? t('common:saving', 'Đang lưu...') 
              : t('common:save')
            }
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default TagForm;
