import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Typography,
  Input,
  Button,
  ResponsiveGrid,
  Card,
  Icon,
  Chip
} from '@/shared/components/common';

import { ALL_EMAIL_PROVIDERS, PROVIDER_CATEGORIES, AUTH_METHODS } from '../../constants/providers';
import { EmailProvider, EmailProviderCategory, ProviderAuthMethod } from '../../types/providers';
import ProviderCard from '../provider/ProviderCard';

interface ProviderSelectionStepProps {
  selectedProvider?: EmailProvider;
  onProviderSelect: (provider: EmailProvider) => void;
  onNext: () => void;
  onBack?: () => void;
}

/**
 * ProviderSelectionStep Component
 * Bước chọn email provider trong wizard
 */
const ProviderSelectionStep: React.FC<ProviderSelectionStepProps> = ({
  selectedProvider,
  onProviderSelect,
  onNext,
  onBack,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  
  // State for filters
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<EmailProviderCategory | 'all'>('all');
  const [selectedAuthMethod, setSelectedAuthMethod] = useState<ProviderAuthMethod | 'all'>('all');
  const [showPopularOnly, setShowPopularOnly] = useState(false);

  // Filtered providers
  const filteredProviders = useMemo(() => {
    return ALL_EMAIL_PROVIDERS.filter(provider => {
      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          provider.displayName.toLowerCase().includes(query) ||
          provider.description.toLowerCase().includes(query) ||
          provider.features.some(feature => feature.toLowerCase().includes(query));
        
        if (!matchesSearch) {return false;}
      }

      // Category filter
      if (selectedCategory !== 'all' && provider.category !== selectedCategory) {
        return false;
      }

      // Auth method filter
      if (selectedAuthMethod !== 'all' && !provider.authMethods.includes(selectedAuthMethod)) {
        return false;
      }

      // Popular filter
      if (showPopularOnly && !provider.isPopular) {
        return false;
      }

      return provider.isActive;
    });
  }, [searchQuery, selectedCategory, selectedAuthMethod, showPopularOnly]);

  // Popular providers for quick selection
  const popularProviders = useMemo(() => {
    return ALL_EMAIL_PROVIDERS.filter(provider => provider.isPopular && provider.isActive);
  }, []);

  const handleProviderClick = (provider: EmailProvider) => {
    onProviderSelect(provider);
  };

  const handleNext = () => {
    if (selectedProvider) {
      onNext();
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-6">
        <Typography variant="h4" className="mb-2">
          {t('integration:wizard.selectProvider.title')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('integration:wizard.selectProvider.description')}
        </Typography>
      </div>

      {/* Quick Selection - Popular Providers */}
      <Card className="mb-6">
        <div className="p-4">
          <div className="flex items-center space-x-2 mb-3">
            <Icon name="star" className="w-5 h-5 text-warning" />
            <Typography variant="h6">
              {t('integration:wizard.selectProvider.popular')}
            </Typography>
          </div>
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
            {popularProviders.map(provider => (
              <ProviderCard
                key={provider.id}
                provider={provider}
                isSelected={selectedProvider?.id === provider.id}
                onClick={handleProviderClick}
                className="h-full"
              />
            ))}
          </ResponsiveGrid>
        </div>
      </Card>

      {/* Filters */}
      <Card className="mb-6">
        <div className="p-4">
          <Typography variant="h6" className="mb-4">
            {t('integration:wizard.selectProvider.filters')}
          </Typography>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Search */}
            <div>
              <Typography variant="caption" className="mb-2">
                {t('common:search')}
              </Typography>
              <Input
                placeholder={t('integration:wizard.selectProvider.searchPlaceholder')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon="search"
                fullWidth
              />
            </div>

            {/* Category Filter */}
            <div>
              <Typography variant="caption" className="mb-2">
                {t('integration:provider.category.title')}
              </Typography>
              <div className="flex flex-wrap gap-2">
                <Chip
                  variant={selectedCategory === 'all' ? 'primary' : 'default'}
                  outlined={selectedCategory !== 'all'}
                  className="cursor-pointer"
                  onClick={() => setSelectedCategory('all')}
                >
                  {t('common:all')}
                </Chip>
                {PROVIDER_CATEGORIES.map(category => (
                  <Chip
                    key={category.value}
                    variant={selectedCategory === category.value ? 'primary' : 'default'}
                    outlined={selectedCategory !== category.value}
                    className="cursor-pointer"
                    onClick={() => setSelectedCategory(category.value as EmailProviderCategory)}
                    leftIconName={category.icon}
                  >
                    {category.label}
                  </Chip>
                ))}
              </div>
            </div>

            {/* Auth Method Filter */}
            <div>
              <Typography variant="caption" className="mb-2">
                {t('integration:provider.authMethod')}
              </Typography>
              <div className="flex flex-wrap gap-2">
                <Chip
                  variant={selectedAuthMethod === 'all' ? 'primary' : 'default'}
                  outlined={selectedAuthMethod !== 'all'}
                  className="cursor-pointer"
                  onClick={() => setSelectedAuthMethod('all')}
                >
                  {t('common:all')}
                </Chip>
                {AUTH_METHODS.map(method => (
                  <Chip
                    key={method.value}
                    variant={selectedAuthMethod === method.value ? 'primary' : 'default'}
                    outlined={selectedAuthMethod !== method.value}
                    className="cursor-pointer"
                    onClick={() => setSelectedAuthMethod(method.value as ProviderAuthMethod)}
                    leftIconName={method.icon}
                  >
                    {method.label}
                  </Chip>
                ))}
              </div>
            </div>

            {/* Popular Only Toggle */}
            <div>
              <Typography variant="caption" className="mb-2">
                {t('integration:wizard.selectProvider.options')}
              </Typography>
              <Chip
                variant={showPopularOnly ? 'primary' : 'default'}
                outlined={!showPopularOnly}
                className="cursor-pointer"
                onClick={() => setShowPopularOnly(!showPopularOnly)}
                leftIconName="star"
              >
                {t('integration:provider.popularOnly')}
              </Chip>
            </div>
          </div>
        </div>
      </Card>

      {/* All Providers Grid */}
      <Card className="mb-6">
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6">
              {t('integration:wizard.selectProvider.allProviders')}
            </Typography>
            <Typography variant="caption" className="text-muted-foreground">
              {filteredProviders.length} {t('integration:provider.found')}
            </Typography>
          </div>

          {filteredProviders.length > 0 ? (
            <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4 }}>
              {filteredProviders.map(provider => (
                <ProviderCard
                  key={provider.id}
                  provider={provider}
                  isSelected={selectedProvider?.id === provider.id}
                  onClick={handleProviderClick}
                  className="h-full"
                />
              ))}
            </ResponsiveGrid>
          ) : (
            <div className="text-center py-8">
              <Icon name="search" className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <Typography variant="h6" className="text-muted-foreground mb-2">
                {t('integration:wizard.selectProvider.noResults')}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {t('integration:wizard.selectProvider.tryDifferentFilters')}
              </Typography>
            </div>
          )}
        </div>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        {onBack && (
          <Button variant="outline" onClick={onBack}>
            <Icon name="arrow-left" className="w-4 h-4 mr-2" />
            {t('common:back')}
          </Button>
        )}
        
        <div className="ml-auto">
          <Button
            variant="primary"
            onClick={handleNext}
            disabled={!selectedProvider}
          >
            {t('common:next')}
            <Icon name="arrow-right" className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>

      {/* Selected Provider Info */}
      {selectedProvider && (
        <Card className="mt-4 border-primary">
          <div className="p-4">
            <Typography variant="h6" className="mb-2">
              {t('integration:wizard.selectProvider.selected')}
            </Typography>
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 flex items-center justify-center bg-gray-100 rounded">
                {selectedProvider.logoUrl ? (
                  <img
                    src={selectedProvider.logoUrl}
                    alt={selectedProvider.displayName}
                    className="w-6 h-6 object-contain"
                  />
                ) : (
                  <Icon name="mail" className="w-4 h-4 text-gray-500" />
                )}
              </div>
              <div>
                <Typography variant="body1" className="font-medium">
                  {selectedProvider.displayName}
                </Typography>
                <Typography variant="caption" className="text-muted-foreground">
                  {selectedProvider.description}
                </Typography>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ProviderSelectionStep;
