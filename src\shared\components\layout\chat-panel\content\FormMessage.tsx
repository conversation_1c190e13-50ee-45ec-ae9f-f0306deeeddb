/**
 * Form Message Component
 * Renders interactive forms in chat messages with real-time sync
 */

import React, { useState, useCallback } from 'react';

import { 
  Form, 
  FormItem, 
  Input, 
  Button, 
  Typography, 
  Card,
  Select,
  Textarea,
  DatePicker
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/useFormErrors';
import { useFormSync } from '@/shared/websocket/hooks/useFormSync';
import { FormContentData, FormField } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface FormMessageProps {
  data: FormContentData;
  messageId: string;
  roomId: string;
  className?: string;
}

/**
 * Form Message Component
 */
const FormMessage: React.FC<FormMessageProps> = ({ 
  data, 
  messageId, 
  roomId, 
  className = '' 
}) => {
  const {
    formId,
    title,
    description,
    schema,
    prefillData = {},
    validation = {},
    styling = {}
  } = data;

  // Form sync hook
  const {
    formData,
    updateField,
    submitForm,
    isValid,
    isDirty,
    isSubmitting,
    isConnected,
    errors,
  } = useFormSync({
    formId,
    messageId,
    roomId,
    schema,
    initialData: prefillData,
    enableRealTimeSync: true,
    enableValidation: validation.realtime,
  });

  // Form errors hook for additional error handling
  const { formRef, setFormErrors } = useFormErrors();

  // Local state
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  // Handle form submission
  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    
    try {
      setSubmitStatus('idle');
      const result = await submitForm();
      
      if (result.success) {
        setSubmitStatus('success');
      } else {
        setSubmitStatus('error');
        if (result.validationErrors) {
          setFormErrors(result.validationErrors);
        }
      }
    } catch (error) {
      setSubmitStatus('error');
      console.error('Form submission error:', error);
    }
  }, [submitForm, setFormErrors]);

  // Render form field based on type
  const renderFormField = (fieldName: string, field: FormField) => {
    const value = formData[fieldName];
    const error = errors[fieldName];
    const isRequired = field.required || schema.required?.includes(fieldName);

    const commonProps = {
      value: value || field.defaultValue || '',
      onChange: (newValue: unknown) => updateField(fieldName, newValue),
      error,
      disabled: isSubmitting,
      placeholder: field.placeholder,
    };

    switch (field.type) {
      case 'string':
        return (
          <Input
            type="text"
            {...commonProps}
            onChange={(e) => updateField(fieldName, e.target.value)}
          />
        );

      case 'number':
        return (
          <Input
            type="number"
            {...commonProps}
            min={field.validation?.min}
            max={field.validation?.max}
            onChange={(e) => updateField(fieldName, parseFloat(e.target.value) || 0)}
          />
        );

      case 'textarea':
        return (
          <Textarea
            {...commonProps}
            rows={4}
            maxLength={field.validation?.maxLength}
            onChange={(e) => updateField(fieldName, e.target.value)}
          />
        );

      case 'select':
        return (
          <Select
            {...commonProps}
            options={field.options || []}
            onChange={(selectedValue) => updateField(fieldName, selectedValue)}
          />
        );

      case 'multiselect':
        return (
          <Select
            {...commonProps}
            multiple
            options={field.options || []}
            onChange={(selectedValues) => updateField(fieldName, selectedValues)}
          />
        );

      case 'date':
        return (
          <DatePicker
            {...commonProps}
            onChange={(date) => updateField(fieldName, date)}
          />
        );

      case 'datetime':
        return (
          <DatePicker
            {...commonProps}
            showTime
            onChange={(datetime) => updateField(fieldName, datetime)}
          />
        );

      case 'boolean':
        return (
          <div className="flex items-center">
            <input
              type="checkbox"
              checked={Boolean(value)}
              onChange={(e) => updateField(fieldName, e.target.checked)}
              disabled={isSubmitting}
              className="mr-2"
            />
            <Typography variant="body2">{field.title}</Typography>
          </div>
        );

      case 'file':
        return (
          <Input
            type="file"
            onChange={(e) => {
              const file = e.target.files?.[0];
              if (file) {
                updateField(fieldName, file);
              }
            }}
            disabled={isSubmitting}
          />
        );

      default:
        return (
          <Input
            type="text"
            {...commonProps}
            onChange={(e) => updateField(fieldName, e.target.value)}
          />
        );
    }
  };

  // Connection status indicator
  const renderConnectionStatus = () => {
    if (isConnected) {return null;}

    return (
      <div className="mb-4 p-2 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
        <Typography variant="caption" className="text-yellow-700 dark:text-yellow-300">
          ⚠️ Not connected to server. Form data may not sync in real-time.
        </Typography>
      </div>
    );
  };

  // Submit status indicator
  const renderSubmitStatus = () => {
    if (submitStatus === 'idle') {return null;}

    const statusConfig = {
      success: {
        bg: 'bg-green-50 dark:bg-green-900/20',
        border: 'border-green-200 dark:border-green-800',
        text: 'text-green-700 dark:text-green-300',
        message: '✅ Form submitted successfully!',
      },
      error: {
        bg: 'bg-red-50 dark:bg-red-900/20',
        border: 'border-red-200 dark:border-red-800',
        text: 'text-red-700 dark:text-red-300',
        message: '❌ Form submission failed. Please try again.',
      },
    };

    const config = statusConfig[submitStatus];

    return (
      <div className={`mb-4 p-2 ${config.bg} border ${config.border} rounded-lg`}>
        <Typography variant="caption" className={config.text}>
          {config.message}
        </Typography>
      </div>
    );
  };

  return (
    <Card className={`p-4 max-w-md ${className}`}>
      {/* Form header */}
      <div className="mb-4">
        <Typography variant="h6" className="font-semibold mb-2">
          {title}
        </Typography>
        {description && (
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            {description}
          </Typography>
        )}
      </div>

      {/* Connection status */}
      {renderConnectionStatus()}

      {/* Submit status */}
      {renderSubmitStatus()}

      {/* Form */}
      <Form
        ref={formRef}
        onSubmit={handleSubmit}
        className="space-y-4"
      >
        {/* Form fields */}
        {Object.entries(schema.properties).map(([fieldName, field]) => (
          <FormItem
            key={fieldName}
            name={fieldName}
            label={field.title}
            required={field.required || schema.required?.includes(fieldName)}
            helpText={field.helpText}
          >
            {renderFormField(fieldName, field)}
          </FormItem>
        ))}

        {/* Form actions */}
        <div className="flex gap-2 pt-4">
          <Button
            type="submit"
            variant="primary"
            disabled={!isValid || isSubmitting}
            loading={isSubmitting}
            className="flex-1"
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </Button>
          
          {isDirty && (
            <Button
              type="button"
              variant="outline"
              onClick={() => window.location.reload()}
              disabled={isSubmitting}
            >
              Reset
            </Button>
          )}
        </div>

        {/* Form validation summary */}
        {!isValid && Object.keys(errors).length > 0 && (
          <div className="mt-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <Typography variant="caption" className="text-red-700 dark:text-red-300 font-medium">
              Please fix the following errors:
            </Typography>
            <ul className="mt-1 space-y-1">
              {Object.entries(errors).map(([fieldName, error]) => (
                <li key={fieldName}>
                  <Typography variant="caption" className="text-red-600 dark:text-red-400">
                    • {schema.properties[fieldName]?.title || fieldName}: {error}
                  </Typography>
                </li>
              ))}
            </ul>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default FormMessage;
