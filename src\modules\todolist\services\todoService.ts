import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

import { TaskDto, TaskStatus, TaskPriority } from '../types/task.types';

/**
 * Score Todo DTO
 */
export interface ScoreTodoDto {
  /**
   * Số sao được chấm (1-5)
   */
  awardedStars: number;

  /**
   * Ph<PERSON>n hồi khi chấm điểm (tùy chọn)
   */
  feedback?: string;
}

/**
 * Update Task Description DTO
 */
export interface UpdateTaskDescriptionDto {
  description: string;
}

/**
 * Update Task Status DTO
 */
export interface UpdateTaskStatusDto {
  status: TaskStatus;
}

/**
 * Update Task Priority DTO
 */
export interface UpdateTaskPriorityDto {
  priority: TaskPriority;
}

/**
 * Update Task Assignee DTO
 */
export interface UpdateTaskAssigneeDto {
  assigneeId: number;
}

/**
 * Todo service for API calls
 */
export class TodoService {
  /**
   * Score a todo (chấm điểm sao)
   */
  static async scoreTodo(
    todoId: number,
    scoreData: ScoreTodoDto
  ): Promise<ApiResponseDto<TaskDto>> {
    return apiClient.put<TaskDto>(`/v1/todos/${todoId}/score`, scoreData);
  }

  /**
   * Update task description
   */
  static async updateTaskDescription(
    taskId: number,
    data: UpdateTaskDescriptionDto
  ): Promise<ApiResponseDto<TaskDto>> {
    return apiClient.patch<TaskDto>(`/todos/${taskId}/description`, data);
  }

  /**
   * Update task status
   */
  static async updateTaskStatus(
    taskId: number,
    data: UpdateTaskStatusDto
  ): Promise<ApiResponseDto<TaskDto>> {
    return apiClient.patch<TaskDto>(`/todos/${taskId}/status`, data);
  }

  /**
   * Update task priority
   */
  static async updateTaskPriority(
    taskId: number,
    data: UpdateTaskPriorityDto
  ): Promise<ApiResponseDto<TaskDto>> {
    return apiClient.patch<TaskDto>(`/todos/${taskId}/priority`, data);
  }

  /**
   * Update task assignee
   */
  static async updateTaskAssignee(
    taskId: number,
    data: UpdateTaskAssigneeDto
  ): Promise<ApiResponseDto<TaskDto>> {
    return apiClient.patch<TaskDto>(`/todos/${taskId}/assignee`, data);
  }

  /**
   * Get task by ID
   */
  static async getTaskById(taskId: number): Promise<ApiResponseDto<TaskDto>> {
    return apiClient.get<TaskDto>(`/todos/${taskId}`);
  }
}

export default TodoService;
