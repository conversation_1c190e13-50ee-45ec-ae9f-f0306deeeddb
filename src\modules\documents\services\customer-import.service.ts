import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

import {
  ExcelData,
  ImportJob,
  MappingTemplate,
  AutoMappingSuggestion,
  DataQualityMetrics,
  ImportAnalytics,
  GoogleSheetsImportData,
  ApiImportData,
  ImportValidationResult,
  ColumnMapping,
} from '../types/customer-import.types';

/**
 * Interface cho upload file request
 */
export interface UploadFileRequest {
  file: File;
  hasHeader?: boolean;
}

/**
 * Interface cho parse Excel response
 */
export interface ParseExcelResponse {
  data: ExcelData;
  suggestions?: AutoMappingSuggestion[];
}

/**
 * Interface cho validate import request
 */
export interface ValidateImportRequest {
  excelData: ExcelData;
  mappings: ColumnMapping[];
  validationRules?: string[];
}

/**
 * Interface cho start import request
 */
export interface StartImportRequest {
  excelData: ExcelData;
  mappings: ColumnMapping[];
  config?: {
    skipFirstRow?: boolean;
    validateEmail?: boolean;
    validatePhone?: boolean;
    allowDuplicates?: boolean;
    batchSize?: number;
  };
}

/**
 * Service xử lý API liên quan đến import khách hàng
 */
export const CustomerImportService = {
  /**
   * Upload và parse file Excel/CSV
   * @param request Upload file request
   * @returns Parsed Excel data với auto-mapping suggestions
   */
  uploadAndParseFile: async (
    request: UploadFileRequest
  ): Promise<ApiResponseDto<ParseExcelResponse>> => {
    const formData = new FormData();
    formData.append('file', request.file);
    if (request.hasHeader !== undefined) {
      formData.append('hasHeader', request.hasHeader.toString());
    }

    return apiClient.post('/user/customer-import/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  /**
   * Parse data từ URL (Google Sheets, etc.)
   * @param data Import data từ URL
   * @returns Parsed data
   */
  parseFromUrl: async (
    data: GoogleSheetsImportData | ApiImportData
  ): Promise<ApiResponseDto<ParseExcelResponse>> => {
    return apiClient.post('/user/customer-import/parse-url', data);
  },

  /**
   * Validate dữ liệu import
   * @param request Validation request
   * @returns Validation result
   */
  validateImportData: async (
    request: ValidateImportRequest
  ): Promise<ApiResponseDto<ImportValidationResult>> => {
    return apiClient.post('/user/customer-import/validate', request);
  },

  /**
   * Bắt đầu quá trình import
   * @param request Start import request
   * @returns Import job
   */
  startImport: async (
    request: StartImportRequest
  ): Promise<ApiResponseDto<ImportJob>> => {
    return apiClient.post('/user/customer-import/start', request);
  },

  /**
   * Lấy trạng thái import job
   * @param jobId ID của import job
   * @returns Import job status
   */
  getImportJobStatus: async (
    jobId: string
  ): Promise<ApiResponseDto<ImportJob>> => {
    return apiClient.get(`/user/customer-import/jobs/${jobId}`);
  },

  /**
   * Hủy import job
   * @param jobId ID của import job
   * @returns Cancellation result
   */
  cancelImportJob: async (
    jobId: string
  ): Promise<ApiResponseDto<void>> => {
    return apiClient.post(`/user/customer-import/jobs/${jobId}/cancel`);
  },

  /**
   * Lấy danh sách import jobs
   * @param params Query parameters
   * @returns List of import jobs
   */
  getImportJobs: async (params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ApiResponseDto<{
    items: ImportJob[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    return apiClient.get('/user/customer-import/jobs', { params });
  },

  /**
   * Lấy auto-mapping suggestions
   * @param excelData Excel data để analyze
   * @returns Auto-mapping suggestions
   */
  getAutoMappingSuggestions: async (
    excelData: ExcelData
  ): Promise<ApiResponseDto<AutoMappingSuggestion[]>> => {
    return apiClient.post('/user/customer-import/auto-mapping', { excelData });
  },

  /**
   * Tính toán data quality metrics
   * @param request Validation request
   * @returns Data quality metrics
   */
  calculateDataQuality: async (
    request: ValidateImportRequest
  ): Promise<ApiResponseDto<DataQualityMetrics>> => {
    return apiClient.post('/user/customer-import/data-quality', request);
  },

  /**
   * Lấy analytics data
   * @param params Query parameters
   * @returns Import analytics
   */
  getImportAnalytics: async (params?: {
    startDate?: string;
    endDate?: string;
    groupBy?: 'day' | 'week' | 'month';
  }): Promise<ApiResponseDto<ImportAnalytics>> => {
    return apiClient.get('/user/customer-import/analytics', { params });
  },

  // Template Management APIs
  /**
   * Lấy danh sách mapping templates
   * @param params Query parameters
   * @returns List of templates
   */
  getMappingTemplates: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    isPublic?: boolean;
  }): Promise<ApiResponseDto<{
    items: MappingTemplate[];
    total: number;
    page: number;
    limit: number;
  }>> => {
    return apiClient.get('/user/customer-import/templates', { params });
  },

  /**
   * Tạo mapping template mới
   * @param template Template data
   * @returns Created template
   */
  createMappingTemplate: async (
    template: Omit<MappingTemplate, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>
  ): Promise<ApiResponseDto<MappingTemplate>> => {
    return apiClient.post('/user/customer-import/templates', template);
  },

  /**
   * Cập nhật mapping template
   * @param id Template ID
   * @param template Updated template data
   * @returns Updated template
   */
  updateMappingTemplate: async (
    id: string,
    template: Partial<MappingTemplate>
  ): Promise<ApiResponseDto<MappingTemplate>> => {
    return apiClient.put(`/user/customer-import/templates/${id}`, template);
  },

  /**
   * Xóa mapping template
   * @param id Template ID
   * @returns Deletion result
   */
  deleteMappingTemplate: async (
    id: string
  ): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/user/customer-import/templates/${id}`);
  },

  /**
   * Download template Excel file
   * @returns Template file blob
   */
  downloadTemplate: async (): Promise<Blob> => {
    const response = await apiClient.get('/user/customer-import/template-download', {
      responseType: 'blob',
    });
    // When responseType is 'blob', axios returns the blob directly in data property
    return (response as unknown as { data: Blob }).data;
  },
};

export default CustomerImportService;
