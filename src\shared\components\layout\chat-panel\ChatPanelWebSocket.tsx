/**
 * Enhanced Chat Panel with WebSocket Support
 * Integrates real-time messaging, forms, and advanced content types
 */

import { useEffect } from 'react';

import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { useChatWebSocket } from '@/shared/websocket/hooks/useChatWebSocket';
import { MessageContent } from '@/shared/websocket/types/chat-message.types';

import ChatHeader from './ChatHeader';
import ChatInput from './ChatInput';
import MessageRenderer from './messages/MessageRenderer';

// Props interface
interface ChatPanelWebSocketProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
  roomId?: string;
  userId?: string;
  userInfo?: {
    name: string;
    avatar?: string;
  };
  websocketConfig?: {
    url: string;
    auth?: {
      token?: string;
      userId?: string;
    };
  };
}

/**
 * Enhanced Chat Panel with WebSocket Support
 */
const ChatPanelWebSocket = ({ 
  onClose, 
  onKeywordDetected,
  roomId = 'default-room',
  userId = 'default-user',
  userInfo = { name: 'User' },
  websocketConfig = {
    url: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
  }
}: ChatPanelWebSocketProps) => {
  const { notifications, addNotification, removeNotification } = useChatNotification();

  // WebSocket chat hook
  const {
    messages,
    isConnected,
    connectionStatus,
    sendMessage,
    clearMessages,
    typingUsers,
    startTyping,
    stopTyping,
    lastError,
    notifications: wsNotifications,
    clearError,
    clearNotifications,
  } = useChatWebSocket({
    config: websocketConfig,
    roomId,
    userId,
    userInfo,
    autoJoinRoom: true,
    enableTypingIndicator: true,
  });

  // Handle new chat
  const handleNewChat = () => {
    clearMessages();
    clearNotifications();
    clearError();
  };

  // Handle send message
  const handleSendMessage = async (content: string) => {
    try {
      // Create message content
      const messageContent: MessageContent = {
        type: 'text',
        data: {
          text: content,
        },
      };

      await sendMessage(messageContent);
    } catch (error) {
      console.error('Failed to send message:', error);
      showNotification('error', 'Failed to send message. Please try again.');
    }
  };

  // Handle typing events
  const handleInputChange = (value: string) => {
    if (value.trim()) {
      startTyping();
    } else {
      stopTyping();
    }
  };

  // Custom notification handler
  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    console.log(`[ChatPanelWebSocket] Showing notification: ${type} - ${message}`);
    return addNotification(type, message, 5000);
  };

  // Handle WebSocket errors
  useEffect(() => {
    if (lastError) {
      showNotification('error', lastError.message);
      clearError();
    }
  }, [lastError, clearError]);

  // Handle WebSocket notifications
  useEffect(() => {
    for (const notification of wsNotifications) {
      showNotification(notification.type, notification.message);
    }
    if (wsNotifications.length > 0) {
      clearNotifications();
    }
  }, [wsNotifications, clearNotifications]);

  // Render connection status
  const renderConnectionStatus = () => {
    if (isConnected) {return null;}

    const statusConfig = {
      connecting: { color: 'text-yellow-600', message: 'Connecting...' },
      reconnecting: { color: 'text-orange-600', message: 'Reconnecting...' },
      disconnected: { color: 'text-red-600', message: 'Disconnected' },
      error: { color: 'text-red-600', message: 'Connection Error' },
    };

    const config = statusConfig[connectionStatus] || statusConfig.disconnected;

    return (
      <div className="px-4 py-2 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center">
          <div className={`w-2 h-2 rounded-full mr-2 ${config.color.replace('text-', 'bg-')}`} />
          <span className={`text-sm ${config.color}`}>{config.message}</span>
        </div>
      </div>
    );
  };

  // Render typing indicators
  const renderTypingIndicators = () => {
    if (typingUsers.length === 0) {return null;}

    const typingNames = typingUsers.map(user => user.userName).join(', ');
    const typingText = typingUsers.length === 1 
      ? `${typingNames} is typing...` 
      : `${typingNames} are typing...`;

    return (
      <div className="px-4 py-2 text-sm text-gray-500 dark:text-gray-400 italic">
        {typingText}
      </div>
    );
  };

  // Render messages
  const renderMessages = () => {
    if (messages.length === 0) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center p-8">
          <div className="text-4xl mb-4">💬</div>
          <h2 className="text-xl font-semibold mb-2 text-gray-900 dark:text-gray-100">
            Welcome to Chat
          </h2>
          <p className="text-gray-500 dark:text-gray-400 max-w-md">
            Start a conversation by typing a message below. You can send text, share files, and interact with forms.
          </p>
        </div>
      );
    }

    return (
      <div className="space-y-4 p-4">
        {messages.map((message) => (
          <MessageRenderer
            key={message.id}
            message={message}
            isCurrentUser={message.sender.id === userId}
            showAvatar={true}
            showTimestamp={true}
            showSender={message.sender.id !== userId}
            onReply={(messageId) => {
              console.log('Reply to message:', messageId);
              // Implement reply functionality
            }}
            onEdit={(messageId) => {
              console.log('Edit message:', messageId);
              // Implement edit functionality
            }}
            onDelete={(messageId) => {
              console.log('Delete message:', messageId);
              // Implement delete functionality
            }}
            onReact={(messageId, emoji) => {
              console.log('React to message:', messageId, emoji);
              // Implement reaction functionality
            }}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-dark relative w-full">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark shadow-sm">
        <ChatHeader onNewChat={handleNewChat} onClose={onClose} />
        {renderConnectionStatus()}
      </div>

      {/* Messages area */}
      <div className="flex-1 overflow-y-auto">
        {renderMessages()}
        {renderTypingIndicators()}
      </div>

      {/* Input area */}
      <div className="border-t border-gray-200 dark:border-gray-700">
        <ChatInput
          onSendMessage={handleSendMessage}
          onKeywordDetected={onKeywordDetected}
          onInputChange={handleInputChange}
          showNotification={showNotification}
          disabled={!isConnected}
        />
      </div>
    </div>
  );
};

export default ChatPanelWebSocket;
