import React, { useCallback, useMemo, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import {
  Card,
  Table,
  DeleteConfirmModal,
  ActionMenu,
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useBulkSelection } from '@/shared/hooks/useBulkSelection';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { NotificationUtil } from '@/shared/utils/notification';

import TagForm from '../components/TagForm';
import { useTags, useDeleteTag, useBulkDeleteTags } from '../hooks/useTags';
import { TagDto, TagQueryDto } from '../types/task.types';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Trang quản lý Tag
 */
const TagListPage: React.FC = () => {
  const { t } = useTranslation(['todolist', 'common']);
  const [tagSearchTerm, setTagSearchTerm] = useState<string>('');

  // Sử dụng hook animation cho form
  const { isVisible, showForm, hideForm } = useSlideForm();

  // Tag mutations
  const deleteTagMutation = useDeleteTag();

  // Xử lý xóa tag
  const handleDeleteTag = useCallback(
    (id: number) => {
      if (window.confirm(t('todolist:tag.confirmDelete', 'Bạn có chắc chắn muốn xóa tag này?'))) {
        deleteTagMutation.mutate(id, {
          onSuccess: () => {
            NotificationUtil.success({
              message: t('todolist:tag.deleteSuccess', 'Xóa tag thành công'),
            });
          },
          onError: error => {
            console.error('Error deleting tag:', error);
            NotificationUtil.error({
              message: t('todolist:tag.deleteError', 'Xóa tag thất bại'),
            });
          },
        });
      }
    },
    [deleteTagMutation, t]
  );

  // Định nghĩa columns cho bảng tag
  const tagColumns = useMemo<TableColumn<TagDto>[]>(
    () => [
      { key: 'id', title: 'ID', dataIndex: 'id', width: '10%', sortable: true },
      {
        key: 'name',
        title: t('todolist:tag.table.name'),
        dataIndex: 'name',
        width: '70%',
        sortable: true,
      },
      {
        key: 'actions',
        title: t('common:actions'),
        width: '20%',
        render: (_: unknown, record: TagDto) => (
          <ActionMenu
            items={[
              {
                key: 'delete',
                label: t('common:delete'),
                icon: 'trash',
                onClick: () => handleDeleteTag(record.id),
                variant: 'danger',
              },
            ]}
            showAllInMenu={true}
          />
        ),
      },
    ],
    [t, handleDeleteTag]
  );

  // Tạo hàm createQueryParams cho tags
  const createTagQueryParams = useCallback((params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
  }): TagQueryDto => {
    return {
      page: params.page,
      limit: params.pageSize,
      search: tagSearchTerm || params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };
  }, [tagSearchTerm]);

  // Sử dụng hook useDataTable cho tags
  const tagDataTable = useDataTable(
    useDataTableConfig<TagDto, TagQueryDto>({
      columns: tagColumns,
      createQueryParams: createTagQueryParams,
    })
  );

  // Lấy danh sách tag từ API
  const { data: tagsData, isLoading: isTagsLoading } = useTags(tagDataTable.queryParams);

  // Hook để xóa nhiều tags
  const bulkDeleteTagsMutation = useBulkDeleteTags();

  // Tạo wrapper function cho bulk delete tags
  const bulkDeleteTagsWrapper = useMemo(() => ({
    mutateAsync: (ids: number[]) => bulkDeleteTagsMutation.mutateAsync({ ids }),
    isLoading: bulkDeleteTagsMutation.isPending,
    isPending: bulkDeleteTagsMutation.isPending,
  }), [bulkDeleteTagsMutation]);

  // Sử dụng custom hook để quản lý bulk selection cho tags
  const {
    selectedRowKeys: selectedTagRowKeys,
    setSelectedRowKeys: setSelectedTagRowKeys,
    showBulkDeleteConfirm: showTagBulkDeleteConfirm,
    deleteCount: tagDeleteCount,
    handleShowBulkDeleteConfirm: handleShowTagBulkDeleteConfirm,
    handleCancelBulkDelete: handleCancelTagBulkDelete,
    handleConfirmBulkDelete: handleConfirmTagBulkDelete,
    isDeleting: isTagDeleting,
  } = useBulkSelection({
    data: tagsData,
    bulkDeleteMutation: bulkDeleteTagsWrapper,
  });

  // Trigger refetch khi tagSearchTerm thay đổi
  useEffect(() => {
    if (tagSearchTerm !== undefined) {
      tagDataTable.tableData.handlePageChange(1, tagDataTable.tableData.pageSize);
    }
  }, [tagSearchTerm, tagDataTable.tableData]);

  // Xử lý search
  const handleSearch = (term: string) => {
    setTagSearchTerm(term);
  };

  // Xử lý thêm mới
  const handleAdd = () => {
    showForm();
  };

  // Xử lý submit form cho tag
  const handleTagSubmit = () => {
    hideForm();
    // Refresh tag data
    tagDataTable.tableData.handlePageChange(1, tagDataTable.tableData.pageSize);
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Tạo additional icons cho MenuIconBar
  const additionalIcons = useMemo(
    () => [
      // Nút bulk delete cho tags (chỉ hiện khi có tags được chọn)
      ...(selectedTagRowKeys.length > 0 ? [{
        icon: 'trash' as const,
        tooltip: t('common:deleteSelected', `Xóa ${selectedTagRowKeys.length} tag đã chọn`),
        variant: 'secondary' as const,
        onClick: handleShowTagBulkDeleteConfirm,
      }] : []),
    ],
    [selectedTagRowKeys.length, t, handleShowTagBulkDeleteConfirm]
  );

  return (
    <div className="w-full bg-background text-foreground">
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAdd}
        items={tagDataTable.menuItems}
        showDateFilter={false}
        showColumnFilter={false}
        additionalIcons={additionalIcons}
      />

      <SlideInForm isVisible={isVisible}>
        <TagForm onSubmit={handleTagSubmit} onCancel={handleCancel} />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={tagColumns}
          data={tagsData?.items ?? []}
          rowKey="id"
          loading={isTagsLoading}
          sortable={true}
          onSortChange={tagDataTable.tableData.handleSortChange}
          rowSelection={{
            selectedRowKeys: selectedTagRowKeys,
            onChange: (selectedRowKeys: React.Key[]) => {
              setSelectedTagRowKeys(selectedRowKeys);
            },
          }}
          pagination={{
            current: tagsData?.meta.currentPage || 1,
            pageSize: tagDataTable.tableData.pageSize,
            total: tagsData?.meta.totalItems || 0,
            onChange: tagDataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Bulk Delete Confirmation Modal for Tags */}
      <DeleteConfirmModal
        isOpen={showTagBulkDeleteConfirm}
        onClose={handleCancelTagBulkDelete}
        onConfirm={handleConfirmTagBulkDelete}
        title={t('common:confirmBulkDelete', 'Xác nhận xóa nhiều tag')}
        message={t('common:confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} tag đã chọn?', { count: tagDeleteCount })}
        description={t('common:bulkDeleteWarning', 'Hành động này không thể hoàn tác.')}
        isLoading={isTagDeleting}
        confirmText={t('common:delete', 'Xóa')}
        cancelText={t('common:cancel', 'Hủy')}
      />
    </div>
  );
};

export default TagListPage;
