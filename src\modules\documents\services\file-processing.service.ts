import * as XLSX from 'xlsx';

import { ExcelData, SUPPORTED_FILE_FORMATS, MAX_FILE_SIZE } from '../types/customer-import.types';

/**
 * Interface cho file validation result
 */
export interface FileValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Service xử lý file processing cho import
 */
export const FileProcessingService = {
  /**
   * Validate file trước khi upload
   * @param file File cần validate
   * @returns Validation result
   */
  validateFile: (file: File): FileValidationResult => {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      errors.push(`File size exceeds maximum limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    // Check file type
    const fileExtension = `.${  file.name.split('.').pop()?.toLowerCase()}`;
    if (!SUPPORTED_FILE_FORMATS.includes(fileExtension as typeof SUPPORTED_FILE_FORMATS[number])) {
      errors.push(`Unsupported file format. Supported formats: ${SUPPORTED_FILE_FORMATS.join(', ')}`);
    }

    // Check file name
    if (file.name.length > 255) {
      warnings.push('File name is too long');
    }

    // Check for special characters in filename
    if (!/^[\w.-]+$/.test(file.name)) {
      warnings.push('File name contains special characters');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  },

  /**
   * Parse Excel/CSV file thành ExcelData
   * @param file File cần parse
   * @param hasHeader Có header hay không
   * @returns Promise<ExcelData>
   */
  parseExcelFile: async (file: File, hasHeader = true): Promise<ExcelData> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          if (!data) {
            reject(new Error('Failed to read file'));
            return;
          }

          let workbook: XLSX.WorkBook;
          const fileExtension = `.${  file.name.split('.').pop()?.toLowerCase()}`;

          if (fileExtension === '.csv') {
            // Parse CSV
            const csvData = data as string;
            workbook = XLSX.read(csvData, { type: 'string' });
          } else {
            // Parse Excel
            const arrayBuffer = data as ArrayBuffer;
            workbook = XLSX.read(arrayBuffer, { type: 'array' });
          }

          // Get first worksheet
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];

          // Convert to JSON
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
            header: 1,
            defval: null,
          }) as (string | number | boolean | null)[][];

          if (jsonData.length === 0) {
            reject(new Error('File is empty'));
            return;
          }

          let headers: string[] = [];
          let rows: (string | number | boolean | null)[][] = [];

          if (hasHeader && jsonData.length > 0) {
            // First row is header
            headers = (jsonData[0] as (string | null)[]).map((h, index) => 
              h?.toString() || `Column ${index + 1}`
            );
            rows = jsonData.slice(1);
          } else {
            // Generate headers
            const maxColumns = Math.max(...jsonData.map(row => row.length));
            headers = Array.from({ length: maxColumns }, (_, i) => `Column ${i + 1}`);
            rows = jsonData;
          }

          // Filter out empty rows
          rows = rows.filter(row => 
            row.some(cell => cell !== null && cell !== undefined && cell !== '')
          );

          const excelData: ExcelData = {
            headers,
            rows,
            fileName: file.name,
          };

          resolve(excelData);
        } catch (error) {
          reject(new Error(`Failed to parse file: ${error instanceof Error ? error.message : 'Unknown error'}`));
        }
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      const fileExtension = `.${  file.name.split('.').pop()?.toLowerCase()}`;
      if (fileExtension === '.csv') {
        reader.readAsText(file);
      } else {
        reader.readAsArrayBuffer(file);
      }
    });
  },

  /**
   * Convert ExcelData thành CSV string
   * @param excelData Data cần convert
   * @returns CSV string
   */
  convertToCSV: (excelData: ExcelData): string => {
    const csvRows: string[] = [];

    // Add headers
    csvRows.push(excelData.headers.map(header => `"${header}"`).join(','));

    // Add data rows
    for (const row of excelData.rows) {
      const csvRow = row.map(cell => {
        if (cell === null || cell === undefined) {return '""';}
        const cellStr = cell.toString();
        // Escape quotes and wrap in quotes
        return `"${cellStr.replace(/"/g, '""')}"`;
      }).join(',');
      csvRows.push(csvRow);
    }

    return csvRows.join('\n');
  },

  /**
   * Download data as Excel file
   * @param excelData Data cần download
   * @param filename Tên file
   */
  downloadAsExcel: (excelData: ExcelData, filename = 'export.xlsx'): void => {
    // Create workbook
    const workbook = XLSX.utils.book_new();

    // Create worksheet data
    const wsData = [excelData.headers, ...excelData.rows];
    const worksheet = XLSX.utils.aoa_to_sheet(wsData);

    // Add worksheet to workbook
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    // Download file
    XLSX.writeFile(workbook, filename);
  },

  /**
   * Download data as CSV file
   * @param excelData Data cần download
   * @param filename Tên file
   */
  downloadAsCSV: (excelData: ExcelData, filename = 'export.csv'): void => {
    const csvContent = FileProcessingService.convertToCSV(excelData);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  },

  /**
   * Get file preview (first few rows)
   * @param excelData Excel data
   * @param maxRows Maximum rows to preview
   * @returns Preview data
   */
  getFilePreview: (excelData: ExcelData, maxRows = 5): ExcelData => {
    return {
      ...excelData,
      rows: excelData.rows.slice(0, maxRows),
    };
  },

  /**
   * Get file statistics
   * @param excelData Excel data
   * @returns File statistics
   */
  getFileStatistics: (excelData: ExcelData) => {
    const totalRows = excelData.rows.length;
    const totalColumns = excelData.headers.length;
    const emptyRows = excelData.rows.filter(row => 
      row.every(cell => cell === null || cell === undefined || cell === '')
    ).length;
    
    const columnStats = excelData.headers.map((header, index) => {
      const columnData = excelData.rows.map(row => row[index]);
      const emptyCount = columnData.filter(cell => 
        cell === null || cell === undefined || cell === ''
      ).length;
      const filledCount = totalRows - emptyCount;
      
      return {
        header,
        index,
        totalValues: totalRows,
        filledValues: filledCount,
        emptyValues: emptyCount,
        fillRate: totalRows > 0 ? (filledCount / totalRows) * 100 : 0,
      };
    });

    return {
      totalRows,
      totalColumns,
      emptyRows,
      validRows: totalRows - emptyRows,
      columnStats,
    };
  },
};

export default FileProcessingService;
