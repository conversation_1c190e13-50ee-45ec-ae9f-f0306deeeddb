import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { ContractService } from '../services/contract.service';

import type {
  ContractQueryParams,
  ContractCreateDto,
  ContractUpdateDto,
} from '../types/contract.types';

/**
 * Query keys for contracts
 */
export const CONTRACT_QUERY_KEYS = {
  ALL: ['contracts'] as const,
  LIST: (params: ContractQueryParams) => [...CONTRACT_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...CONTRACT_QUERY_KEYS.ALL, 'detail', id] as const,
  STATS: () => [...CONTRACT_QUERY_KEYS.ALL, 'stats'] as const,
  HISTORY: (id: string) => [...CONTRACT_QUERY_KEYS.ALL, 'history', id] as const,
  EXPIRING: (days: number) => [...CONTRACT_QUERY_KEYS.ALL, 'expiring', days] as const,
  SEARCH: (query: string, filters?: any) => [...CONTRACT_QUERY_KEYS.ALL, 'search', query, filters] as const,
  ACTIVITY: (id: string) => [...CONTRACT_QUERY_KEYS.ALL, 'activity', id] as const,
};

/**
 * Hook to get contracts list with pagination and filtering
 */
export const useContracts = (params?: ContractQueryParams) => {
  return useQuery({
    queryKey: CONTRACT_QUERY_KEYS.LIST(params || {}),
    queryFn: () => ContractService.getContracts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to get contract by ID
 */
export const useContract = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_QUERY_KEYS.DETAIL(id),
    queryFn: () => ContractService.getContract(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to get contract statistics
 */
export const useContractStats = () => {
  return useQuery({
    queryKey: CONTRACT_QUERY_KEYS.STATS(),
    queryFn: () => ContractService.getContractStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to get contract history
 */
export const useContractHistory = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_QUERY_KEYS.HISTORY(id),
    queryFn: () => ContractService.getContractHistory(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to get contracts expiring soon
 */
export const useExpiringSoonContracts = (days = 30) => {
  return useQuery({
    queryKey: CONTRACT_QUERY_KEYS.EXPIRING(days),
    queryFn: () => ContractService.getExpiringSoon(days),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook to search contracts
 */
export const useSearchContracts = (query: string, filters?: any) => {
  return useQuery({
    queryKey: CONTRACT_QUERY_KEYS.SEARCH(query, filters),
    queryFn: () => ContractService.searchContracts(query, filters),
    enabled: !!query && query.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook to get contract activity log
 */
export const useContractActivity = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_QUERY_KEYS.ACTIVITY(id),
    queryFn: () => ContractService.getActivityLog(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook to create contract
 */
export const useCreateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ContractCreateDto) => ContractService.createContract(data),
    onSuccess: () => {
      // Invalidate contracts list
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to update contract
 */
export const useUpdateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ContractUpdateDto }) =>
      ContractService.updateContract(id, data),
    onSuccess: (updatedContract) => {
      // Update contract detail cache
      queryClient.setQueryData(
        CONTRACT_QUERY_KEYS.DETAIL(updatedContract.id),
        updatedContract
      );
      // Invalidate contracts list
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to delete contract
 */
export const useDeleteContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractService.deleteContract(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(deletedId) });
      // Invalidate contracts list
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to duplicate contract
 */
export const useDuplicateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractService.duplicateContract(id),
    onSuccess: () => {
      // Invalidate contracts list
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to upload attachment
 */
export const useUploadAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, file, description }: { id: string; file: File; description?: string }) =>
      ContractService.uploadAttachment(id, file, description),
    onSuccess: (_, { id }) => {
      // Invalidate contract detail to refresh attachments
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
    },
  });
};

/**
 * Hook to delete attachment
 */
export const useDeleteAttachment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ contractId, attachmentId }: { contractId: string; attachmentId: string }) =>
      ContractService.deleteAttachment(contractId, attachmentId),
    onSuccess: (_, { contractId }) => {
      // Invalidate contract detail to refresh attachments
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(contractId) });
    },
  });
};

/**
 * Hook to add comment
 */
export const useAddComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      id,
      content,
      type,
      isInternal,
      position,
    }: {
      id: string;
      content: string;
      type?: string;
      isInternal?: boolean;
      position?: any;
    }) => ContractService.addComment(id, content, type, isInternal, position),
    onSuccess: (_, { id }) => {
      // Invalidate contract detail to refresh comments
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
    },
  });
};

/**
 * Hook to send for review
 */
export const useSendForReview = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reviewerIds, message }: { id: string; reviewerIds: string[]; message?: string }) =>
      ContractService.sendForReview(id, reviewerIds, message),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
    },
  });
};

/**
 * Hook to send for approval
 */
export const useSendForApproval = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, approverIds, message }: { id: string; approverIds: string[]; message?: string }) =>
      ContractService.sendForApproval(id, approverIds, message),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
    },
  });
};

/**
 * Hook to send for signature
 */
export const useSendForSignature = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, signers, message }: { id: string; signers: any[]; message?: string }) =>
      ContractService.sendForSignature(id, signers, message),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
    },
  });
};

/**
 * Hook to cancel contract
 */
export const useCancelContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason: string }) =>
      ContractService.cancelContract(id, reason),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to terminate contract
 */
export const useTerminateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason, terminationDate }: { id: string; reason: string; terminationDate?: string }) =>
      ContractService.terminateContract(id, reason, terminationDate),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to renew contract
 */
export const useRenewContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, newEndDate, terms }: { id: string; newEndDate: string; terms?: string }) =>
      ContractService.renewContract(id, newEndDate, terms),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.DETAIL(id) });
      queryClient.invalidateQueries({ queryKey: CONTRACT_QUERY_KEYS.ALL });
    },
  });
};
