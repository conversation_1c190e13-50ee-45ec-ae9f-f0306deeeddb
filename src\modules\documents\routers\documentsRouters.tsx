import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';

import i18n from '@/lib/i18n';
import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';

import DocumentDetails from '../components/documents/DocumentDetails';
import { DocumentsPage } from '../pages';

/**
 * Documents module routes
 */
export const documentsRouters: RouteObject[] = [
  // Trang tổng quan
  {
    path: '/documents',
    element: (
      <MainLayout title={i18n.t('documents:title', 'Tài liệu')}>
        <Suspense fallback={<Loading />}>
          <DocumentsPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang chi tiết tài liệu
  {
    path: '/documents/:id',
    element: (
      <MainLayout title={i18n.t('documents:detailTitle', 'Tài liệu')}>
        <Suspense fallback={<Loading />}>
          <DocumentDetails />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default documentsRouters;
