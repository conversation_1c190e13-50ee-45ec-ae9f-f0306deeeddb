import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { ContractApprovalService } from '../services/contract-approval.service';

import type {
  ApprovalQueryParams,
  ApprovalCreateDto,
  ApprovalUpdateDto,
  ApprovalActionDto,
  ApprovalRejectDto,
  ApprovalDelegateDto,
} from '../types/contract-approval.types';

/**
 * Query keys for contract approvals
 */
export const CONTRACT_APPROVAL_QUERY_KEYS = {
  ALL: ['contract-approvals'] as const,
  LIST: (params: ApprovalQueryParams) => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'detail', id] as const,
  CONTRACT: (contractId: string) => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'contract', contractId] as const,
  WORKFLOW: (contractId: string) => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'workflow', contractId] as const,
  PENDING: (userId?: string) => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'pending', userId] as const,
  STATS: () => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'stats'] as const,
  HISTORY: (contractId: string) => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'history', contractId] as const,
  TEMPLATES: () => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'templates'] as const,
  RULES: () => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'rules'] as const,
  NOTIFICATIONS: (userId?: string) => [...CONTRACT_APPROVAL_QUERY_KEYS.ALL, 'notifications', userId] as const,
};

/**
 * Hook to get approvals list
 */
export const useContractApprovals = (params?: ApprovalQueryParams) => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.LIST(params || {}),
    queryFn: () => ContractApprovalService.getApprovals(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to get approval by ID
 */
export const useContractApproval = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.DETAIL(id),
    queryFn: () => ContractApprovalService.getApproval(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to get approvals for a contract
 */
export const useContractApprovalsByContract = (contractId: string) => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.CONTRACT(contractId),
    queryFn: () => ContractApprovalService.getContractApprovals(contractId),
    enabled: !!contractId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook to get approval workflow for a contract
 */
export const useApprovalWorkflow = (contractId: string) => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.WORKFLOW(contractId),
    queryFn: () => ContractApprovalService.getApprovalWorkflow(contractId),
    enabled: !!contractId,
    staleTime: 2 * 60 * 1000,
  });
};

/**
 * Hook to get pending approvals for user
 */
export const usePendingApprovals = (userId?: string) => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.PENDING(userId),
    queryFn: () => ContractApprovalService.getPendingApprovals(userId),
    staleTime: 2 * 60 * 1000,
  });
};

/**
 * Hook to get approval statistics
 */
export const useApprovalStats = () => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.STATS(),
    queryFn: () => ContractApprovalService.getApprovalStats(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to get approval history for a contract
 */
export const useApprovalHistory = (contractId: string) => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.HISTORY(contractId),
    queryFn: () => ContractApprovalService.getApprovalHistory(contractId),
    enabled: !!contractId,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to get approval templates
 */
export const useApprovalTemplates = () => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.TEMPLATES(),
    queryFn: () => ContractApprovalService.getApprovalTemplates(),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook to get approval rules
 */
export const useApprovalRules = () => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.RULES(),
    queryFn: () => ContractApprovalService.getApprovalRules(),
    staleTime: 30 * 60 * 1000,
  });
};

/**
 * Hook to get approval notifications
 */
export const useApprovalNotifications = (userId?: string) => {
  return useQuery({
    queryKey: CONTRACT_APPROVAL_QUERY_KEYS.NOTIFICATIONS(userId),
    queryFn: () => ContractApprovalService.getApprovalNotifications(userId),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

/**
 * Hook to create approval
 */
export const useCreateApproval = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ApprovalCreateDto) => ContractApprovalService.createApproval(data),
    onSuccess: (newApproval) => {
      // Invalidate approvals list
      queryClient.invalidateQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.ALL });
      // Invalidate contract approvals
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.CONTRACT(newApproval.contractId) 
      });
      // Invalidate workflow
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.WORKFLOW(newApproval.contractId) 
      });
    },
  });
};

/**
 * Hook to update approval
 */
export const useUpdateApproval = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ApprovalUpdateDto }) =>
      ContractApprovalService.updateApproval(id, data),
    onSuccess: (updatedApproval) => {
      // Update approval detail cache
      queryClient.setQueryData(
        CONTRACT_APPROVAL_QUERY_KEYS.DETAIL(updatedApproval.id),
        updatedApproval
      );
      // Invalidate approvals list
      queryClient.invalidateQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to delete approval
 */
export const useDeleteApproval = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractApprovalService.deleteApproval(id),
    onSuccess: (_, deletedId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.DETAIL(deletedId) });
      // Invalidate approvals list
      queryClient.invalidateQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to approve contract
 */
export const useApproveContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data?: ApprovalActionDto }) =>
      ContractApprovalService.approveContract(id, data),
    onSuccess: (updatedApproval) => {
      // Update approval detail cache
      queryClient.setQueryData(
        CONTRACT_APPROVAL_QUERY_KEYS.DETAIL(updatedApproval.id),
        updatedApproval
      );
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.ALL });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.CONTRACT(updatedApproval.contractId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.WORKFLOW(updatedApproval.contractId) 
      });
    },
  });
};

/**
 * Hook to reject contract
 */
export const useRejectContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ApprovalRejectDto }) =>
      ContractApprovalService.rejectContract(id, data),
    onSuccess: (updatedApproval) => {
      // Update approval detail cache
      queryClient.setQueryData(
        CONTRACT_APPROVAL_QUERY_KEYS.DETAIL(updatedApproval.id),
        updatedApproval
      );
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.ALL });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.CONTRACT(updatedApproval.contractId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.WORKFLOW(updatedApproval.contractId) 
      });
    },
  });
};

/**
 * Hook to delegate approval
 */
export const useDelegateApproval = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ApprovalDelegateDto }) =>
      ContractApprovalService.delegateApproval(id, data),
    onSuccess: (updatedApproval) => {
      // Update approval detail cache
      queryClient.setQueryData(
        CONTRACT_APPROVAL_QUERY_KEYS.DETAIL(updatedApproval.id),
        updatedApproval
      );
      // Invalidate approvals list
      queryClient.invalidateQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to send approval reminder
 */
export const useSendApprovalReminder = () => {
  return useMutation({
    mutationFn: ({ id, message }: { id: string; message?: string }) =>
      ContractApprovalService.sendApprovalReminder(id, message),
  });
};

/**
 * Hook to cancel approval request
 */
export const useCancelApprovalRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) =>
      ContractApprovalService.cancelApprovalRequest(id, reason),
    onSuccess: (updatedApproval) => {
      queryClient.setQueryData(
        CONTRACT_APPROVAL_QUERY_KEYS.DETAIL(updatedApproval.id),
        updatedApproval
      );
      queryClient.invalidateQueries({ queryKey: CONTRACT_APPROVAL_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to restart approval workflow
 */
export const useRestartApprovalWorkflow = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (contractId: string) => ContractApprovalService.restartApprovalWorkflow(contractId),
    onSuccess: (_, contractId) => {
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.WORKFLOW(contractId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: CONTRACT_APPROVAL_QUERY_KEYS.CONTRACT(contractId) 
      });
    },
  });
};
