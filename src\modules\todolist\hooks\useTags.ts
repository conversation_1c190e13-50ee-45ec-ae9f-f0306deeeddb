import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { TagService } from '../services/tag.service';
import {
  TagQueryDto,
  CreateTagDto,
  UpdateTagDto,
  BulkDeleteTagDto,
} from '../types/task.types';

// Key cho React Query
const TAGS_QUERY_KEY = 'tags';

/**
 * Hook để lấy danh sách tag có phân trang
 * @param params Tham số truy vấn
 * @returns Query result với danh sách tag
 */
export const useTags = (params?: TagQueryDto) => {
  return useQuery({
    queryKey: [TAGS_QUERY_KEY, params],
    queryFn: () => TagService.getTags(params),
    select: data => data,
  });
};

/**
 * Hook để lấy tất cả tag không phân trang
 * @param search Từ khóa tìm kiếm
 * @returns Query result với danh sách tất cả tag
 */
export const useAllTags = (search?: string) => {
  return useQuery({
    queryKey: [TAGS_QUERY_KEY, 'all', search],
    queryFn: () => TagService.getAllTags(search),
    select: data => data.result,
  });
};

/**
 * Hook để lấy thông tin tag theo ID
 * @param id ID của tag
 * @returns Query result với thông tin tag
 */
export const useTag = (id: number) => {
  return useQuery({
    queryKey: [TAGS_QUERY_KEY, id],
    queryFn: () => TagService.getTagById(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo tag mới
 * @returns Mutation để tạo tag
 */
export const useCreateTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTagDto) => TagService.createTag(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách tag
      queryClient.invalidateQueries({ queryKey: [TAGS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật tag
 * @returns Mutation để cập nhật tag
 */
export const useUpdateTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateTagDto }) =>
      TagService.updateTag(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch danh sách tag
      queryClient.invalidateQueries({ queryKey: [TAGS_QUERY_KEY] });
      // Invalidate tag cụ thể
      queryClient.invalidateQueries({ queryKey: [TAGS_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa tag
 * @returns Mutation để xóa tag
 */
export const useDeleteTag = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => TagService.deleteTag(id),
    onSuccess: () => {
      // Invalidate và refetch danh sách tag
      queryClient.invalidateQueries({ queryKey: [TAGS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để xóa nhiều tag
 * @returns Mutation để xóa nhiều tag
 */
export const useBulkDeleteTags = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: BulkDeleteTagDto) => TagService.bulkDeleteTags(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách tag
      queryClient.invalidateQueries({ queryKey: [TAGS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để tạo hàm load tags cho AsyncMultiSelectMenu
 * @returns Hàm load options cho AsyncMultiSelectMenu
 */
export const useTagsForMultiSelect = () => {
  return (params: { search?: string; page?: number; limit?: number }) =>
    TagService.getTagsForMultiSelect(params);
};
