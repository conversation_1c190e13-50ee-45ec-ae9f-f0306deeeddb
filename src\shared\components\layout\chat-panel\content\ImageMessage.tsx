/**
 * Image Message Component
 * Renders image content with zoom and preview functionality
 */

import React, { useState } from 'react';

import { Typography, Modal, Icon } from '@/shared/components/common';
import { ImageContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface ImageMessageProps {
  data: ImageContentData;
  className?: string;
}

/**
 * Image Message Component
 */
const ImageMessage: React.FC<ImageMessageProps> = ({ data, className = '' }) => {
  const {
    url,
    alt,
    caption,
    thumbnail,
    width,
    height,
    size,
    format,
  } = data;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) {return '0 Bytes';}
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))  } ${  sizes[i]}`;
  };

  // Handle image load
  const handleImageLoad = () => {
    setImageLoaded(true);
    setImageError(false);
  };

  // Handle image error
  const handleImageError = () => {
    setImageError(true);
    setImageLoaded(false);
  };

  // Render image placeholder
  const renderPlaceholder = () => (
    <div className="flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg p-8">
      <div className="text-center">
        <Icon name="image" size="lg" className="text-gray-400 mb-2" />
        <Typography variant="caption" className="text-gray-500">
          {imageError ? 'Failed to load image' : 'Loading image...'}
        </Typography>
      </div>
    </div>
  );

  // Render image info
  const renderImageInfo = () => {
    const info = [];
    
    if (width && height) {
      info.push(`${width}×${height}`);
    }
    
    if (size) {
      info.push(formatFileSize(size));
    }
    
    if (format) {
      info.push(format.toUpperCase());
    }

    if (info.length === 0) {return null;}

    return (
      <Typography variant="caption" className="text-gray-500 dark:text-gray-400 mt-1">
        {info.join(' • ')}
      </Typography>
    );
  };

  // Render image modal
  const renderImageModal = () => (
    <Modal
      isOpen={isModalOpen}
      onClose={() => setIsModalOpen(false)}
      title={caption || alt || 'Image'}
      size="lg"
    >
      <div className="p-4">
        <div className="flex justify-center">
          <img
            src={url}
            alt={alt}
            className="max-w-full max-h-[80vh] object-contain"
            onLoad={handleImageLoad}
            onError={handleImageError}
          />
        </div>
        
        {caption && (
          <Typography variant="body2" className="text-center mt-4 text-gray-600 dark:text-gray-400">
            {caption}
          </Typography>
        )}
        
        <div className="text-center mt-2">
          {renderImageInfo()}
        </div>
      </div>
    </Modal>
  );

  return (
    <>
      <div className={`rounded-lg overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
        {/* Image container */}
        <div 
          className="relative cursor-pointer group"
          onClick={() => setIsModalOpen(true)}
        >
          {!imageLoaded && !imageError && renderPlaceholder()}
          
          {!imageError && (
            <img
              src={thumbnail || url}
              alt={alt}
              className={`w-full h-auto object-cover transition-opacity duration-200 ${
                imageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              style={{ 
                maxHeight: '400px',
                display: imageLoaded ? 'block' : 'none'
              }}
              onLoad={handleImageLoad}
              onError={handleImageError}
            />
          )}

          {imageError && renderPlaceholder()}

          {/* Overlay on hover */}
          {imageLoaded && (
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <div className="bg-white dark:bg-gray-800 rounded-full p-2 shadow-lg">
                  <Icon name="zoom-in" size="sm" className="text-gray-700 dark:text-gray-300" />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Image caption and info */}
        {(caption || size || width || height || format) && (
          <div className="p-3">
            {caption && (
              <Typography variant="body2" className="text-gray-700 dark:text-gray-300 mb-1">
                {caption}
              </Typography>
            )}
            {renderImageInfo()}
          </div>
        )}
      </div>

      {/* Image modal */}
      {renderImageModal()}
    </>
  );
};

export default ImageMessage;
