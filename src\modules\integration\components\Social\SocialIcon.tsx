import React from 'react';

import { Typography, Card, Icon } from '@/shared/components/common';
import { PropsSocial } from '@/shared/components/common/Icon/socials';
import { useTheme } from '@/shared/contexts';

export interface SocialIconProps {
  network: PropsSocial;
  onIconClick?: (network: PropsSocial) => void;
}

export const SocialIcon: React.FC<SocialIconProps> = ({ network, onIconClick }) => {
  const { themeMode } = useTheme();
  const isDark = themeMode === 'dark';

  // Handle click event
  const handleClick = () => {
    if (onIconClick) {
      onIconClick(network);
    }
  };

  return (
    <Card onClick={handleClick} tabIndex={0} aria-label={`Connect with ${network.name}`}>
      {/* Icon */}
      <div
        className="w-12 h-12 rounded-full flex items-center justify-center text-white"
        style={{ backgroundColor: network.color || (isDark ? '#374151' : '#E5E7EB') }}
      >
        <Icon name={network.iconName} size="md" className="w-6 h-6" />
      </div>

      {/* Name */}
      <Typography variant="body2" align="center" weight="medium" className="mt-1">
        {network.name}
      </Typography>
    </Card>
  );
};
