/**
 * Message Renderer Component
 * Renders different types of chat messages based on content type
 */

import React from 'react';

import { ChatMessage } from '@/shared/websocket/types/chat-message.types';

import AudioMessage from '../content/AudioMessage';
import FileMessage from '../content/FileMessage';
import FormMessage from '../content/FormMessage';
import ImageMessage from '../content/ImageMessage';
import LinkMessage from '../content/LinkMessage';
import MarkdownMessage from '../content/MarkdownMessage';
import NavigationMessage from '../content/NavigationMessage';
import StreamMessage from '../content/StreamMessage';
import TextMessage from '../content/TextMessage';
import VideoMessage from '../content/VideoMessage';

import MessageContainer from './MessageContainer';

// Props interface
export interface MessageRendererProps {
  message: ChatMessage;
  isCurrentUser?: boolean;
  showAvatar?: boolean;
  showTimestamp?: boolean;
  showSender?: boolean;
  onReply?: (messageId: string) => void;
  onEdit?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
  onReact?: (messageId: string, emoji: string) => void;
  className?: string;
}

/**
 * Message Renderer Component
 */
const MessageRenderer: React.FC<MessageRendererProps> = ({
  message,
  isCurrentUser = false,
  showAvatar = true,
  showTimestamp = true,
  showSender = true,
  onReply,
  onEdit,
  onDelete,
  onReact,
  className = '',
}) => {
  // Render content based on message type
  const renderContent = () => {
    const { content } = message;

    switch (content.type) {
      case 'text':
        return <TextMessage data={content.data} />;

      case 'markdown':
        return <MarkdownMessage data={content.data} />;

      case 'image':
        return <ImageMessage data={content.data} />;

      case 'video':
        return <VideoMessage data={content.data} />;

      case 'audio':
        return <AudioMessage data={content.data} />;

      case 'link':
        return <LinkMessage data={content.data} />;

      case 'file':
        return <FileMessage data={content.data} />;

      case 'form':
        return (
          <FormMessage 
            data={content.data} 
            messageId={message.id}
            roomId={message.metadata.roomId}
          />
        );

      case 'navigation':
        return <NavigationMessage data={content.data} />;

      case 'stream':
        return (
          <StreamMessage 
            data={content.data} 
            messageId={message.id}
          />
        );

      default:
        return (
          <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-lg">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Unsupported message type: {content.type}
            </p>
          </div>
        );
    }
  };

  return (
    <MessageContainer
      message={message}
      isCurrentUser={isCurrentUser}
      showAvatar={showAvatar}
      showTimestamp={showTimestamp}
      showSender={showSender}
      onReply={onReply}
      onEdit={onEdit}
      onDelete={onDelete}
      onReact={onReact}
      className={className}
    >
      {renderContent()}
    </MessageContainer>
  );
};

export default MessageRenderer;
