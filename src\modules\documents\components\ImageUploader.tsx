import React, { useState } from 'react';

import { Typography, Icon } from '@/shared/components/common';

interface ImageUploaderProps {
  /**
   * Callback khi upload ảnh
   */
  onImageUpload: (file: File, dataUrl: string) => void;

  /**
   * Ảnh hiện tại (nếu có)
   */
  currentImage?: string | null;

  /**
   * Chiều cao của vùng upload
   */
  height?: string;

  /**
   * Kích thước icon
   */
  iconSize?: 'sm' | 'md' | 'lg';

  /**
   * Text hiển thị
   */
  placeholder?: string;

  /**
   * Class bổ sung
   */
  className?: string;
}

/**
 * Component upload ảnh với preview
 */
const ImageUploader: React.FC<ImageUploaderProps> = ({
  onImageUpload,
  currentImage = null,
  height = 'h-64',
  iconSize = 'lg',
  placeholder = 'Kéo thả hoặc click để tải lên ảnh',
  className = '',
}) => {
  const [isDragging, setIsDragging] = useState(false);

  // Xử lý khi kéo file vào vùng upload
  const handleDragEnter = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  // Xử lý khi thả file
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  // Xử lý khi chọn file
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      processFile(files[0]);
    }
  };

  // Xử lý file đã chọn
  const processFile = (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('Vui lòng chọn file ảnh');
      return;
    }

    const reader = new FileReader();
    reader.onload = () => {
      onImageUpload(file, reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  return (
    <div
      className={`
        border-2 border-dashed rounded-lg p-4 flex flex-col items-center justify-center relative
        ${height} ${className}
        ${isDragging ? 'border-primary bg-primary/5' : 'border-gray-300 dark:border-gray-700'}
      `}
      style={{
        backgroundImage: currentImage ? `url(${currentImage})` : 'none',
        backgroundSize: 'contain',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      {!currentImage && (
        <>
          <Icon name="upload" size={iconSize} className="mb-2 text-gray-400" />
          <Typography variant="body2" className="text-center text-gray-500 dark:text-gray-400">
            {placeholder}
          </Typography>
        </>
      )}
      <input
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
      />
    </div>
  );
};

export default ImageUploader;
