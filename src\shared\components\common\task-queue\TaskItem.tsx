import { formatDistanceToNow } from 'date-fns';
import { vi } from 'date-fns/locale';
import { motion } from 'framer-motion';
/**
 * Component hiển thị một task trong queue
 */
import React, { useState } from 'react';

import { Icon, IconButton } from '@/shared/components/common';
import { Task, TaskStatus, TaskType, FileUploadTask } from '@/shared/types/task-queue.types';

/**
 * Props cho TaskItem
 */
export interface TaskItemProps {
  /**
   * Task cần hiển thị
   */
  task: Task;

  /**
   * Callback khi hủy task
   */
  onCancel?: () => void;

  /**
   * Callback khi thử lại task
   */
  onRetry?: () => void;

  /**
   * Callback khi xóa task
   */
  onRemove?: () => void;
}

/**
 * Component hiển thị một task trong queue
 */
const TaskItem: React.FC<TaskItemProps> = ({ task, onCancel, onRetry, onRemove }) => {
  // Trạng thái mở rộng chi tiết
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  // Xác định icon cho task
  const getTaskIcon = () => {
    if (task.type === TaskType.FILE_UPLOAD) {
      const fileTask = task as FileUploadTask;
      const fileType = fileTask.file?.type || '';

      if (fileType.startsWith('image/')) {
        return <Icon name="image" className="w-5 h-5 text-blue-500" />;
      } else if (fileType.startsWith('video/')) {
        return <Icon name="film" className="w-5 h-5 text-purple-500" />;
      } else if (fileType.startsWith('audio/')) {
        return <Icon name="file" className="w-5 h-5 text-yellow-500" />;
      } else if (fileType.startsWith('application/') || fileType.startsWith('text/')) {
        return <Icon name="file-text" className="w-5 h-5 text-gray-500" />;
      }

      return <Icon name="cloud-upload" className="w-5 h-5 text-blue-500" />;
    } else if (task.type === TaskType.API_CALL) {
      return <Icon name="upload" className="w-5 h-5 text-green-500" />;
    }

    return <Icon name="file" className="w-5 h-5 text-gray-500" />;
  };

  // Xác định icon cho trạng thái
  const getStatusIcon = () => {
    // Kiểm tra nếu là lỗi CORS hoặc Network Error (có thể đã upload thành công)
    if (task.status === TaskStatus.ERROR &&
        task.error instanceof Error &&
        (task.error.message?.includes('CORS') || task.error.message === 'Network Error')) {
      return <Icon name="alert-triangle" className="w-5 h-5 text-yellow-500" />;
    }

    switch (task.status) {
      case TaskStatus.SUCCESS:
        return <Icon name="check-circle" className="w-5 h-5 text-green-500" />;
      case TaskStatus.ERROR:
        return <Icon name="alert-circle" className="w-5 h-5 text-red-500" />;
      case TaskStatus.CANCELLED:
        return <Icon name="x-circle" className="w-5 h-5 text-gray-500" />;
      case TaskStatus.RUNNING:
        return (
          <div className="animate-spin">
            <Icon name="refresh-cw" className="w-5 h-5 text-blue-500" />
          </div>
        );
      case TaskStatus.PENDING:
        return <Icon name="pause-circle" className="w-5 h-5 text-yellow-500" />;
      default:
        return null;
    }
  };

  // Xác định màu cho progress bar
  const getProgressBarColor = () => {
    // Kiểm tra nếu là lỗi CORS hoặc Network Error (có thể đã upload thành công)
    if (task.status === TaskStatus.ERROR &&
        task.error instanceof Error &&
        (task.error.message?.includes('CORS') || task.error.message === 'Network Error')) {
      return 'bg-yellow-500'; // Màu vàng cho lỗi CORS hoặc Network Error
    }

    switch (task.status) {
      case TaskStatus.SUCCESS:
        return 'bg-green-500';
      case TaskStatus.ERROR:
        return 'bg-red-500';
      case TaskStatus.CANCELLED:
        return 'bg-gray-500';
      case TaskStatus.RUNNING:
        return 'bg-blue-500';
      case TaskStatus.PENDING:
        return 'bg-yellow-500';
      default:
        return 'bg-gray-300';
    }
  };

  // Xác định thời gian hiển thị
  const getTimeDisplay = () => {
    if (task.completedAt) {
      return `Hoàn thành ${formatDistanceToNow(task.completedAt, { addSuffix: true, locale: vi })}`;
    } else if (task.startedAt) {
      return `Bắt đầu ${formatDistanceToNow(task.startedAt, { addSuffix: true, locale: vi })}`;
    } else {
      return `Tạo ${formatDistanceToNow(task.createdAt, { addSuffix: true, locale: vi })}`;
    }
  };

  // Hiển thị thumbnail cho file upload
  const renderThumbnail = () => {
    if (task.type === TaskType.FILE_UPLOAD) {
      const fileTask = task as FileUploadTask;

      if (fileTask.thumbnail) {
        return (
          <div className="w-10 h-10 rounded overflow-hidden flex-shrink-0 mr-3">
            <img
              src={fileTask.thumbnail}
              alt={fileTask.file?.name || 'File thumbnail'}
              className="w-full h-full object-cover"
            />
          </div>
        );
      }
    }

    return (
      <div className="w-10 h-10 rounded bg-muted/30 flex items-center justify-center flex-shrink-0 mr-3">
        {getTaskIcon()}
      </div>
    );
  };

  return (
    <motion.div
      layout
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, height: 0 }}
      transition={{ duration: 0.2 }}
      className="bg-card rounded-lg shadow-sm border border-border overflow-hidden"
    >
      {/* Task header */}
      <div
        className="p-3 flex items-start cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        {/* Thumbnail hoặc icon */}
        {renderThumbnail()}

        {/* Thông tin task */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-foreground truncate">
              {task.title}
            </h3>
            <div className="ml-2 flex-shrink-0">{getStatusIcon()}</div>
          </div>

          <p className="text-xs text-muted mt-1 truncate">
            {task.type === TaskType.FILE_UPLOAD
              ? (task as FileUploadTask).file?.name || 'Không có tên file'
              : task.description || 'Không có mô tả'}
          </p>

          <div className="mt-2">
            <div className="w-full bg-muted/30 rounded-full h-1.5">
              <div
                className={`h-1.5 rounded-full ${getProgressBarColor()}`}
                style={{ width: `${task.progress}%` }}
              ></div>
            </div>
          </div>

          <div className="mt-1 flex items-center justify-between">
            <span className="text-xs text-muted">
              {task.status === TaskStatus.RUNNING ? `${task.progress}%` : getTimeDisplay()}
            </span>

            <div className="flex space-x-1">
              {/* Nút hủy */}
              {task.status === TaskStatus.RUNNING && task.cancellable && onCancel && (
                <IconButton
                  icon="x"
                  size="sm"
                  variant="ghost"
                  onClick={() => {
                    onCancel();
                  }}
                  title="Hủy"
                  className="text-destructive"
                />
              )}

              {/* Nút thử lại */}
              {task.status === TaskStatus.ERROR &&
                task.retryable &&
                task.retryCount < task.maxRetries &&
                onRetry && (
                  <IconButton
                    icon="refresh-cw"
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      onRetry();
                    }}
                    title="Thử lại"
                    className="text-primary"
                  />
                )}

              {/* Nút xóa */}
              {(task.status === TaskStatus.SUCCESS ||
                task.status === TaskStatus.ERROR ||
                task.status === TaskStatus.CANCELLED) &&
                onRemove && (
                  <IconButton
                    icon="x"
                    size="sm"
                    variant="ghost"
                    onClick={() => {
                      onRemove();
                    }}
                    title="Xóa"
                    className="text-destructive"
                  />
                )}
            </div>
          </div>
        </div>
      </div>

      {/* Chi tiết task (khi mở rộng) */}
      {isExpanded && (
        <div className="px-3 pb-3 pt-0 text-xs text-foreground/80">
          {task.status === TaskStatus.ERROR && task.error && (
            <div className={`mt-2 p-2 rounded ${
              task.error instanceof Error &&
              (task.error.message?.includes('CORS') || task.error.message === 'Network Error')
                ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-500'
                : 'bg-destructive/10 text-destructive'
            }`}>
              <p className="font-medium">
                {task.error instanceof Error &&
                (task.error.message?.includes('CORS') || task.error.message === 'Network Error')
                  ? 'Cảnh báo:'
                  : 'Lỗi:'}
              </p>
              <p>{task.error instanceof Error ? task.error.message : String(task.error)}</p>
              {/* Hiển thị ghi chú nếu là lỗi CORS hoặc Network Error nhưng có thể đã upload thành công */}
              {(task.error instanceof Error &&
                (task.error.message?.includes('CORS') || task.error.message === 'Network Error')) && (
                <p className="mt-2 font-medium">
                  Lưu ý: Mặc dù có lỗi {task.error.message === 'Network Error' ? 'mạng' : 'CORS'},
                  file đã được tải lên thành công. Bạn có thể tiếp tục sử dụng.
                </p>
              )}
            </div>
          )}

          {task.type === TaskType.FILE_UPLOAD && (task as FileUploadTask).file && (
            <div className="mt-2">
              <p>
                <span className="font-medium">Tên file:</span> {(task as FileUploadTask).file?.name || 'Không có tên'}
              </p>
              <p>
                <span className="font-medium">Kích thước:</span>{' '}
                {formatFileSize((task as FileUploadTask).file?.size || 0)}
              </p>
              <p>
                <span className="font-medium">Loại:</span>{' '}
                {(task as FileUploadTask).file?.type || 'Không xác định'}
              </p>
            </div>
          )}

          <div className="mt-2">
            <p>
              <span className="font-medium">Trạng thái:</span> {getStatusText(task.status)}
            </p>
            <p>
              <span className="font-medium">Tạo lúc:</span> {formatDate(task.createdAt)}
            </p>
            {task.startedAt && (
              <p>
                <span className="font-medium">Bắt đầu lúc:</span> {formatDate(task.startedAt)}
              </p>
            )}
            {task.completedAt && (
              <p>
                <span className="font-medium">Hoàn thành lúc:</span> {formatDate(task.completedAt)}
              </p>
            )}
            {task.retryCount > 0 && (
              <p>
                <span className="font-medium">Số lần thử lại:</span> {task.retryCount}/
                {task.maxRetries}
              </p>
            )}
          </div>
        </div>
      )}
    </motion.div>
  );
};

/**
 * Format kích thước file
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) {return '0 Bytes';}

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))  } ${  sizes[i]}`;
};

/**
 * Format ngày giờ
 */
const formatDate = (date: Date): string => {
  return new Intl.DateTimeFormat('vi-VN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).format(date);
};

/**
 * Lấy text cho trạng thái
 */
const getStatusText = (status: TaskStatus): string => {
  switch (status) {
    case TaskStatus.PENDING:
      return 'Đang chờ';
    case TaskStatus.RUNNING:
      return 'Đang thực thi';
    case TaskStatus.SUCCESS:
      return 'Thành công';
    case TaskStatus.ERROR:
      return 'Lỗi';
    case TaskStatus.CANCELLED:
      return 'Đã hủy';
    default:
      return 'Không xác định';
  }
};

export default TaskItem;
