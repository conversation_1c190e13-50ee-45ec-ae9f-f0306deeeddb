import { useQuery } from '@tanstack/react-query';

import { ConversionService } from '../services/conversion.service';
import { ConversionQueryParams } from '../types/conversion.types';

// Đ<PERSON>nh nghĩa các query key
export const CONVERSION_QUERY_KEYS = {
  all: ['conversions'] as const,
  lists: () => [...CONVERSION_QUERY_KEYS.all, 'list'] as const,
  list: (filters: ConversionQueryParams) => [...CONVERSION_QUERY_KEYS.lists(), filters] as const,
  details: () => [...CONVERSION_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...CONVERSION_QUERY_KEYS.details(), id] as const,
};

/**
 * Hook để lấy danh sách bản ghi chuyển đổi
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useConversions = (params?: ConversionQueryParams) => {
  return useQuery({
    queryKey: CONVERSION_QUERY_KEYS.list(params || { page: 1, limit: 10 }),
    queryFn: () => ConversionService.getConverts(params),
  });
};

/**
 * Hook để lấy chi tiết bản ghi chuyển đổi theo ID
 * @param id ID của bản ghi chuyển đổi
 * @returns Query object
 */
export const useConversion = (id: number) => {
  return useQuery({
    queryKey: CONVERSION_QUERY_KEYS.detail(id),
    queryFn: () => ConversionService.getConvertById(id),
    select: (data) => data.result,
    enabled: !!id && id > 0,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};
