import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { KeyResultService } from '../services/key-result.service';
import {
  CreateKeyResultDto,
  KeyResultDto,
  KeyResultQueryDto,
  UpdateKeyResultDto,
} from '../types/key-result.types';

import type { SelectOption } from '@/shared/components/common/Select/Select';

// Key cho React Query
const KEY_RESULTS_QUERY_KEY = 'keyResults';

/**
 * Hook để lấy danh sách key results
 * @param params Tham số truy vấn
 * @returns Query result với danh sách key results
 */
export const useKeyResults = (params?: KeyResultQueryDto) => {
  return useQuery({
    queryKey: [KEY_RESULTS_QUERY_KEY, params],
    queryFn: () => KeyResultService.getKeyResults(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy key results theo objective ID
 * @param objectiveId ID của objective
 * @returns Query result với danh sách key results
 */
export const useKeyResultsByObjective = (objectiveId: number) => {
  return useQuery({
    queryKey: [KEY_RESULTS_QUERY_KEY, 'objective', objectiveId],
    queryFn: () => KeyResultService.getKeyResultsByObjective(objectiveId),
    select: data => data.result,
    enabled: !!objectiveId,
  });
};

/**
 * Hook để lấy chi tiết key result
 * @param id ID key result
 * @returns Query result với chi tiết key result
 */
export const useKeyResult = (id: number) => {
  return useQuery({
    queryKey: [KEY_RESULTS_QUERY_KEY, id],
    queryFn: () => KeyResultService.getKeyResult(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo key result mới
 * @returns Mutation result cho việc tạo key result
 */
export const useCreateKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateKeyResultDto) => KeyResultService.createKeyResult(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật key result
 * @returns Mutation result cho việc cập nhật key result
 */
export const useUpdateKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateKeyResultDto }) =>
      KeyResultService.updateKeyResult(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY] });
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY, variables.id] });
    },
  });
};

/**
 * Hook để xóa key result
 * @returns Mutation result cho việc xóa key result
 */
export const useDeleteKeyResult = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => KeyResultService.deleteKeyResult(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [KEY_RESULTS_QUERY_KEY] });
    },
  });
};

/**
 * Hàm load key results cho AsyncSelectWithPagination
 * @param params Tham số tìm kiếm và phân trang
 * @returns Promise với kết quả phân trang SelectOption
 */
export const loadKeyResultsForAsyncSelect = async (params: {
  search?: string;
  page?: number;
  limit?: number;
  objectiveId?: number; // Thêm filter theo objective
}): Promise<{
  items: SelectOption[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
}> => {
  try {
    const { search = '', page = 1, limit = 20, objectiveId } = params;

    // Gọi API tìm kiếm key results với phân trang
    const response = await KeyResultService.getKeyResults({
      search: search.trim() || undefined,
      page,
      limit,
      objectiveId,
    });

    // Chuyển đổi dữ liệu thành SelectOption
    const items: SelectOption[] = response.result.items.map((keyResult: KeyResultDto) => ({
      value: keyResult.id,
      label: keyResult.title,
      data: keyResult,
    }));

    return {
      items,
      totalItems: response.result.meta?.totalItems || 0,
      totalPages: response.result.meta?.totalPages || 0,
      currentPage: response.result.meta?.currentPage || 1,
    };
  } catch (error) {
    console.error('Error loading key results for select:', error);
    return {
      items: [],
      totalItems: 0,
      totalPages: 0,
      currentPage: 1,
    };
  }
};
