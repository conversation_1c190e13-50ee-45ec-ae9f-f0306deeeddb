import { debounce } from 'lodash';
import { Loader2 } from 'lucide-react';
import React, { useState, useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react';
import { createPortal } from 'react-dom';
import { useTranslation } from 'react-i18next';

import { Icon } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

import SelectOption from './SelectOption';

import type { SelectOption as SelectOptionType } from './Select';

export interface AsyncSelectWithPaginationProps {
  /**
   * Giá trị đã chọn
   */
  value?: string | string[] | number | number[];

  /**
   * Callback khi giá trị thay đổi
   */
  onChange?: (value: string | string[] | number | number[] | undefined) => void;

  /**
   * Hàm load options từ API với pagination
   */
  loadOptions: (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => Promise<{
    items: SelectOptionType[];
    totalItems: number;
    totalPages: number;
    currentPage: number;
  }>;

  /**
   * Options ban đầu để hiển thị giá trị đã chọn
   */
  initialOptions?: SelectOptionType[];

  /**
   * Thời gian debounce cho search (ms)
   */
  debounceTime?: number;

  /**
   * Cho phép chọn nhiều
   */
  multiple?: boolean;

  /**
   * Placeholder
   */
  placeholder?: string;

  /**
   * Label
   */
  label?: string;

  /**
   * Disabled
   */
  disabled?: boolean;

  /**
   * Custom rendering
   */
  renderOption?: (option: SelectOptionType) => React.ReactNode;

  /**
   * Name attribute
   */
  name?: string;

  /**
   * ID attribute
   */
  id?: string;

  /**
   * CSS class
   */
  className?: string;

  /**
   * Error message
   */
  error?: string;

  /**
   * Helper text
   */
  helperText?: string;

  /**
   * Size
   */
  size?: 'sm' | 'md' | 'lg';

  /**
   * Full width
   */
  fullWidth?: boolean;

  /**
   * Số items mỗi trang
   */
  itemsPerPage?: number;

  /**
   * Tự động load trang đầu tiên khi mở
   */
  autoLoadInitial?: boolean;

  /**
   * Message khi không có options
   */
  noOptionsMessage?: string;

  /**
   * Message khi đang loading
   */
  loadingMessage?: string;

  /**
   * Search chỉ khi nhấn Enter
   */
  searchOnEnter?: boolean;
}

/**
 * Component AsyncSelectWithPagination - Select với khả năng tải dữ liệu từ API có phân trang
 */
const AsyncSelectWithPagination = forwardRef<HTMLInputElement, AsyncSelectWithPaginationProps>(
  (
    {
      value,
      onChange,
      loadOptions,
      initialOptions = [],
      debounceTime = 300,
      multiple = false,
      placeholder = '',
      label,
      disabled = false,
      renderOption,
      name,
      id,
      className = '',
      error,
      helperText,
      size = 'md',
      fullWidth = false,
      itemsPerPage = 20,
      autoLoadInitial = true,
      noOptionsMessage,
      loadingMessage,
      searchOnEnter = true,
    },
    ref
  ) => {
    useTheme(); // Keep the hook call to avoid React hooks rules violation
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);
    const [options, setOptions] = useState<SelectOptionType[]>(initialOptions);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [pendingSearchTerm, setPendingSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMore, setHasMore] = useState(false);
    const [selectedValues, setSelectedValues] = useState<(string | number)[]>(
      multiple
        ? Array.isArray(value)
          ? (value as (string | number)[])
          : []
        : value !== undefined
          ? [value as string | number]
          : []
    );
    const [dropdownPosition, setDropdownPosition] = useState<{
      top: number;
      left: number;
      width: number;
    } | null>(null);

    const selectRef = useRef<HTMLDivElement>(null);
    const searchInputRef = useRef<HTMLInputElement>(null);

    // Forward ref to hidden input element
    const hiddenInputRef = useRef<HTMLInputElement>(null);
    useImperativeHandle(ref, () => hiddenInputRef.current as HTMLInputElement);

    // Size classes
    const sizeClasses = {
      sm: 'h-8 text-sm',
      md: 'h-10',
      lg: 'h-12 text-lg',
    }[size];

    // Width class
    const widthClass = fullWidth ? 'w-full' : '';

    // Update selectedValues when value prop changes
    useEffect(() => {
      if (multiple) {
        setSelectedValues(Array.isArray(value) ? (value as (string | number)[]) : []);
      } else {
        setSelectedValues(value !== undefined ? [value as string | number] : []);
      }
    }, [value, multiple]);

    // Update options when initialOptions changes
    useEffect(() => {
      if (initialOptions.length > 0) {
        setOptions(prevOptions => {
          // Merge initial options with existing options, avoiding duplicates
          const existingValues = new Set(prevOptions.map(opt => opt.value));
          const newOptions = initialOptions.filter(opt => !existingValues.has(opt.value));
          return [...prevOptions, ...newOptions];
        });
      }
    }, [initialOptions]);

    // Load data function
    const loadData = useCallback(async (params: {
      search?: string;
      page?: number;
      reset?: boolean;
    }) => {
      const { search = '', page = 1, reset = false } = params;
      
      setLoading(true);
      try {
        const result = await loadOptions({
          search,
          page,
          limit: itemsPerPage,
        });

        if (reset || page === 1) {
          setOptions(result.items);
        } else {
          setOptions(prev => [...prev, ...result.items]);
        }

        setCurrentPage(result.currentPage);
        setHasMore(result.currentPage < result.totalPages);
      } catch (error) {
        console.error('Error loading options:', error);
        if (reset || page === 1) {
          setOptions([]);
        }
      } finally {
        setLoading(false);
      }
    }, [loadOptions, itemsPerPage]);

    // Create debounced search function
    const debouncedSearchRef = useRef<ReturnType<typeof debounce>>();

    useEffect(() => {
      debouncedSearchRef.current = debounce((search: string) => {
        if (!searchOnEnter) {
          setSearchTerm(search);
          setCurrentPage(1);
          loadData({ search, page: 1, reset: true });
        }
      }, debounceTime);

      return () => {
        debouncedSearchRef.current?.cancel();
      };
    }, [loadData, debounceTime, searchOnEnter]);

    // Handle search input change
    const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      setPendingSearchTerm(value);

      if (!searchOnEnter && debouncedSearchRef.current) {
        debouncedSearchRef.current(value);
      }
    };

    // Handle search on Enter
    const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === 'Enter' && searchOnEnter) {
        e.preventDefault();
        setSearchTerm(pendingSearchTerm);
        setCurrentPage(1);
        loadData({ search: pendingSearchTerm, page: 1, reset: true });
      }
    };

    // Load more data when scrolling
    const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      
      if (
        scrollHeight - scrollTop <= clientHeight + 50 && // 50px threshold
        hasMore &&
        !loading
      ) {
        loadData({ search: searchTerm, page: currentPage + 1 });
      }
    }, [hasMore, loading, searchTerm, currentPage, loadData]);

    // Calculate dropdown position when opening
    const calculateDropdownPosition = useCallback(() => {
      if (!selectRef.current) {return null;}

      const rect = selectRef.current.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      return {
        top: rect.bottom + scrollTop + 4, // 4px gap
        left: rect.left + scrollLeft,
        width: rect.width,
      };
    }, []);



    // Handle option click
    const handleOptionClick = (optionValue: string | number) => {
      let newSelectedValues: (string | number)[];

      if (multiple) {
        // Toggle selection for multiple select
        if (selectedValues.includes(optionValue)) {
          newSelectedValues = selectedValues.filter(val => val !== optionValue);
        } else {
          newSelectedValues = [...selectedValues, optionValue];
        }
      } else {
        // Single select - thêm tính năng bỏ chọn
        if (selectedValues.includes(optionValue)) {
          // Nếu đang chọn option hiện tại thì bỏ chọn (deselect)
          newSelectedValues = [];
        } else {
          // Chọn option mới
          newSelectedValues = [optionValue];
        }
        setIsOpen(false); // Close dropdown for single select
      }

      setSelectedValues(newSelectedValues);

      // Call onChange with the new value(s)
      if (onChange) {
        if (multiple) {
          onChange(newSelectedValues as string[] | number[]);
        } else {
          // Trả về undefined khi bỏ chọn, hoặc giá trị khi chọn
          onChange(newSelectedValues.length > 0 ? newSelectedValues[0] : undefined);
        }
      }

      // Clear search input when option is selected
      setPendingSearchTerm('');
    };

    // Get display value
    const getDisplayValue = () => {
      if (selectedValues.length === 0) {return placeholder;}

      if (multiple) {
        if (selectedValues.length === 1) {
          const selectedOption = options.find(opt => opt.value === selectedValues[0]);
          return selectedOption ? selectedOption.label : '';
        } else {
          return t('common.selected', { count: selectedValues.length });
        }
      } else {
        const selectedOption = options.find(opt => opt.value === selectedValues[0]);
        return selectedOption ? selectedOption.label : '';
      }
    };

    // Render single option
    const renderSingleOption = (option: SelectOptionType) => {
      const isSelected = selectedValues.includes(option.value);

      if (renderOption) {
        return (
          <div
            key={`option-${option.value}`}
            onClick={() => !option.disabled && handleOptionClick(option.value)}
            className={`${option.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
          >
            {renderOption(option)}
          </div>
        );
      }

      return (
        <SelectOption
          key={`option-${option.value}`}
          value={option.value}
          label={option.label}
          icon={option.icon}
          disabled={option.disabled}
          selected={isSelected}
          onClick={() => handleOptionClick(option.value)}
          data={option.data}
        />
      );
    };

    // Close dropdown when clicking outside and handle scroll
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        const target = event.target as Node;

        // Check if click is outside both select trigger and dropdown
        const isOutsideSelect = selectRef.current && !selectRef.current.contains(target);
        const isOutsideDropdown = !document.querySelector('.async-select-dropdown')?.contains(target);

        if (isOutsideSelect && isOutsideDropdown) {
          setIsOpen(false);
        }
      };

      const handleScroll = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      const handleResize = () => {
        if (isOpen) {
          const position = calculateDropdownPosition();
          setDropdownPosition(position);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true);
      window.addEventListener('resize', handleResize);

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
        window.removeEventListener('scroll', handleScroll, true);
        window.removeEventListener('resize', handleResize);
      };
    }, [isOpen, calculateDropdownPosition]);

    // Focus input when dropdown opens and calculate position
    useEffect(() => {
      if (isOpen) {
        if (searchInputRef.current) {
          searchInputRef.current.focus();
        }
        const position = calculateDropdownPosition();
        setDropdownPosition(position);

        // Load initial data if needed
        if (autoLoadInitial && options.length === 0) {
          loadData({ search: '', page: 1, reset: true });
        }
      } else {
        setDropdownPosition(null);
      }
    }, [isOpen, calculateDropdownPosition, autoLoadInitial, options.length, loadData]);



    return (
      <div className={`relative ${widthClass} ${className}`} ref={selectRef}>
        {/* Hidden input for form submission */}
        <input
          type="hidden"
          name={name}
          id={id}
          value={multiple ? selectedValues.join(',') : selectedValues[0] || ''}
          ref={hiddenInputRef}
        />

        {/* Label */}
        {label && <label className="block text-sm font-medium mb-1">{label}</label>}

        {/* Select trigger */}
        <div
          className={`
          flex items-center justify-between px-3
          border-0 rounded-md bg-card-muted text-foreground
          ${sizeClasses}
          ${disabled ? 'opacity-60 cursor-not-allowed' : 'cursor-pointer'}
          ${error ? 'ring-1 ring-error' : ''}
          ${isOpen ? 'ring-2 ring-primary/30' : ''}
          ${fullWidth ? 'w-full' : ''}
        `}
          onClick={() => !disabled && setIsOpen(!isOpen)}
        >
          <div className="flex-grow truncate">{getDisplayValue()}</div>

          <div className="flex items-center">
            {loading ? (
              <Icon name="loading" className="animate-spin" size="sm" />
            ) : (
              <svg
                className={`w-4 h-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            )}
          </div>
        </div>

        {/* Error message */}
        {error && (
          <p className="mt-1 text-sm text-destructive">{error}</p>
        )}

        {/* Helper text */}
        {helperText && !error && (
          <p className="mt-1 text-sm text-muted-foreground">{helperText}</p>
        )}

        {/* Dropdown Portal */}
        {isOpen && dropdownPosition && createPortal(
          <div
            className="async-select-dropdown fixed z-[99999] bg-card rounded-md shadow-lg border-0 animate-fade-in"
            style={{
              top: `${dropdownPosition.top}px`,
              left: `${dropdownPosition.left}px`,
              width: `${dropdownPosition.width}px`,
              maxHeight: '300px',
            }}
          >
            {/* Search input */}
            <div className="sticky top-0 p-2 bg-card border-b-0">
              <input
                ref={searchInputRef}
                type="text"
                value={pendingSearchTerm}
                onChange={handleSearchInputChange}
                onKeyDown={handleSearchKeyDown}
                placeholder={
                  searchOnEnter
                    ? t('common.searchPressEnter', 'Tìm kiếm... (Nhấn Enter)')
                    : t('common.search', 'Tìm kiếm...')
                }
                className="w-full px-3 py-1 text-sm border-0 rounded-md focus:outline-none focus:ring-1 focus:ring-primary/20 bg-card-muted text-foreground"
                onClick={e => e.stopPropagation()}
              />
            </div>

            {/* Options */}
            <div
              className="max-h-60 overflow-auto custom-scrollbar auto-hide"
              role="listbox"
              aria-multiselectable={multiple}
              onScroll={handleScroll}
            >
              {loading && options.length === 0 ? (
                <div className="px-4 py-2 text-sm text-muted flex items-center">
                  <Icon name="loading" className="animate-spin mr-2" size="sm" />
                  {loadingMessage || t('common.loading', 'Loading...')}
                </div>
              ) : options.length > 0 ? (
                <>
                  {options.map(option => renderSingleOption(option))}

                  {/* Loading more indicator */}
                  {loading && options.length > 0 && (
                    <div className="flex items-center justify-center p-2 border-t">
                      <Loader2 size={14} className="animate-spin mr-2" />
                      <span className="text-xs text-muted-foreground">
                        {t('common.loadingMore', 'Đang tải thêm...')}
                      </span>
                    </div>
                  )}
                </>
              ) : (
                <div className="px-4 py-2 text-sm text-muted">
                  {noOptionsMessage || t('common.noResults', 'No results found')}
                </div>
              )}
            </div>
          </div>,
          document.body
        )}
      </div>
    );
  }
);

AsyncSelectWithPagination.displayName = 'AsyncSelectWithPagination';

export default AsyncSelectWithPagination;
