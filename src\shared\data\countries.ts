/**
 * D<PERSON> liệu quốc gia với mã điện thoại và cờ
 */
export interface Country {
  code: string; // Mã quốc gia ISO 2 chữ cái
  name: string; // Tên quốc gia
  dialCode: string; // Mã vùng điện thoại
  flag: string; // Emoji cờ quốc gia
  svgFlag?: string; // URL SVG flag từ flagcdn.com
}

export const COUNTRIES: Country[] = [
  { code: 'VN', name: 'Vietnam', dialCode: '+84', flag: '🇻🇳', svgFlag: 'https://flagcdn.com/vn.svg' },
  { code: 'US', name: 'United States', dialCode: '+1', flag: '🇺🇸', svgFlag: 'https://flagcdn.com/us.svg' },
  { code: 'GB', name: 'United Kingdom', dialCode: '+44', flag: '🇬🇧', svgFlag: 'https://flagcdn.com/gb.svg' },
  { code: 'C<PERSON>', name: 'China', dialCode: '+86', flag: '🇨🇳', svgFlag: 'https://flagcdn.com/cn.svg' },
  { code: 'JP', name: 'Japan', dialCode: '+81', flag: '🇯🇵', svgFlag: 'https://flagcdn.com/jp.svg' },
  { code: 'KR', name: 'South Korea', dialCode: '+82', flag: '🇰🇷', svgFlag: 'https://flagcdn.com/kr.svg' },
  { code: 'TH', name: 'Thailand', dialCode: '+66', flag: '🇹🇭', svgFlag: 'https://flagcdn.com/th.svg' },
  { code: 'SG', name: 'Singapore', dialCode: '+65', flag: '🇸🇬', svgFlag: 'https://flagcdn.com/sg.svg' },
  { code: 'MY', name: 'Malaysia', dialCode: '+60', flag: '🇲🇾', svgFlag: 'https://flagcdn.com/my.svg' },
  { code: 'ID', name: 'Indonesia', dialCode: '+62', flag: '🇮🇩', svgFlag: 'https://flagcdn.com/id.svg' },
  { code: 'PH', name: 'Philippines', dialCode: '+63', flag: '🇵🇭', svgFlag: 'https://flagcdn.com/ph.svg' },
  { code: 'IN', name: 'India', dialCode: '+91', flag: '🇮🇳', svgFlag: 'https://flagcdn.com/in.svg' },
  { code: 'AU', name: 'Australia', dialCode: '+61', flag: '🇦🇺', svgFlag: 'https://flagcdn.com/au.svg' },
  { code: 'CA', name: 'Canada', dialCode: '+1', flag: '🇨🇦', svgFlag: 'https://flagcdn.com/ca.svg' },
  { code: 'DE', name: 'Germany', dialCode: '+49', flag: '🇩🇪', svgFlag: 'https://flagcdn.com/de.svg' },
  { code: 'FR', name: 'France', dialCode: '+33', flag: '🇫🇷', svgFlag: 'https://flagcdn.com/fr.svg' },
  { code: 'IT', name: 'Italy', dialCode: '+39', flag: '🇮🇹', svgFlag: 'https://flagcdn.com/it.svg' },
  { code: 'ES', name: 'Spain', dialCode: '+34', flag: '🇪🇸', svgFlag: 'https://flagcdn.com/es.svg' },
  { code: 'RU', name: 'Russia', dialCode: '+7', flag: '🇷🇺', svgFlag: 'https://flagcdn.com/ru.svg' },
  { code: 'BR', name: 'Brazil', dialCode: '+55', flag: '🇧🇷', svgFlag: 'https://flagcdn.com/br.svg' },
  { code: 'MX', name: 'Mexico', dialCode: '+52', flag: '🇲🇽', svgFlag: 'https://flagcdn.com/mx.svg' },
  { code: 'AR', name: 'Argentina', dialCode: '+54', flag: '🇦🇷', svgFlag: 'https://flagcdn.com/ar.svg' },
  { code: 'CL', name: 'Chile', dialCode: '+56', flag: '🇨🇱', svgFlag: 'https://flagcdn.com/cl.svg' },
  { code: 'CO', name: 'Colombia', dialCode: '+57', flag: '🇨🇴', svgFlag: 'https://flagcdn.com/co.svg' },
  { code: 'PE', name: 'Peru', dialCode: '+51', flag: '🇵🇪', svgFlag: 'https://flagcdn.com/pe.svg' },
  { code: 'EG', name: 'Egypt', dialCode: '+20', flag: '🇪🇬', svgFlag: 'https://flagcdn.com/eg.svg' },
  { code: 'ZA', name: 'South Africa', dialCode: '+27', flag: '🇿🇦', svgFlag: 'https://flagcdn.com/za.svg' },
  { code: 'NG', name: 'Nigeria', dialCode: '+234', flag: '🇳🇬', svgFlag: 'https://flagcdn.com/ng.svg' },
  { code: 'KE', name: 'Kenya', dialCode: '+254', flag: '🇰🇪', svgFlag: 'https://flagcdn.com/ke.svg' },
  { code: 'GH', name: 'Ghana', dialCode: '+233', flag: '🇬🇭', svgFlag: 'https://flagcdn.com/gh.svg' },
  { code: 'AF', name: 'Afghanistan', dialCode: '+93', flag: '🇦🇫', svgFlag: 'https://flagcdn.com/af.svg' },
  { code: 'AX', name: 'Aland Islands', dialCode: '+358', flag: '🇦🇽', svgFlag: 'https://flagcdn.com/ax.svg' },
  { code: 'AL', name: 'Albania', dialCode: '+355', flag: '🇦🇱', svgFlag: 'https://flagcdn.com/al.svg' },
  { code: 'DZ', name: 'Algeria', dialCode: '+213', flag: '🇩🇿', svgFlag: 'https://flagcdn.com/dz.svg' },
  { code: 'AS', name: 'American Samoa', dialCode: '+1', flag: '🇦🇸', svgFlag: 'https://flagcdn.com/as.svg' },
  { code: 'AD', name: 'Andorra', dialCode: '+376', flag: '🇦🇩', svgFlag: 'https://flagcdn.com/ad.svg' },
  { code: 'AO', name: 'Angola', dialCode: '+244', flag: '🇦🇴', svgFlag: 'https://flagcdn.com/ao.svg' },
  { code: 'AI', name: 'Anguilla', dialCode: '+1', flag: '🇦🇮', svgFlag: 'https://flagcdn.com/ai.svg' },
];

/**
 * Tìm quốc gia theo mã
 */
export const findCountryByCode = (code: string): Country | undefined => {
  return COUNTRIES.find(country => country.code === code);
};

/**
 * Tìm quốc gia theo mã điện thoại
 */
export const findCountryByDialCode = (dialCode: string): Country | undefined => {
  return COUNTRIES.find(country => country.dialCode === dialCode);
};

/**
 * Lọc quốc gia theo từ khóa tìm kiếm
 */
export const filterCountries = (searchTerm: string): Country[] => {
  if (!searchTerm) {return COUNTRIES;}
  
  const term = searchTerm.toLowerCase();
  return COUNTRIES.filter(country => 
    country.name.toLowerCase().includes(term) ||
    country.dialCode.includes(term) ||
    country.code.toLowerCase().includes(term)
  );
};

/**
 * Định dạng số điện thoại
 */
export const formatPhoneNumber = (phoneNumber: string, country: Country): string => {
  if (!phoneNumber) {return '';}
  
  // Loại bỏ tất cả ký tự không phải số
  const cleanNumber = phoneNumber.replace(/\D/g, '');
  
  // Nếu số bắt đầu bằng mã vùng, loại bỏ nó
  const dialCodeDigits = country.dialCode.replace(/\D/g, '');
  let numberWithoutDialCode = cleanNumber;
  
  if (cleanNumber.startsWith(dialCodeDigits)) {
    numberWithoutDialCode = cleanNumber.slice(dialCodeDigits.length);
  }
  
  return numberWithoutDialCode;
};

/**
 * Lấy số điện thoại đầy đủ với mã vùng
 */
export const getFullPhoneNumber = (phoneNumber: string, country: Country): string => {
  const formattedNumber = formatPhoneNumber(phoneNumber, country);
  if (!formattedNumber) {return '';}

  return `${country.dialCode}${formattedNumber}`;
};

/**
 * Kiểm tra xem emoji flag có hiển thị được không
 */
export const isEmojiSupported = (): boolean => {
  if (typeof window === 'undefined') {return false;}

  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (!ctx) {return false;}

  canvas.width = 20;
  canvas.height = 20;

  ctx.fillText('🇺🇸', 0, 15);
  const imageData = ctx.getImageData(0, 0, 20, 20);

  // Kiểm tra xem có pixel nào được vẽ không
  for (let i = 0; i < imageData.data.length; i += 4) {
    if (imageData.data[i + 3] > 0) {return true;}
  }

  return false;
};

/**
 * Lấy display text cho country flag
 */
export const getCountryFlagDisplay = (country: Country): string => {
  return country.flag || country.code;
};

/**
 * Lấy SVG flag URL cho quốc gia
 */
export const getCountrySvgFlag = (countryCode: string, size: 'w20' | 'w40' | 'w80' | 'w160' = 'w40'): string => {
  return `https://flagcdn.com/${size}/${countryCode.toLowerCase()}.png`;
};

/**
 * Lấy SVG flag URL vector cho quốc gia
 */
export const getCountrySvgFlagVector = (countryCode: string): string => {
  return `https://flagcdn.com/${countryCode.toLowerCase()}.svg`;
};
