/**
 * Video Message Component
 * Renders video content with controls and preview
 */

import React, { useState, useRef } from 'react';

import { Typography, Button, Icon } from '@/shared/components/common';
import { VideoContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface VideoMessageProps {
  data: VideoContentData;
  className?: string;
}

/**
 * Video Message Component
 */
const VideoMessage: React.FC<VideoMessageProps> = ({ data, className = '' }) => {
  const {
    url,
    thumbnail,
    title,
    duration,
    autoplay = false,
    controls = true,
    muted = false,
    loop = false,
    width,
    height,
  } = data;

  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [videoLoaded, setVideoLoaded] = useState(false);
  const [videoError, setVideoError] = useState(false);
  const [showControls, setShowControls] = useState(false);

  // Format duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle play/pause
  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
    }
  };

  // Handle video events
  const handleVideoLoad = () => {
    setVideoLoaded(true);
    setVideoError(false);
  };

  const handleVideoError = () => {
    setVideoError(true);
    setVideoLoaded(false);
  };

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handlePause = () => {
    setIsPlaying(false);
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  // Handle seek
  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = parseFloat(e.target.value);
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  // Render video placeholder
  const renderPlaceholder = () => (
    <div className="flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg p-8 aspect-video">
      <div className="text-center">
        <Icon name="video" size="lg" className="text-gray-400 mb-2" />
        <Typography variant="caption" className="text-gray-500">
          {videoError ? 'Failed to load video' : 'Loading video...'}
        </Typography>
      </div>
    </div>
  );

  // Render custom controls
  const renderCustomControls = () => {
    if (!showControls || !videoLoaded) {return null;}

    const videoDuration = videoRef.current?.duration || duration || 0;

    return (
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
        {/* Progress bar */}
        <div className="mb-3">
          <input
            type="range"
            min="0"
            max={videoDuration}
            value={currentTime}
            onChange={handleSeek}
            className="w-full h-1 bg-white/30 rounded-lg appearance-none cursor-pointer"
          />
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Play/Pause button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handlePlayPause}
              className="text-white hover:bg-white/20 p-2"
            >
              <Icon name={isPlaying ? 'pause' : 'play'} size="sm" />
            </Button>

            {/* Time display */}
            <Typography variant="caption" className="text-white">
              {formatDuration(currentTime)} / {formatDuration(videoDuration)}
            </Typography>
          </div>

          {/* Volume and fullscreen controls */}
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (videoRef.current) {
                  videoRef.current.muted = !videoRef.current.muted;
                }
              }}
              className="text-white hover:bg-white/20 p-2"
            >
              <Icon name="volume-2" size="sm" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                if (videoRef.current) {
                  videoRef.current.requestFullscreen();
                }
              }}
              className="text-white hover:bg-white/20 p-2"
            >
              <Icon name="maximize" size="sm" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={`rounded-lg overflow-hidden bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Video container */}
      <div 
        className="relative group"
        onMouseEnter={() => setShowControls(true)}
        onMouseLeave={() => setShowControls(false)}
      >
        {!videoLoaded && !videoError && renderPlaceholder()}
        
        {!videoError && (
          <video
            ref={videoRef}
            src={url}
            poster={thumbnail}
            autoPlay={autoplay}
            controls={controls}
            muted={muted}
            loop={loop}
            width={width}
            height={height}
            className={`w-full h-auto ${videoLoaded ? 'block' : 'hidden'}`}
            style={{ maxHeight: '400px' }}
            onLoadedData={handleVideoLoad}
            onError={handleVideoError}
            onPlay={handlePlay}
            onPause={handlePause}
            onTimeUpdate={handleTimeUpdate}
          />
        )}

        {videoError && renderPlaceholder()}

        {/* Custom controls overlay */}
        {!controls && renderCustomControls()}

        {/* Play button overlay for thumbnail */}
        {!isPlaying && thumbnail && videoLoaded && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Button
              variant="ghost"
              size="lg"
              onClick={handlePlayPause}
              className="bg-black/50 hover:bg-black/70 text-white rounded-full p-4"
            >
              <Icon name="play" size="lg" />
            </Button>
          </div>
        )}
      </div>

      {/* Video info */}
      {(title || duration) && (
        <div className="p-3">
          {title && (
            <Typography variant="body2" className="font-medium text-gray-900 dark:text-gray-100 mb-1">
              {title}
            </Typography>
          )}
          
          <div className="flex items-center justify-between">
            {duration && (
              <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                Duration: {formatDuration(duration)}
              </Typography>
            )}
            
            {(width && height) && (
              <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                {width}×{height}
              </Typography>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoMessage;
