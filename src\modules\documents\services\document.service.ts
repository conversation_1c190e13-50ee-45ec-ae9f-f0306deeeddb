import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import * as DocumentAPI from '../api/document.api';
import {
  DocumentResponseDto,
  DocumentWithDownloadUrlResponseDto,
  DocumentQueryDto,
  DocumentSearchDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  CreateUploadUrlDto,
  UploadUrlResponseDto,
  ConfirmUploadDto,
} from '../types/document.types';

/**
 * Document Services Layer
 * Business logic for document operations
 */

/**
 * Lấy danh sách tài liệu với logic nghiệp vụ
 */
export const getDocumentsWithBusinessLogic = async (
  params?: DocumentQueryDto
): Promise<PaginatedResult<DocumentResponseDto>> => {
  // Thiết lập giá trị mặc định
  const defaultParams: DocumentQueryDto = {
    page: 1,
    limit: 10,
    sortBy: 'createdAt',
    sortDirection: 'DESC',
    isActive: true,
    ...params,
  };

  // Validate limit
  if (defaultParams.limit && defaultParams.limit > 100) {
    throw new Error('Limit cannot exceed 100');
  }

  // Validate page
  if (defaultParams.page && defaultParams.page < 1) {
    defaultParams.page = 1;
  }

  const response = await DocumentAPI.getDocuments(defaultParams);
  return response.result;
};

/**
 * Tìm kiếm tài liệu với logic nghiệp vụ
 */
export const searchDocumentsWithBusinessLogic = async (
  params: DocumentSearchDto
): Promise<PaginatedResult<DocumentResponseDto>> => {
  // Validate search text
  if (!params.searchText || params.searchText.trim().length < 2) {
    throw new Error('Search text must be at least 2 characters');
  }

  // Thiết lập giá trị mặc định
  const defaultParams: DocumentSearchDto = {
    page: 1,
    limit: 10,
    ...params,
    searchText: params.searchText.trim(),
  };

  const response = await DocumentAPI.searchDocuments(defaultParams);
  return response.result;
};

/**
 * Lấy chi tiết tài liệu với logic nghiệp vụ
 */
export const getDocumentByIdWithBusinessLogic = async (
  id: number
): Promise<DocumentResponseDto | null> => {
  if (!id || id <= 0) {
    throw new Error('Invalid document ID');
  }

  const response = await DocumentAPI.getDocumentById(id);
  return response.result;
};

/**
 * Lấy URL download với logic nghiệp vụ
 */
export const getDocumentDownloadUrlWithBusinessLogic = async (
  id: number
): Promise<DocumentWithDownloadUrlResponseDto> => {
  if (!id || id <= 0) {
    throw new Error('Invalid document ID');
  }

  const response = await DocumentAPI.getDocumentDownloadUrl(id);
  return response.result;
};

/**
 * Tạo tài liệu mới với logic nghiệp vụ
 */
export const createDocumentWithBusinessLogic = async (
  data: CreateDocumentDto
): Promise<DocumentResponseDto> => {
  // Validate required fields
  if (!data.title || data.title.trim().length === 0) {
    throw new Error('Title is required');
  }

  if (!data.fileName || data.fileName.trim().length === 0) {
    throw new Error('File name is required');
  }

  if (!data.documentType) {
    throw new Error('Document type is required');
  }

  if (!data.s3Key || data.s3Key.trim().length === 0) {
    throw new Error('S3 key is required');
  }

  // Validate file size (max 100MB)
  if (data.fileSize > 100 * 1024 * 1024) {
    throw new Error('File size cannot exceed 100MB');
  }

  // Set default values
  const createData: CreateDocumentDto = {
    ...data,
    title: data.title.trim(),
    fileName: data.fileName.trim(),
    language: data.language || 'vi',
    isPublic: data.isPublic || false,
  };

  const response = await DocumentAPI.createDocument(createData);
  return response.result;
};

/**
 * Cập nhật tài liệu với logic nghiệp vụ
 */
export const updateDocumentWithBusinessLogic = async (
  id: number,
  data: UpdateDocumentDto
): Promise<DocumentResponseDto> => {
  if (!id || id <= 0) {
    throw new Error('Invalid document ID');
  }

  // Validate title if provided
  if (data.title !== undefined && data.title.trim().length === 0) {
    throw new Error('Title cannot be empty');
  }

  // Clean up data
  const updateData: UpdateDocumentDto = { ...data };
  if (updateData.title) {
    updateData.title = updateData.title.trim();
  }

  const response = await DocumentAPI.updateDocument(id, updateData);
  return response.result;
};

/**
 * Xóa tài liệu với logic nghiệp vụ
 */
export const deleteDocumentWithBusinessLogic = async (id: number): Promise<void> => {
  if (!id || id <= 0) {
    throw new Error('Invalid document ID');
  }

  await DocumentAPI.deleteDocument(id);
};

/**
 * Xóa nhiều tài liệu với logic nghiệp vụ
 */
export const bulkDeleteDocumentsWithBusinessLogic = async (ids: number[]): Promise<void> => {
  if (!ids || ids.length === 0) {
    throw new Error('No document IDs provided');
  }

  // Validate all IDs
  const invalidIds = ids.filter(id => !id || id <= 0);
  if (invalidIds.length > 0) {
    throw new Error('Invalid document IDs found');
  }

  // Limit bulk delete to 50 items
  if (ids.length > 50) {
    throw new Error('Cannot delete more than 50 documents at once');
  }

  await DocumentAPI.bulkDeleteDocuments(ids);
};

/**
 * Tạo URL upload với logic nghiệp vụ
 */
export const createUploadUrlWithBusinessLogic = async (
  data: CreateUploadUrlDto
): Promise<UploadUrlResponseDto> => {
  // Validate required fields
  if (!data.fileName || data.fileName.trim().length === 0) {
    throw new Error('File name is required');
  }

  if (!data.mimeType || data.mimeType.trim().length === 0) {
    throw new Error('MIME type is required');
  }

  if (!data.documentType) {
    throw new Error('Document type is required');
  }

  // Validate file size (max 100MB)
  if (data.fileSize > 100 * 1024 * 1024) {
    throw new Error('File size cannot exceed 100MB');
  }

  // Validate file type
  const allowedMimeTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
    'text/csv',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/png',
    'image/gif',
  ];

  if (!allowedMimeTypes.includes(data.mimeType)) {
    throw new Error('File type not supported');
  }

  const response = await DocumentAPI.createUploadUrl(data);
  return response.result;
};

/**
 * Xác nhận upload với logic nghiệp vụ
 */
export const confirmUploadWithBusinessLogic = async (
  data: ConfirmUploadDto
): Promise<DocumentResponseDto> => {
  if (!data.documentId || data.documentId <= 0) {
    throw new Error('Invalid document ID');
  }

  const response = await DocumentAPI.confirmUpload(data);
  return response.result;
};

/**
 * Lấy thống kê tài liệu
 */
export const getDocumentStatsWithBusinessLogic = async () => {
  const response = await DocumentAPI.getDocumentStats();
  return response.result;
};
