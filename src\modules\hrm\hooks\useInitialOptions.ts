import { useEffect, useState } from 'react';

import { loadDepartmentById } from './useDepartments';
import { loadEmployeeById } from './useEmployees';

import type { SelectOption } from '@/shared/components/common/Select/Select';

/**
 * Hook để load initial options cho AsyncSelectWithPagination trong form edit
 */
export const useInitialOptions = (data: {
  departmentId?: number | null;
  managerId?: number | null;
}) => {
  const [departmentOptions, setDepartmentOptions] = useState<SelectOption[]>([]);
  const [managerOptions, setManagerOptions] = useState<SelectOption[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadOptions = async () => {
      if (!data.departmentId && !data.managerId) {return;}

      setLoading(true);
      try {
        const promises: Promise<SelectOption | null>[] = [];
        
        if (data.departmentId) {
          promises.push(loadDepartmentById(data.departmentId));
        }
        
        if (data.managerId) {
          promises.push(loadEmployeeById(data.managerId));
        }

        const results = await Promise.all(promises);
        
        let departmentOption: SelectOption | null = null;
        let managerOption: SelectOption | null = null;
        
        if (data.departmentId && data.managerId) {
          [departmentOption, managerOption] = results;
        } else if (data.departmentId) {
          [departmentOption] = results;
        } else if (data.managerId) {
          [managerOption] = results;
        }

        if (departmentOption) {
          setDepartmentOptions([departmentOption]);
        }
        
        if (managerOption) {
          setManagerOptions([managerOption]);
        }
      } catch (error) {
        console.error('Error loading initial options:', error);
      } finally {
        setLoading(false);
      }
    };

    loadOptions();
  }, [data.departmentId, data.managerId]);

  return {
    departmentOptions,
    managerOptions,
    loading,
  };
};
