import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Typography,
  Select,
  RatingSelector,
  AsyncSelectWithPagination
} from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';

import { TodoService, ScoreTodoDto } from '../../services/todoService';
import { EmployeeService } from '@/modules/hrm/services/employee.service';
import { EmployeeQueryDto } from '@/modules/hrm/types/employee.types';
import { TaskDto, TaskStatus, TaskPriority } from '../../types/task.types';

interface TaskDetailSidebarProps {
  task: TaskDto;
  onUpdateStatus: (status: TaskStatus) => void;
  onUpdatePriority: (priority: TaskPriority) => void;
  onUpdateAssignee: (assigneeId: string) => void;
  onUpdateExpectedStars: (stars: number) => void;
  onRefresh?: () => void;
}

/**
 * Task detail sidebar component
 */
const TaskDetailSidebar: React.FC<TaskDetailSidebarProps> = ({
  task,
  onUpdateStatus,
  onUpdatePriority,
  onUpdateAssignee,
  onUpdateExpectedStars,
  onRefresh,
}) => {
  const { t } = useTranslation(['todolist']);

  // Handle score todo (chấm điểm sao)
  const handleScoreTodo = async (stars: number) => {
    try {
      const scoreData: ScoreTodoDto = {
        awardedStars: stars,
        feedback: `Chấm điểm ${stars} sao cho công việc: ${task.title}`,
      };

      await TodoService.scoreTodo(task.id, scoreData);

      NotificationUtil.success({
        message: t('todolist:task.notifications.scoreSuccess', 'Chấm điểm thành công'),
      });

      // Refresh task data
      if (onRefresh) {
        onRefresh();
      }
    } catch (error) {
      console.error('Error scoring todo:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.scoreError', 'Có lỗi xảy ra khi chấm điểm'),
      });
    }
  };

  // Status options for Select
  const statusOptions = useMemo(() => [
    { value: TaskStatus.PENDING, label: t('todolist:task.status.todo', 'To Do') },
    { value: TaskStatus.IN_PROGRESS, label: t('todolist:task.status.inProgress', 'In Progress') },
    { value: TaskStatus.COMPLETED, label: t('todolist:task.status.done', 'Done') },
    { value: TaskStatus.REJECTED, label: t('todolist:task.status.cancelled', 'Cancelled') },
  ], [t]);

  // Priority options for Select
  const priorityOptions = useMemo(() => [
    { value: TaskPriority.LOW, label: t('todolist:task.priority.low', 'Low') },
    { value: TaskPriority.MEDIUM, label: t('todolist:task.priority.medium', 'Medium') },
    { value: TaskPriority.HIGH, label: t('todolist:task.priority.high', 'High') },
    { value: TaskPriority.URGENT, label: t('todolist:task.priority.urgent', 'Urgent') },
  ], [t]);

  // Load employees for assignee selection
  const loadEmployees = async ({ search, page = 1, limit = 20 }) => {
    try {
      const query: EmployeeQueryDto = {
        page,
        limit,
        search,
      };

      const response = await EmployeeService.getEmployees(query);

      // Transform API response to AsyncSelectWithPagination format
      const transformedItems = response.result.items.map(employee => ({
        value: employee.id.toString(),
        label: `${employee.employeeName} - ${employee.department?.name || 'N/A'}`,
        data: {
          employeeName: employee.employeeName,
          employeeCode: employee.employeeCode,
          departmentName: employee.department?.name,
          jobTitle: employee.jobTitle,
        }
      }));

      return {
        items: transformedItems,
        totalItems: response.result.meta.totalItems,
        totalPages: response.result.meta.totalPages,
        currentPage: response.result.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading users:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };

  return (
    <div className="p-4">
      <Typography variant="h6" className="mb-4">
        {t('todolist:task.details.title', 'Task Details')}
      </Typography>

      <div className="space-y-6">
        {/* Status */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 dark:text-gray-400 mb-2">
            {t('todolist:task.fields.status', 'Status')}
          </Typography>
          <Select
            value={task.status}
            onChange={(value) => onUpdateStatus(value as TaskStatus)}
            options={statusOptions}
            placeholder={t('todolist:task.selectStatus', 'Select status')}
            fullWidth
          />
        </div>

        {/* Priority */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 dark:text-gray-400 mb-2">
            {t('todolist:task.fields.priority', 'Priority')}
          </Typography>
          <Select
            value={task.priority}
            onChange={(value) => onUpdatePriority(value as TaskPriority)}
            options={priorityOptions}
            placeholder={t('todolist:task.selectPriority', 'Select priority')}
            fullWidth
          />
        </div>

        {/* Assignee */}
        <div>
          <Typography variant="subtitle2" className="text-gray-500 dark:text-gray-400 mb-2">
            {t('todolist:task.fields.assignee', 'Assignee')}
          </Typography>
          <AsyncSelectWithPagination
            value={task.assigneeId?.toString()}
            onChange={(value) => {
              if (value) {
                onUpdateAssignee(value.toString());
              }
            }}
            loadOptions={loadEmployees}
            placeholder={t('todolist:task.searchAssignee', 'Search for employee...')}
            fullWidth
          />
        </div>

        {/* Expected Stars */}
        <RatingSelector
          value={task.expectedStars || 0}
          onChange={onUpdateExpectedStars}
          label={t('todolist:task.fields.expectedStars', 'Expected Stars')}
          size="lg"
          interactive={true}
        />

        {/* Awarded Stars (chỉ hiển thị nếu task đã hoàn thành) */}
        {task.status === TaskStatus.COMPLETED && (
          <RatingSelector
            value={task.awardedStars || 0}
            onChange={handleScoreTodo}
            label={t('todolist:task.fields.awardedStars', 'Awarded Stars')}
            size="lg"
            interactive={true}
          />
        )}

        {/* Awarded Stars (read-only nếu chưa hoàn thành nhưng đã có điểm) */}
        {task.status !== TaskStatus.COMPLETED && task.awardedStars && (
          <RatingSelector
            value={task.awardedStars}
            onChange={() => {}} // Read-only
            label={t('todolist:task.fields.awardedStars', 'Awarded Stars')}
            size="lg"
            interactive={false}
          />
        )}
      </div>
    </div>
  );
};

export default TaskDetailSidebar;
