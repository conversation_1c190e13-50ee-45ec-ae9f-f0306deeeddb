import React, { useMemo } from 'react';
import { Navigate, useLocation } from 'react-router-dom';

import { useAuth } from '@/modules/auth/hooks/useAuth';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

/**
 * HOC Component để bảo vệ các route yêu cầu authentication
 * Sử dụng useAuth hook để kiểm tra token hợp lệ trước khi cho phép truy cập
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  redirectTo = '/auth/login'
}) => {
  const { isAuthenticated, isTokenValid, getToken } = useAuth();
  const location = useLocation();

  // Kiểm tra authentication status một cách an toàn
  const hasValidAuth = useMemo(() => {
    const token = getToken();
    return isAuthenticated && token && isTokenValid();
  }, [isAuthenticated, getToken, isTokenValid]);

  // Nếu chưa đăng nhập hoặc token không hợp lệ
  if (!hasValidAuth) {
    // Lưu lại đường dẫn hiện tại để redirect sau khi đăng nhập
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location.pathname }}
        replace
      />
    );
  }

  // Nếu đã đăng nhập và token hợp lệ, render children
  return <>{children}</>;
};

export default ProtectedRoute;
