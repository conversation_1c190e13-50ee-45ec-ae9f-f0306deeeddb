import React from 'react';
import { useTranslation } from 'react-i18next';

import { Typography, Card, Icon, IconName } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts/theme';

import { SocialNetwork } from '../../types/social';

export interface SocialNetworkIconProps {
  /**
   * Social network information
   */
  network: SocialNetwork;

  /**
   * Optional click handler
   */
  onClick?: (network: SocialNetwork) => void;

  /**
   * Additional CSS class
   */
  className?: string;
}

/**
 * Component to display a social network with its icon and name
 */
export const SocialNetworkIcon: React.FC<SocialNetworkIconProps> = ({
  network,
  onClick,
  className = '',
}) => {
  const { t } = useTranslation();
  const { themeMode } = useTheme();
  const isDarkMode = themeMode === 'dark';
  // Handle click event
  const handleClick = () => {
    if (onClick) {
      onClick(network);
    }
  };

  return (
    <Card
      variant="bordered"
      onClick={handleClick}
      hoverable={!!onClick}
      className={`flex flex-col items-center justify-between p-3 h-28 cursor-pointer ${className}`}
      noPadding
      tabIndex={onClick ? 0 : undefined}
      aria-label={t('integration.social.networkAriaLabel', '{{name}} social network', {
        name: network.name,
      })}
    >
      {/* Icon */}
      <div className="flex items-center justify-center w-full pt-1">
        <div
          className="w-12 h-12 rounded-full flex items-center justify-center text-white"
          style={{
            backgroundColor:
              network.color || (isDarkMode ? 'var(--muted)' : 'var(--muted-foreground)'),
          }}
        >
          <Icon name={network.code as IconName} size="md" className="w-6 h-6" />
        </div>
      </div>

      {/* Name */}
      <div className="w-full text-center mt-2 pb-1">
        <Typography variant="body2" align="center" weight="medium">
          {network.name}
        </Typography>
      </div>
    </Card>
  );
};

export default SocialNetworkIcon;
