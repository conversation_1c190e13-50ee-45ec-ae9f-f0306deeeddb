import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  Table,
  Icon,
  Typography,
} from '@/shared/components';
import ActionMenu from '@/shared/components/common/ActionMenu';

import { FileResponseDto } from '../../services/user-file.service';

interface FileTableProps {
  files: FileResponseDto[];
  selectedItems: number[];
  onSelectionChange: (selectedIds: number[]) => void;
  onDelete: (fileId: number) => void;
  onEdit?: (fileId: number) => void;
  onView?: (fileId: number) => void;
}

/**
 * Component hiển thị bảng danh sách file
 */
const FileTable: React.FC<FileTableProps> = ({
  files,
  selectedItems,
  onSelectionChange,
  onDelete,
  onEdit,
  onView,
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Hàm lấy icon theo extension file
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();

    switch (extension) {
      case 'pdf':
        return 'file-pdf';
      case 'doc':
      case 'docx':
        return 'file-word';
      case 'xls':
      case 'xlsx':
        return 'file-excel';
      case 'ppt':
      case 'pptx':
        return 'file-powerpoint';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'svg':
        return 'image';
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return 'video';
      case 'mp3':
      case 'wav':
      case 'flac':
        return 'music';
      case 'zip':
      case 'rar':
      case '7z':
        return 'archive';
      case 'txt':
        return 'file-text';
      default:
        return 'file';
    }
  };

  // Hàm format kích thước file
  const formatFileSize = (bytes: number | null) => {
    if (!bytes) {return t('common:unknown');}

    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${Math.round(bytes / Math.pow(1024, i) * 100) / 100  } ${  sizes[i]}`;
  };

  // Columns cho table file
  const columns = [
    {
      key: 'name',
      title: t('business:file.name'),
      render: (_: unknown, file: FileResponseDto) => (
        <div className="flex items-center space-x-3">
          <Icon
            name={getFileIcon(file.name)}
            className="text-gray-500 flex-shrink-0"
            size="md"
          />
          <div className="min-w-0 flex-1">
            <button
              onClick={() => onView?.(file.id)}
              className="text-blue-600 hover:text-blue-800 hover:underline truncate block max-w-full"
              title={file.name}
            >
              {file.name}
            </button>
          </div>
        </div>
      ),
    },
    {
      key: 'size',
      title: t('business:file.size'),
      render: (_: unknown, file: FileResponseDto) => (
        <Typography variant="body2" className="text-gray-600">
          {formatFileSize(file.size)}
        </Typography>
      ),
    },
    {
      key: 'createdAt',
      title: t('common:createdAt'),
      render: (_: unknown, file: FileResponseDto) => {
        const formatTimestamp = (timestamp: string | number) => {
          try {
            // Convert string timestamp to number if needed
            const timeValue = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;

            if (isNaN(timeValue)) {
              return 'Invalid Date';
            }

            const date = new Date(timeValue);

            if (isNaN(date.getTime())) {
              return 'Invalid Date';
            }

            return date.toLocaleDateString('vi-VN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
            });
          } catch (error) {
            console.error('Error formatting timestamp:', error, timestamp);
            return 'Invalid Date';
          }
        };

        return (
          <Typography variant="body2" className="text-gray-600">
            {formatTimestamp(file.createdAt)}
          </Typography>
        );
      },
    },
    {
      key: 'updatedAt',
      title: t('common:updatedAt'),
      render: (_: unknown, file: FileResponseDto) => {
        const formatTimestamp = (timestamp: string | number) => {
          try {
            // Convert string timestamp to number if needed
            const timeValue = typeof timestamp === 'string' ? parseInt(timestamp, 10) : timestamp;

            if (isNaN(timeValue)) {
              return 'Invalid Date';
            }

            const date = new Date(timeValue);

            if (isNaN(date.getTime())) {
              return 'Invalid Date';
            }

            return date.toLocaleDateString('vi-VN', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
            });
          } catch (error) {
            console.error('Error formatting timestamp:', error, timestamp);
            return 'Invalid Date';
          }
        };

        return (
          <Typography variant="body2" className="text-gray-600">
            {formatTimestamp(file.updatedAt)}
          </Typography>
        );
      },
    },
    {
      key: 'actions',
      title: t('common:actions'),
      render: (_: unknown, file: FileResponseDto) => (
        <ActionMenu
          items={[
            {
              id: 'view',
              label: t('common:view'),
              icon: 'eye',
              onClick: () => onView?.(file.id),
            },
            {
              id: 'download',
              label: t('common:download', 'Tải xuống'),
              icon: 'download',
              onClick: () => {
                // Mở URL trong tab mới để download
                window.open(file.viewUrl, '_blank');
              },
            },
            ...(onEdit ? [{
              id: 'edit',
              label: t('common:edit'),
              icon: 'edit',
              onClick: () => onEdit(file.id),
            }] : []),
            {
              id: 'delete',
              label: t('common:delete'),
              icon: 'trash',
              onClick: () => onDelete(file.id),
            },
          ]}
        />
      ),
    },
  ];

  return (
    <Table
      data={files}
      columns={columns}
      selectable
      rowSelection={{
        selectedRowKeys: selectedItems,
        onChange: (keys) => onSelectionChange(keys as number[]),
      }}
      rowKey="id"
      className="mt-4"
    />
  );
};

export default FileTable;
