import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { ActiveFilters } from '@/modules/components/filters';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Card, Table, IconCard, Tooltip } from '@/shared/components/common';
import { DeleteConfirmModal } from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { useActiveFilters } from '@/shared/hooks/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import useSlideForm from '@/shared/hooks/useSlideForm';

import DocumentForm from '../components/DocumentForm';
import {
  useDocuments,
  useDeleteDocument,
  useBulkDeleteDocuments,
} from '../hooks/useDocuments';
import {
  DocumentQueryDto,
  DocumentResponseDto,
  DocumentType,
  ProcessingStatus,
} from '../types/document.types';

/**
 * Trang quản lý tài liệu
 */
const DocumentsPage: React.FC = () => {
  const { t } = useTranslation(['documents', 'common']);
  const navigate = useNavigate();

  // State cho form và modal
  const { isVisible: isFormVisible, showForm, hideForm } = useSlideForm();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<number | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  // Xử lý xem chi tiết
  const handleView = useCallback(
    (id: number) => {
      navigate(`/documents/${id}`);
    },
    [navigate]
  );

  // Xử lý chỉnh sửa
  const handleEdit = useCallback(
    (id: number) => {
      setSelectedDocumentId(id);
      showForm();
    },
    [setSelectedDocumentId, showForm]
  );

  // Xử lý hiển thị modal xác nhận xóa
  const handleShowDeleteConfirm = useCallback(
    (id: number) => {
      setSelectedDocumentId(id);
      setIsDeleteModalOpen(true);
    },
    [setSelectedDocumentId]
  );

  // Xử lý xóa nhiều
  const handleBulkDelete = useCallback(() => {
    if (selectedRowKeys.length > 0) {
      setIsBulkDeleteModalOpen(true);
    }
  }, [selectedRowKeys]);

  // Định nghĩa cột cho bảng
  const columns: TableColumn<DocumentResponseDto>[] = useMemo(
    () => [
      {
        key: 'title',
        title: t('documents:table.title', 'Tiêu đề'),
        dataIndex: 'title',
        sortable: true,
        render: (value: unknown, record: DocumentResponseDto) => (
          <button
            onClick={() => handleView(record.id)}
            className="text-blue-600 hover:text-blue-800 hover:underline font-medium"
          >
            {value as string}
          </button>
        ),
        align: 'left',
      },
      {
        key: 'description',
        title: t('documents:table.description', 'Mô tả'),
        dataIndex: 'description',
        sortable: true,
        render: (value: unknown) => {
          const desc = value as string;
          return desc ? (
            <span className="text-gray-600 truncate max-w-xs block" title={desc}>
              {desc}
            </span>
          ) : (
            <span className="text-gray-400">-</span>
          );
        },
      },
      {
        key: 'documentType',
        title: t('documents:table.type', 'Loại'),
        dataIndex: 'documentType',
        sortable: true,
        render: (value: unknown) => {
          const type = value as DocumentType;
          const typeLabels = {
            [DocumentType.POLICY]: t('documents:type.policy', 'Chính sách'),
            [DocumentType.PROCEDURE]: t('documents:type.procedure', 'Quy trình'),
            [DocumentType.MANUAL]: t('documents:type.manual', 'Hướng dẫn'),
            [DocumentType.FORM]: t('documents:type.form', 'Biểu mẫu'),
            [DocumentType.TEMPLATE]: t('documents:type.template', 'Mẫu'),
            [DocumentType.REPORT]: t('documents:type.report', 'Báo cáo'),
            [DocumentType.CONTRACT]: t('documents:type.contract', 'Hợp đồng'),
            [DocumentType.PRESENTATION]: t('documents:type.presentation', 'Thuyết trình'),
            [DocumentType.SPREADSHEET]: t('documents:type.spreadsheet', 'Bảng tính'),
            [DocumentType.IMAGE]: t('documents:type.image', 'Hình ảnh'),
            [DocumentType.VIDEO]: t('documents:type.video', 'Video'),
            [DocumentType.AUDIO]: t('documents:type.audio', 'Âm thanh'),
            [DocumentType.OTHER]: t('documents:type.other', 'Khác'),
          };
          return (
            <span className="text-gray-600">{typeLabels[type] || type}</span>
          );
        },
      },
      {
        key: 'processingStatus',
        title: t('documents:table.status', 'Trạng thái'),
        dataIndex: 'processingStatus',
        sortable: true,
        render: (value: unknown) => {
          const status = value as ProcessingStatus;
          const statusConfig = {
            [ProcessingStatus.PENDING]: {
              label: t('documents:status.pending', 'Chờ xử lý'),
              className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
            },
            [ProcessingStatus.PROCESSING]: {
              label: t('documents:status.processing', 'Đang xử lý'),
              className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
            },
            [ProcessingStatus.COMPLETED]: {
              label: t('documents:status.completed', 'Hoàn thành'),
              className: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
            },
            [ProcessingStatus.FAILED]: {
              label: t('documents:status.failed', 'Thất bại'),
              className: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
            },
            [ProcessingStatus.CANCELLED]: {
              label: t('documents:status.cancelled', 'Đã hủy'),
              className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
            },
          };

          const config = statusConfig[status];
          return (
            <span
              className={`px-2 py-1 rounded-full text-xs font-medium ${config?.className || ''}`}
            >
              {config?.label || status}
            </span>
          );
        },
      },
      {
        key: 'fileSize',
        title: t('documents:table.size', 'Kích thước'),
        dataIndex: 'fileSizeMB',
        sortable: true,
        render: (value: unknown) => {
          const size = value as number;
          return (
            <span className="text-gray-600">
              {size ? `${size.toFixed(2)} MB` : '-'}
            </span>
          );
        },
      },
      {
        key: 'actions',
        title: t('common:actions'),
        render: (_, record) => {
          return (
            <div className="flex space-x-2">
              <Tooltip content={t('common:view')}>
                <IconCard
                  icon="eye"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleView(record.id)}
                />
              </Tooltip>
              <Tooltip content={t('common:edit')}>
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleEdit(record.id)}
                />
              </Tooltip>
              <Tooltip content={t('common:delete')}>
                <IconCard
                  icon="trash"
                  variant="ghost"
                  size="sm"
                  onClick={() => handleShowDeleteConfirm(record.id)}
                />
              </Tooltip>
            </div>
          );
        },
      },
    ],
    [t, handleView, handleEdit, handleShowDeleteConfirm]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): DocumentQueryDto => {
    const queryParams: DocumentQueryDto = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection === 'asc' ? 'ASC' : 'DESC',
    };

    if (params.filterValue && params.filterValue !== 'all') {
      if (Object.values(DocumentType).includes(params.filterValue as DocumentType)) {
        queryParams.documentType = params.filterValue as DocumentType;
      } else if (Object.values(ProcessingStatus).includes(params.filterValue as ProcessingStatus)) {
        queryParams.processingStatus = params.filterValue as ProcessingStatus;
      }
    }

    // Thêm xử lý dateRange nếu có
    if (params.dateRange && params.dateRange[0] && params.dateRange[1]) {
      queryParams.createdFrom = params.dateRange[0].getTime();
      queryParams.createdTo = params.dateRange[1].getTime();
    }

    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<DocumentResponseDto, DocumentQueryDto>({
      columns,
      filterOptions: [
        { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
        {
          id: 'policy',
          label: t('documents:type.policy', 'Chính sách'),
          icon: 'file-text',
          value: DocumentType.POLICY,
        },
        {
          id: 'procedure',
          label: t('documents:type.procedure', 'Quy trình'),
          icon: 'list',
          value: DocumentType.PROCEDURE,
        },
        {
          id: 'manual',
          label: t('documents:type.manual', 'Hướng dẫn'),
          icon: 'book',
          value: DocumentType.MANUAL,
        },
        {
          id: 'completed',
          label: t('documents:status.completed', 'Hoàn thành'),
          icon: 'check',
          value: ProcessingStatus.COMPLETED,
        },
        {
          id: 'failed',
          label: t('documents:status.failed', 'Thất bại'),
          icon: 'x',
          value: ProcessingStatus.FAILED,
        },
      ],
      showDateFilter: true,
      createQueryParams,
    })
  );

  // Gọi API lấy danh sách tài liệu
  const { data: documentsData, isLoading } = useDocuments(dataTable.queryParams);

  // Xử lý dữ liệu trước khi hiển thị
  const processedData = useMemo(() => {
    if (!documentsData?.items) {return [];}
    return documentsData.items;
  }, [documentsData]);

  // Mutations
  const { mutateAsync: deleteDocument } = useDeleteDocument();
  const { mutateAsync: deleteMultipleDocuments } = useBulkDeleteDocuments();

  // Wrapper cho hàm handleSortChange để đảm bảo kiểu dữ liệu đúng
  const handleSortChangeWrapper = useCallback(
    (column: string | null, order: SortOrder | null) => {
      if (column === null || order === null) {
        dataTable.tableData.handleSortChange(null, null);
        return;
      }
      dataTable.tableData.handleSortChange(column, order as SortOrder);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [DocumentType.POLICY]: t('documents:type.policy', 'Chính sách'),
      [DocumentType.PROCEDURE]: t('documents:type.procedure', 'Quy trình'),
      [DocumentType.MANUAL]: t('documents:type.manual', 'Hướng dẫn'),
      [ProcessingStatus.COMPLETED]: t('documents:status.completed', 'Hoàn thành'),
      [ProcessingStatus.FAILED]: t('documents:status.failed', 'Thất bại'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    setSelectedDocumentId(null);
    showForm();
  };

  // Xử lý hủy xóa
  const handleCancelDelete = () => {
    setIsDeleteModalOpen(false);
    setSelectedDocumentId(null);
  };

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = () => {
    setIsBulkDeleteModalOpen(false);
  };

  // Xử lý xác nhận xóa
  const handleConfirmDelete = async () => {
    if (selectedDocumentId) {
      try {
        await deleteDocument(selectedDocumentId);
        setIsDeleteModalOpen(false);
        setSelectedDocumentId(null);
      } catch (error) {
        console.error('Error deleting document:', error);
      }
    }
  };

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = async () => {
    if (selectedRowKeys.length > 0) {
      try {
        const ids = selectedRowKeys.map(key => Number(key));
        await deleteMultipleDocuments(ids);
        setIsBulkDeleteModalOpen(false);
        setSelectedRowKeys([]);
      } catch (error) {
        console.error('Error deleting multiple documents:', error);
      }
    }
  };

  // Xử lý submit form thành công
  const handleFormSuccess = () => {
    hideForm();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    setSelectedDocumentId(null);
    hideForm();
  };

  // Lấy document hiện tại để edit
  const selectedDocument = useMemo(() => {
    if (!selectedDocumentId || !documentsData?.items) {return undefined;}
    return documentsData.items.find(doc => doc.id === selectedDocumentId);
  }, [selectedDocumentId, documentsData]);

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        items={dataTable.menuItems}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={true}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete'),
            variant: 'primary',
            onClick: handleBulkDelete,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form container với animation */}
      <SlideInForm isVisible={isFormVisible}>
        <DocumentForm
          document={selectedDocument}
          onClose={handleCancel}
          onSuccess={handleFormSuccess}
          title={
            selectedDocument
              ? t('documents:form.editTitle', 'Cập nhật tài liệu')
              : t('documents:form.title', 'Thêm tài liệu mới')
          }
        />
      </SlideInForm>

      {/* Modal xác nhận xóa đơn */}
      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete')}
        message={t('documents:confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa tài liệu này?')}
      />

      {/* Modal xác nhận xóa nhiều */}
      <DeleteConfirmModal
        isOpen={isBulkDeleteModalOpen}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmBulkDelete')}
        message={t('documents:confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} tài liệu đã chọn?', {
          count: selectedRowKeys.length,
        })}
      />

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={processedData}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: documentsData?.meta.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: documentsData?.meta.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>
    </div>
  );
};

export default DocumentsPage;
