import { apiClient } from '@/shared/api/axios';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  KeyResultDto,
  KeyResultQueryDto,
  CreateKeyResultDto,
  UpdateKeyResultDto,
} from '../types/key-result.types';

/**
 * Service cho key results
 */
export const KeyResultService = {
  /**
   * Lấy danh sách key results
   * @param params Tham số truy vấn
   * @returns Promise với phản hồi API chứa danh sách key results
   */
  getKeyResults: (params?: KeyResultQueryDto) => {
    return apiClient.get<PaginatedResult<KeyResultDto>>('/okrs/key-results', { params });
  },

  /**
   * Lấy chi tiết key result theo ID
   * @param id ID key result
   * @returns Promise với phản hồi API chứa chi tiết key result
   */
  getKeyResult: (id: number) => {
    return apiClient.get<KeyResultDto>(`/okrs/key-results/${id}`);
  },

  /**
   * Lấy key results theo objective ID
   * @param objectiveId ID objective
   * @returns Promise với phản hồi API chứa danh sách key results
   */
  getKeyResultsByObjective: (objectiveId: number) => {
    return apiClient.get<KeyResultDto[]>(`/okrs/key-results/by-objective/${objectiveId}`);
  },

  /**
   * Tạo mới key result
   * @param data Dữ liệu tạo key result
   * @returns Key result đã tạo
   */
  createKeyResult: (data: CreateKeyResultDto) => {
    return apiClient.post<KeyResultDto>('/okrs/key-results', data);
  },

  /**
   * Cập nhật key result
   * @param id ID key result
   * @param data Dữ liệu cập nhật
   * @returns Key result đã cập nhật
   */
  updateKeyResult: (id: number, data: UpdateKeyResultDto) => {
    return apiClient.put<KeyResultDto>(`/okrs/key-results/${id}`, data);
  },

  /**
   * Xóa key result
   * @param id ID key result
   * @returns Kết quả xóa
   */
  deleteKeyResult: (id: number) => {
    return apiClient.delete<void>(`/okrs/key-results/${id}`);
  },
};
