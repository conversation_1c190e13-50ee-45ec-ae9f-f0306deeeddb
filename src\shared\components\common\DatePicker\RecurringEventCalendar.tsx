import {
  addDays,
  addWeeks,
  addMonths,
  addYears,
  isSameDay,
  startOfMonth,
  endOfMonth,
  format
} from 'date-fns';
import React, { useState, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import EventCalendar, { CalendarEvent } from './EventCalendar';
import { EventCalendarProps } from './EventCalendar';

/**
 * Recurring pattern types
 */
export type RecurrencePattern = 
  | 'daily' 
  | 'weekly' 
  | 'monthly' 
  | 'yearly' 
  | 'weekdays' 
  | 'weekends'
  | 'custom';

/**
 * Recurring rule interface
 */
export interface RecurrenceRule {
  pattern: RecurrencePattern;
  interval: number; // Every N days/weeks/months/years
  endDate?: Date;
  count?: number; // Number of occurrences
  daysOfWeek?: number[]; // 0=Sunday, 1=Monday, etc.
  dayOfMonth?: number; // For monthly recurrence
  monthOfYear?: number; // For yearly recurrence
  exceptions?: Date[]; // Dates to exclude
}

/**
 * Recurring event interface
 */
export interface RecurringEvent extends Omit<CalendarEvent, 'date'> {
  startDate: Date;
  endDate?: Date;
  recurrenceRule: RecurrenceRule;
  originalEvent?: CalendarEvent; // For modified instances
}

/**
 * Props cho RecurringEventCalendar
 */
export interface RecurringEventCalendarProps extends Omit<EventCalendarProps, 'events'> {
  /**
   * Recurring events
   */
  recurringEvents?: RecurringEvent[];
  
  /**
   * One-time events
   */
  oneTimeEvents?: CalendarEvent[];
  
  /**
   * Callback khi tạo recurring event
   */
  onCreateRecurringEvent?: (event: RecurringEvent) => void;
  
  /**
   * Callback khi edit recurring event
   */
  onEditRecurringEvent?: (eventId: string, event: RecurringEvent) => void;
  
  /**
   * Callback khi delete recurring event
   */
  onDeleteRecurringEvent?: (eventId: string) => void;
  
  /**
   * Hiển thị recurring event creator
   */
  showRecurringEventCreator?: boolean;
  
  /**
   * Max events để generate (performance)
   */
  maxGeneratedEvents?: number;
  
  /**
   * Date range để generate events
   */
  generateRange?: {
    start: Date;
    end: Date;
  };
}

/**
 * Generate recurring events based on rule
 */
const generateRecurringEvents = (
  recurringEvent: RecurringEvent,
  startDate: Date,
  endDate: Date,
  maxEvents = 1000
): CalendarEvent[] => {
  const events: CalendarEvent[] = [];
  const { recurrenceRule } = recurringEvent;
  let currentDate = new Date(recurringEvent.startDate);
  let count = 0;

  // Check if we should stop generating
  const shouldStop = () => {
    if (count >= maxEvents) {return true;}
    if (currentDate > endDate) {return true;}
    if (recurrenceRule.endDate && currentDate > recurrenceRule.endDate) {return true;}
    if (recurrenceRule.count && count >= recurrenceRule.count) {return true;}
    return false;
  };

  while (!shouldStop()) {
    // Check if current date is in range and not an exception
    if (currentDate >= startDate && currentDate <= endDate) {
      const isException = recurrenceRule.exceptions?.some(exception => 
        isSameDay(exception, currentDate)
      );

      if (!isException) {
        // Check day of week constraints
        let shouldInclude = true;
        
        if (recurrenceRule.pattern === 'weekdays') {
          const dayOfWeek = currentDate.getDay();
          shouldInclude = dayOfWeek >= 1 && dayOfWeek <= 5; // Monday to Friday
        } else if (recurrenceRule.pattern === 'weekends') {
          const dayOfWeek = currentDate.getDay();
          shouldInclude = dayOfWeek === 0 || dayOfWeek === 6; // Sunday or Saturday
        } else if (recurrenceRule.daysOfWeek && recurrenceRule.daysOfWeek.length > 0) {
          const dayOfWeek = currentDate.getDay();
          shouldInclude = recurrenceRule.daysOfWeek.includes(dayOfWeek);
        }

        if (shouldInclude) {
          events.push({
            ...recurringEvent,
            id: `${recurringEvent.id}-${format(currentDate, 'yyyy-MM-dd')}`,
            date: new Date(currentDate),
            title: recurringEvent.title,
            description: recurringEvent.description,
            color: recurringEvent.color,
            type: recurringEvent.type,
            priority: recurringEvent.priority,
            category: recurringEvent.category,
          });
          count++;
        }
      }
    }

    // Move to next occurrence
    switch (recurrenceRule.pattern) {
      case 'daily':
        currentDate = addDays(currentDate, recurrenceRule.interval);
        break;
      case 'weekly':
        currentDate = addWeeks(currentDate, recurrenceRule.interval);
        break;
      case 'monthly':
        currentDate = addMonths(currentDate, recurrenceRule.interval);
        break;
      case 'yearly':
        currentDate = addYears(currentDate, recurrenceRule.interval);
        break;
      case 'weekdays':
      case 'weekends':
        currentDate = addDays(currentDate, 1);
        break;
      case 'custom':
        // For custom patterns, use daily increment and filter by daysOfWeek
        currentDate = addDays(currentDate, 1);
        break;
      default:
        currentDate = addDays(currentDate, recurrenceRule.interval);
        break;
    }
  }

  return events;
};

/**
 * RecurringEventCalendar component
 */
const RecurringEventCalendar: React.FC<RecurringEventCalendarProps> = ({
  recurringEvents = [],
  oneTimeEvents = [],
  onCreateRecurringEvent,
  // onEditRecurringEvent, // Unused for now
  // onDeleteRecurringEvent, // Unused for now
  showRecurringEventCreator = false,
  maxGeneratedEvents = 1000,
  generateRange,
  month: propMonth,
  ...eventCalendarProps
}) => {
  const { t } = useTranslation();
  
  // Current month for generating events
  const currentMonth = useMemo(() => propMonth || new Date(), [propMonth]);

  // Default generate range (current month ± 6 months)
  const defaultGenerateRange = useMemo(() => ({
    start: addMonths(startOfMonth(currentMonth), -6),
    end: addMonths(endOfMonth(currentMonth), 6),
  }), [currentMonth]);

  const finalGenerateRange = generateRange || defaultGenerateRange;

  // Generate all events from recurring rules
  const generatedEvents = useMemo(() => {
    const events: CalendarEvent[] = [];
    
    // Add one-time events
    events.push(...oneTimeEvents);
    
    // Generate recurring events
    for (const recurringEvent of recurringEvents) {
      const generated = generateRecurringEvents(
        recurringEvent,
        finalGenerateRange.start,
        finalGenerateRange.end,
        maxGeneratedEvents
      );
      events.push(...generated);
    }
    
    return events;
  }, [recurringEvents, oneTimeEvents, finalGenerateRange, maxGeneratedEvents]);

  // State for recurring event creator
  const [showCreator, setShowCreator] = useState(false);
  const [newRecurringEvent, setNewRecurringEvent] = useState<Partial<RecurringEvent>>({
    title: '',
    startDate: new Date(),
    recurrenceRule: {
      pattern: 'weekly',
      interval: 1,
    },
    color: '#3B82F6',
    type: 'dot',
  });

  // Handle create recurring event
  const handleCreateRecurringEvent = useCallback(() => {
    if (newRecurringEvent.title && newRecurringEvent.startDate && newRecurringEvent.recurrenceRule) {
      const event: RecurringEvent = {
        id: `recurring-${Date.now()}`,
        title: newRecurringEvent.title,
        startDate: newRecurringEvent.startDate,
        endDate: newRecurringEvent.endDate,
        recurrenceRule: newRecurringEvent.recurrenceRule,
        color: newRecurringEvent.color || '#3B82F6',
        type: newRecurringEvent.type || 'dot',
        description: newRecurringEvent.description,
        priority: newRecurringEvent.priority,
        category: newRecurringEvent.category,
      };
      
      onCreateRecurringEvent?.(event);
      setShowCreator(false);
      setNewRecurringEvent({
        title: '',
        startDate: new Date(),
        recurrenceRule: {
          pattern: 'weekly',
          interval: 1,
        },
        color: '#3B82F6',
        type: 'dot',
      });
    }
  }, [newRecurringEvent, onCreateRecurringEvent]);

  // Recurring pattern options
  const patternOptions = [
    { value: 'daily', label: t('recurrence.daily', 'Daily') },
    { value: 'weekly', label: t('recurrence.weekly', 'Weekly') },
    { value: 'monthly', label: t('recurrence.monthly', 'Monthly') },
    { value: 'yearly', label: t('recurrence.yearly', 'Yearly') },
    { value: 'weekdays', label: t('recurrence.weekdays', 'Weekdays') },
    { value: 'weekends', label: t('recurrence.weekends', 'Weekends') },
  ];

  return (
    <div className="space-y-4">
      {/* Recurring Event Creator */}
      {showRecurringEventCreator && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-4 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('recurrence.createEvent', 'Create Recurring Event')}
            </h3>
            <button
              onClick={() => setShowCreator(!showCreator)}
              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {showCreator ? t('common.cancel', 'Cancel') : t('common.create', 'Create')}
            </button>
          </div>

          {showCreator && (
            <div className="space-y-4">
              {/* Event Title */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('event.title', 'Title')}
                </label>
                <input
                  type="text"
                  value={newRecurringEvent.title || ''}
                  onChange={(e) => setNewRecurringEvent(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder={t('event.titlePlaceholder', 'Enter event title')}
                />
              </div>

              {/* Start Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('event.startDate', 'Start Date')}
                </label>
                <input
                  type="date"
                  value={newRecurringEvent.startDate ? format(newRecurringEvent.startDate, 'yyyy-MM-dd') : ''}
                  onChange={(e) => setNewRecurringEvent(prev => ({ 
                    ...prev, 
                    startDate: new Date(e.target.value) 
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                />
              </div>

              {/* Recurrence Pattern */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('recurrence.pattern', 'Recurrence Pattern')}
                </label>
                <select
                  value={newRecurringEvent.recurrenceRule?.pattern || 'weekly'}
                  onChange={(e) => setNewRecurringEvent(prev => ({
                    ...prev,
                    recurrenceRule: {
                      ...prev.recurrenceRule!,
                      pattern: e.target.value as RecurrencePattern,
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  {patternOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Interval */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  {t('recurrence.interval', 'Repeat every')}
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="number"
                    min="1"
                    value={newRecurringEvent.recurrenceRule?.interval || 1}
                    onChange={(e) => setNewRecurringEvent(prev => ({
                      ...prev,
                      recurrenceRule: {
                        ...prev.recurrenceRule!,
                        interval: parseInt(e.target.value) || 1,
                      }
                    }))}
                    className="w-20 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                  <span className="text-sm text-gray-600 dark:text-gray-400">
                    {newRecurringEvent.recurrenceRule?.pattern === 'daily' && t('recurrence.days', 'day(s)')}
                    {newRecurringEvent.recurrenceRule?.pattern === 'weekly' && t('recurrence.weeks', 'week(s)')}
                    {newRecurringEvent.recurrenceRule?.pattern === 'monthly' && t('recurrence.months', 'month(s)')}
                    {newRecurringEvent.recurrenceRule?.pattern === 'yearly' && t('recurrence.years', 'year(s)')}
                  </span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button
                  onClick={() => setShowCreator(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                >
                  {t('common.cancel', 'Cancel')}
                </button>
                <button
                  onClick={handleCreateRecurringEvent}
                  disabled={!newRecurringEvent.title}
                  className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {t('common.create', 'Create')}
                </button>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Event Calendar */}
      <EventCalendar
        {...eventCalendarProps}
        events={generatedEvents}
        month={propMonth}
      />

      {/* Event Statistics */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('events.statistics', 'Event Statistics')}
        </h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600 dark:text-gray-400">
              {t('events.recurring', 'Recurring Events')}:
            </span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {recurringEvents.length}
            </span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">
              {t('events.oneTime', 'One-time Events')}:
            </span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {oneTimeEvents.length}
            </span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">
              {t('events.generated', 'Generated Events')}:
            </span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {generatedEvents.length}
            </span>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">
              {t('events.thisMonth', 'This Month')}:
            </span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {generatedEvents.filter(event => 
                event.date.getMonth() === currentMonth.getMonth() &&
                event.date.getFullYear() === currentMonth.getFullYear()
              ).length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecurringEventCalendar;
