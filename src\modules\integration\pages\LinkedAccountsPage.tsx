import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Button, ResponsiveGrid } from '@/shared/components/common';

import { addLinkedAccount } from '../api/accountMockData';
import LinkedAccountsCard from '../components/LinkedAccountsCard';
import { BankAccount } from '../types/account';

/**
 * Page to demonstrate the LinkedAccountsCard component
 */
const LinkedAccountsPage: React.FC = () => {
  const { t } = useTranslation();
  const [, setIsAdding] = useState(false);

  // Handle add button click
  const handleAddClick = async () => {
    // Trong ứng dụng thực tế, đây sẽ mở modal để thêm tài khoản
    // Ở đây chúng ta sẽ giả lập việc thêm tài khoản
    try {
      setIsAdding(true);

      // Tạo dữ liệu mẫu cho tài khoản mới
      const newAccount: Omit<BankAccount, 'id' | 'linkedDate'> = {
        bankCode: 'TCB',
        bankName: 'Techcombank',
        accountName: 'NGUYEN VAN A',
        accountNumber: '************',
        isDefault: false,
      };

      await addLinkedAccount(newAccount);

      // Hiển thị thông báo thành công
      alert(
        t(
          'integration.accounts.addSuccess',
          'Tài khoản đã được thêm thành công! Vui lòng tải lại trang để xem.'
        )
      );
    } catch (error) {
      console.log(error);
      alert(t('integration.accounts.addError', 'Có lỗi xảy ra khi thêm tài khoản.'));
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <div>
      <Typography variant="h4" className="mb-4">
        {t('integration.accounts.title')}
      </Typography>
      <Typography variant="body1" color="muted" className="mb-6">
        {t('integration.accounts.description')}
      </Typography>

      <ResponsiveGrid>
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('integration.accounts.basicExample', 'Ví dụ cơ bản')}
          </Typography>
          <LinkedAccountsCard onAddClick={handleAddClick} />
        </Card>

        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('integration.accounts.withoutButton', 'Không có nút thêm')}
          </Typography>
          <LinkedAccountsCard showAddButton={false} />
        </Card>
      </ResponsiveGrid>

      <div className="mt-8">
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('integration.accounts.usage', 'Cách sử dụng')}
          </Typography>

          <div className="bg-card-muted p-4 rounded-md overflow-x-auto">
            <pre className="text-sm text-foreground">
              {`import { LinkedAccountsCard } from '@/modules/integration/components';
import { useState } from 'react';
import { addLinkedAccount } from '@/modules/integration/api/accountMockData';

// Basic usage
const MyComponent = () => {
  const handleAddClick = async () => {
    // Implement add account logic
    const newAccount = {
      bankCode: 'TCB',
      bankName: 'Techcombank',
      accountName: 'NGUYEN VAN A',
      accountNumber: '************',
      isDefault: false
    };

    try {
      await addLinkedAccount(newAccount);
      // Handle success
    } catch (error) {
      // Handle error
    }
  };

  return (
    <>
      {/* Basic usage */}
      <LinkedAccountsCard onAddClick={handleAddClick} />

      {/* Without add button */}
      <LinkedAccountsCard showAddButton={false} />

      {/* With custom max accounts */}
      <LinkedAccountsCard maxAccounts={5} />
    </>
  );
};
`}
            </pre>
          </div>
        </Card>
      </div>

      <div className="mt-8">
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('integration.accounts.reload', 'Tải lại dữ liệu')}
          </Typography>
          <Typography variant="body1" className="mb-4">
            {t(
              'integration.accounts.reloadDescription',
              'Nhấn nút bên dưới để tải lại trang và xem tài khoản mới được thêm vào.'
            )}
          </Typography>
          <Button variant="primary" onClick={() => window.location.reload()}>
            {t('integration.accounts.reloadButton', 'Tải lại trang')}
          </Button>
        </Card>
      </div>
    </div>
  );
};

export default LinkedAccountsPage;
