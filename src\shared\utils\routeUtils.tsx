import { RouteObject } from 'react-router-dom';

import { ProtectedRoute } from '@/shared/components/auth';

/**
 * Utility function để wrap các routes với ProtectedRoute
 * @param routes - Mảng các route objects cần bảo vệ
 * @param redirectTo - Đường dẫn redirect khi chưa đăng nhập (mặc định: '/auth/login')
 * @returns Mảng routes đã được wrap với ProtectedRoute
 */
export const protectRoutes = (
  routes: RouteObject[],
  redirectTo: string = '/auth'
): RouteObject[] => {
  return routes.map(route => {
    const protectedRoute: RouteObject = {
      ...route,
      element: route.element ? (
        <ProtectedRoute redirectTo={redirectTo}>
          {route.element}
        </ProtectedRoute>
      ) : route.element,
    };

    // Chỉ thêm children nếu có
    if (route.children) {
      protectedRoute.children = protectRoutes(route.children, redirectTo);
    }

    return protectedRoute;
  });
};

/**
 * Utility function để tạo một protected route đơn lẻ
 * @param route - Route object cần bảo vệ
 * @param redirectTo - Đường dẫn redirect khi chưa đăng nhập
 * @returns Route object đã được wrap với ProtectedRoute
 */
export const createProtectedRoute = (
  route: RouteObject,
  redirectTo: string = '/auth'
): RouteObject => {
  const protectedRoute: RouteObject = {
    ...route,
    element: route.element ? (
      <ProtectedRoute redirectTo={redirectTo}>
        {route.element}
      </ProtectedRoute>
    ) : route.element,
  };

  // Chỉ thêm children nếu có
  if (route.children) {
    protectedRoute.children = protectRoutes(route.children, redirectTo);
  }

  return protectedRoute;
};
