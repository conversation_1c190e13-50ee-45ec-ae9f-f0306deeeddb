import { useState, ReactNode, useEffect, useMemo, useCallback, memo } from 'react';

import { Resizer } from '@/shared/components/common';
import { useWindowSize } from '@/shared/hooks/common';
import { useBreakpoint } from '@/shared/hooks/common';

interface ResizableChatLayoutProps {
  chatPanel: ReactNode;
  viewPanel: ReactNode;
  isChatPanelOpen: boolean;
  isMobile: boolean;
}

/**
 * Layout có thể resize cho ChatPanel và ViewPanel
 * Tối ưu hóa để tránh re-render ViewPanel khi resize
 */
const ResizableChatLayout = memo<ResizableChatLayoutProps>(({
  chatPanel,
  viewPanel,
  isChatPanelOpen,
  isMobile,
}) => {
  const { width: windowWidth } = useWindowSize();
  const isDesktop = useBreakpoint('lg'); // lg = 1024px
  const isTablet = useBreakpoint('md') && !isDesktop; // md = 768px, < 1024px

  // Tính toán tỷ lệ viewport width dựa trên breakpoint
  const getViewportWidthPercentage = useMemo(() => {
    if (isDesktop) {return 30;} // 30vw cho desktop
    if (isTablet) {return 40;} // 40vw cho tablet
    return 85; // 85vw cho mobile
  }, [isDesktop, isTablet]);

  const [leftWidthVw, setLeftWidthVw] = useState(getViewportWidthPercentage);
  const [isResizing, setIsResizing] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(0);

  // Cập nhật leftWidthVw khi kích thước màn hình hoặc breakpoint thay đổi
  useEffect(() => {
    setLeftWidthVw(getViewportWidthPercentage);
  }, [windowWidth, getViewportWidthPercentage]);

  // Handle mouse down on resizer
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true);
    setStartX(e.clientX);
    setStartWidth(leftWidthVw);
    document.body.style.cursor = 'col-resize';
    document.body.style.userSelect = 'none';
  }, [leftWidthVw]);

  // Handle mouse move
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (!isResizing) {return;}

    // Tính toán sự thay đổi theo vw
    const deltaX = e.clientX - startX;
    const deltaVw = (deltaX / windowWidth) * 100;
    const newWidthVw = startWidth + deltaVw;

    // Tính toán giới hạn min/max dựa trên breakpoint
    const minWidthVw = isDesktop ? 25 : isTablet ? 30 : 70; // min vw cho desktop/tablet/mobile
    const maxWidthVw = isDesktop ? 35 : isTablet ? 50 : 90; // max vw cho desktop/tablet/mobile

    // Apply min/max constraints
    if (newWidthVw >= minWidthVw && newWidthVw <= maxWidthVw) {
      setLeftWidthVw(newWidthVw);

      // Trigger a custom resize event during dragging
      window.dispatchEvent(
        new CustomEvent('layout-resizing', { detail: { leftWidthVw: newWidthVw } })
      );
    }
  }, [isResizing, startX, startWidth, windowWidth, isDesktop, isTablet]);

  // Handle mouse up
  const handleMouseUp = useCallback(() => {
    if (isResizing) {
      setIsResizing(false);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';

      // Trigger a custom resize event to notify components that the layout has been resized
      window.dispatchEvent(new CustomEvent('layout-resized', { detail: { leftWidthVw } }));
    }
  }, [isResizing, leftWidthVw]);

  return (
    <div
      className="flex h-full"
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      {/* Chat Panel - Left side (chỉ hiển thị khi cần) */}
      {!isMobile && isChatPanelOpen && (
        <>
          <div
            className="h-full overflow-hidden flex-shrink-0"
            style={{
              width: `${leftWidthVw}dvw`,
              minWidth: `${leftWidthVw}dvw`,
              maxWidth: `${leftWidthVw}dvw`,
            }}
          >
            {chatPanel}
          </div>

          {/* Resizer */}
          <Resizer
            direction="vertical"
            onResizeStart={handleMouseDown}
            color="primary"
            chatPanelId="chat-panel-scroll"
          />
        </>
      )}

      {/* View Panel - Right side (luôn hiển thị) */}
      <div className="flex-1 h-full overflow-hidden">{viewPanel}</div>
    </div>
  );
});

ResizableChatLayout.displayName = 'ResizableChatLayout';

export default ResizableChatLayout;
