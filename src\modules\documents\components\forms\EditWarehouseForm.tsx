import React, { useEffect, useState } from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import {
  Card,
  Form,
  FormItem,
  Input,
  Select,
  Button,
  Textarea,
} from '@/shared/components/common';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { zodResolver } from '@hookform/resolvers/zod';

import { useUpdateWarehouse, useWarehouse } from '../../hooks/useWarehouseQuery';
import { WarehouseTypeEnum, UpdateWarehouseDto } from '../../types/warehouse.types';

// Schema cho form
const getWarehouseSchema = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(1, t('business:warehouse.form.namePlaceholder')),
    description: z.string().optional(),
    type: z.nativeEnum(WarehouseTypeEnum, {
      errorMap: () => ({
        message: t('business:warehouse.form.typePlaceholder'),
      }),
    }),
  });

export type EditWarehouseFormValues = z.infer<ReturnType<typeof getWarehouseSchema>>;

interface EditWarehouseFormProps {
  warehouseId: number;
  onSubmit?: (data: EditWarehouseFormValues) => void;
  onCancel?: () => void;
}

/**
 * Component form chỉnh sửa kho
 */
const EditWarehouseForm: React.FC<EditWarehouseFormProps> = ({
  warehouseId,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const notification = useSmartNotification();
  const [isLoading, setIsLoading] = useState(true);

  // Lấy thông tin kho
  const { data: warehouseData, isLoading: isLoadingWarehouse } = useWarehouse(warehouseId);

  // Mutation để cập nhật kho
  const { mutateAsync: updateWarehouse, isPending: isUpdating } = useUpdateWarehouse();

  // Schema cho form
  const warehouseSchema = getWarehouseSchema(t);

  // Khởi tạo form
  const form = useForm<EditWarehouseFormValues>({
    resolver: zodResolver(warehouseSchema),
    defaultValues: {
      name: '',
      description: '',
      type: WarehouseTypeEnum.PHYSICAL,
    },
  });

  // Cập nhật giá trị mặc định khi có dữ liệu
  useEffect(() => {
    if (warehouseData?.result) {
      const warehouse = warehouseData.result;
      form.reset({
        name: warehouse.name,
        description: warehouse.description || '',
        type: (warehouse.type as WarehouseTypeEnum) || WarehouseTypeEnum.PHYSICAL,
      });
      setIsLoading(false);
    }
  }, [warehouseData, form]);

  // Xử lý submit form
  const handleSubmit = async (values: EditWarehouseFormValues) => {
    try {
      // Cập nhật kho
      await updateWarehouse({
        id: warehouseId,
        data: values as UpdateWarehouseDto,
      });
      notification.success({ message: t('business:warehouse.updateSuccess') });

      // Gọi callback onSubmit nếu có
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error) {
      console.error('Error updating warehouse:', error);
      notification.error({
        message: t('business:warehouse.updateError')
      });
    }
  };

  return (
    <Card title={t('business:warehouse.edit')}>
      <Form
        schema={warehouseSchema}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        onSubmit={handleSubmit as any}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 gap-4">
          <FormItem
            name="name"
            label={t('business:warehouse.name')}
            required
          >
            <Input
              fullWidth
              placeholder={t('business:warehouse.form.namePlaceholder')}
              {...form.register('name')}
              disabled={isLoading || isLoadingWarehouse}
            />
          </FormItem>

          <FormItem
            name="description"
            label={t('business:warehouse.desc')}
          >
            <Textarea
              rows={4}
              placeholder={t('business:warehouse.form.descriptionPlaceholder')}
              {...form.register('description')}
              disabled={isLoading || isLoadingWarehouse}
            />
          </FormItem>

          <FormItem
            name="type"
            label={t('business:warehouse.type')}
            required
          >
            <Controller
              control={form.control}
              name="type"
              render={({ field }) => (
                <Select
                  fullWidth
                  placeholder={t('business:warehouse.form.selectType')}
                  options={[
                    {
                      value: WarehouseTypeEnum.PHYSICAL,
                      label: t('business:warehouse.types.PHYSICAL'),
                    },
                    {
                      value: WarehouseTypeEnum.VIRTUAL,
                      label: t('business:warehouse.types.VIRTUAL'),
                    },
                  ]}
                  value={field.value}
                  onChange={field.onChange}
                  disabled={isLoading || isLoadingWarehouse}
                />
              )}
            />
          </FormItem>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button
            variant="outline"
            onClick={onCancel}
          >
            {t('common:cancel')}
          </Button>
          <Button
            type="submit"
            isLoading={isUpdating}
          >
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditWarehouseForm;
