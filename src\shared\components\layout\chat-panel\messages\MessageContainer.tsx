/**
 * Message Container Component
 * Wrapper component for chat messages with avatar, timestamp, and actions
 */

import React, { useState } from 'react';

import { Typography, Icon } from '@/shared/components/common';
import { formatRelativeTime } from '@/shared/utils/date';
import { ChatMessage } from '@/shared/websocket/types/chat-message.types';

import MessageActions from './MessageActions';

// Props interface
export interface MessageContainerProps {
  message: ChatMessage;
  children: React.ReactNode;
  isCurrentUser?: boolean;
  showAvatar?: boolean;
  showTimestamp?: boolean;
  showSender?: boolean;
  onReply?: (messageId: string) => void;
  onEdit?: (messageId: string) => void;
  onDelete?: (messageId: string) => void;
  onReact?: (messageId: string, emoji: string) => void;
  className?: string;
}

/**
 * Message Container Component
 */
const MessageContainer: React.FC<MessageContainerProps> = ({
  message,
  children,
  isCurrentUser = false,
  showAvatar = true,
  showTimestamp = true,
  showSender = true,
  onReply,
  onEdit,
  onDelete,
  onReact,
  className = '',
}) => {
  const [showActions, setShowActions] = useState(false);

  // Message alignment based on sender
  const messageAlignment = isCurrentUser ? 'flex-row-reverse' : 'flex-row';
  const contentAlignment = isCurrentUser ? 'items-end' : 'items-start';
  const bubbleAlignment = isCurrentUser ? 'ml-auto' : 'mr-auto';

  // Avatar component
  const renderAvatar = () => {
    if (!showAvatar || isCurrentUser) {return null;}

    return (
      <div className="flex-shrink-0 mr-3">
        {message.sender.avatar ? (
          <img
            src={message.sender.avatar}
            alt={message.sender.name}
            className="w-8 h-8 rounded-full object-cover"
          />
        ) : (
          <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
            <Typography variant="caption" className="text-primary font-medium">
              {message.sender.name.charAt(0).toUpperCase()}
            </Typography>
          </div>
        )}
      </div>
    );
  };

  // Sender name component
  const renderSender = () => {
    if (!showSender || isCurrentUser) {return null;}

    return (
      <Typography 
        variant="caption" 
        className="text-gray-600 dark:text-gray-400 mb-1 block"
      >
        {message.sender.name}
      </Typography>
    );
  };

  // Timestamp component
  const renderTimestamp = () => {
    if (!showTimestamp) {return null;}

    return (
      <div className={`flex items-center gap-1 mt-1 ${isCurrentUser ? 'justify-end' : 'justify-start'}`}>
        <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
          {formatRelativeTime(new Date(message.timestamp))}
        </Typography>
        
        {/* Message status indicators for current user */}
        {isCurrentUser && (
          <>
            {message.metadata.edited && (
              <Icon name="edit" size="xs" className="text-gray-400" />
            )}
            {/* Add more status indicators as needed */}
          </>
        )}
      </div>
    );
  };

  // Message priority indicator
  const renderPriorityIndicator = () => {
    if (message.metadata.priority === 'normal') {return null;}

    const priorityConfig = {
      high: { icon: 'alert-triangle', color: 'text-orange-500' },
      urgent: { icon: 'alert-circle', color: 'text-red-500' },
    };

    const config = priorityConfig[message.metadata.priority];
    if (!config) {return null;}

    return (
      <Icon 
        name={config.icon} 
        size="xs" 
        className={`${config.color} ml-1`} 
      />
    );
  };

  // Reply indicator
  const renderReplyIndicator = () => {
    if (!message.metadata.replyTo) {return null;}

    return (
      <div className="flex items-center gap-1 mb-2 text-gray-500 dark:text-gray-400">
        <Icon name="corner-down-right" size="xs" />
        <Typography variant="caption">
          Replying to message
        </Typography>
      </div>
    );
  };

  return (
    <div 
      className={`flex ${messageAlignment} ${contentAlignment} mb-4 group ${className}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {/* Avatar */}
      {renderAvatar()}

      {/* Message content */}
      <div className={`flex flex-col max-w-[80%] ${bubbleAlignment}`}>
        {/* Sender name */}
        {renderSender()}

        {/* Reply indicator */}
        {renderReplyIndicator()}

        {/* Message content with priority indicator */}
        <div className="relative">
          <div className="flex items-start gap-1">
            {children}
            {renderPriorityIndicator()}
          </div>

          {/* Message actions */}
          {showActions && (onReply || onEdit || onDelete || onReact) && (
            <MessageActions
              messageId={message.id}
              isCurrentUser={isCurrentUser}
              onReply={onReply}
              onEdit={onEdit}
              onDelete={onDelete}
              onReact={onReact}
              className="absolute -top-2 right-0 opacity-0 group-hover:opacity-100 transition-opacity"
            />
          )}
        </div>

        {/* Timestamp */}
        {renderTimestamp()}
      </div>
    </div>
  );
};

export default MessageContainer;
