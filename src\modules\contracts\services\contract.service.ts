import { apiClient } from '@/shared/api/axios';

import type {
  Contract,
  ContractQueryParams,
  ContractCreateDto,
  ContractUpdateDto,
  ContractListResponse,
  ContractStats,
} from '../types/contract.types';

/**
 * Contract API Service
 */
export class ContractService {
  private static readonly BASE_URL = '/api/contracts';

  /**
   * Get contracts with pagination and filtering
   */
  static async getContracts(params?: ContractQueryParams): Promise<ContractListResponse> {
    const response = await apiClient.get(this.BASE_URL, { params });
    return response.data;
  }

  /**
   * Get contract by ID
   */
  static async getContract(id: string): Promise<Contract> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}`);
    return response.data;
  }

  /**
   * Create new contract
   */
  static async createContract(data: ContractCreateDto): Promise<Contract> {
    const response = await apiClient.post(this.BASE_URL, data);
    return response.data;
  }

  /**
   * Update contract
   */
  static async updateContract(id: string, data: ContractUpdateDto): Promise<Contract> {
    const response = await apiClient.put(`${this.BASE_URL}/${id}`, data);
    return response.data;
  }

  /**
   * Delete contract
   */
  static async deleteContract(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Duplicate contract
   */
  static async duplicateContract(id: string): Promise<Contract> {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/duplicate`);
    return response.data;
  }

  /**
   * Get contract statistics
   */
  static async getContractStats(): Promise<ContractStats> {
    const response = await apiClient.get(`${this.BASE_URL}/stats`);
    return response.data;
  }

  /**
   * Get contract history/versions
   */
  static async getContractHistory(id: string) {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/history`);
    return response.data;
  }

  /**
   * Upload contract attachment
   */
  static async uploadAttachment(id: string, file: File, description?: string) {
    const formData = new FormData();
    formData.append('file', file);
    if (description) {
      formData.append('description', description);
    }

    const response = await apiClient.post(`${this.BASE_URL}/${id}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  /**
   * Download contract attachment
   */
  static async downloadAttachment(contractId: string, attachmentId: string): Promise<Blob> {
    const response = await apiClient.get(
      `${this.BASE_URL}/${contractId}/attachments/${attachmentId}/download`,
      {
        responseType: 'blob',
      }
    );
    return response.data;
  }

  /**
   * Delete contract attachment
   */
  static async deleteAttachment(contractId: string, attachmentId: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${contractId}/attachments/${attachmentId}`);
  }

  /**
   * Add comment to contract
   */
  static async addComment(
    id: string,
    content: string,
    type?: string,
    isInternal?: boolean,
    position?: any
  ) {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/comments`, {
      content,
      type,
      isInternal,
      position,
    });
    return response.data;
  }

  /**
   * Update comment
   */
  static async updateComment(contractId: string, commentId: string, content: string) {
    const response = await apiClient.put(`${this.BASE_URL}/${contractId}/comments/${commentId}`, {
      content,
    });
    return response.data;
  }

  /**
   * Delete comment
   */
  static async deleteComment(contractId: string, commentId: string): Promise<void> {
    await apiClient.delete(`${this.BASE_URL}/${contractId}/comments/${commentId}`);
  }

  /**
   * Resolve comment
   */
  static async resolveComment(contractId: string, commentId: string): Promise<void> {
    await apiClient.patch(`${this.BASE_URL}/${contractId}/comments/${commentId}/resolve`);
  }

  /**
   * Export contract to PDF
   */
  static async exportToPdf(id: string): Promise<Blob> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/export/pdf`, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Export contract to Word
   */
  static async exportToWord(id: string): Promise<Blob> {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/export/word`, {
      responseType: 'blob',
    });
    return response.data;
  }

  /**
   * Send contract for review
   */
  static async sendForReview(id: string, reviewerIds: string[], message?: string) {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/send-for-review`, {
      reviewerIds,
      message,
    });
    return response.data;
  }

  /**
   * Send contract for approval
   */
  static async sendForApproval(id: string, approverIds: string[], message?: string) {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/send-for-approval`, {
      approverIds,
      message,
    });
    return response.data;
  }

  /**
   * Send contract for signature
   */
  static async sendForSignature(id: string, signers: any[], message?: string) {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/send-for-signature`, {
      signers,
      message,
    });
    return response.data;
  }

  /**
   * Cancel contract
   */
  static async cancelContract(id: string, reason: string) {
    const response = await apiClient.patch(`${this.BASE_URL}/${id}/cancel`, {
      reason,
    });
    return response.data;
  }

  /**
   * Terminate contract
   */
  static async terminateContract(id: string, reason: string, terminationDate?: string) {
    const response = await apiClient.patch(`${this.BASE_URL}/${id}/terminate`, {
      reason,
      terminationDate,
    });
    return response.data;
  }

  /**
   * Renew contract
   */
  static async renewContract(id: string, newEndDate: string, terms?: string) {
    const response = await apiClient.post(`${this.BASE_URL}/${id}/renew`, {
      newEndDate,
      terms,
    });
    return response.data;
  }

  /**
   * Get contracts expiring soon
   */
  static async getExpiringSoon(days = 30) {
    const response = await apiClient.get(`${this.BASE_URL}/expiring-soon`, {
      params: { days },
    });
    return response.data;
  }

  /**
   * Search contracts
   */
  static async searchContracts(query: string, filters?: any) {
    const response = await apiClient.get(`${this.BASE_URL}/search`, {
      params: { q: query, ...filters },
    });
    return response.data;
  }

  /**
   * Get contract activity log
   */
  static async getActivityLog(id: string) {
    const response = await apiClient.get(`${this.BASE_URL}/${id}/activity`);
    return response.data;
  }

  /**
   * Validate contract data
   */
  static async validateContract(data: ContractCreateDto | ContractUpdateDto) {
    const response = await apiClient.post(`${this.BASE_URL}/validate`, data);
    return response.data;
  }
}
