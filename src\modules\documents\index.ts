/**
 * Documents Module
 *
 * Module quản lý tài liệu với OpenAI integration
 */

export * from './pages';
export * from './components';
export * from './types';
export * from './hooks';
export * from './api';
export * from './services';
export * from './constants';
export * from './routers/documentsRouters';

// Export specific components
export { default as DocumentsPage } from './pages/DocumentsPage';
export { default as DocumentForm } from './components/DocumentForm';

// Export service types with aliases to avoid conflicts
export type {
  CreateVirtualWarehouseDto as ServiceCreateVirtualWarehouseDto,
  UpdateVirtualWarehouseDto as ServiceUpdateVirtualWarehouseDto,
} from './services/user-virtual-warehouse.service';
