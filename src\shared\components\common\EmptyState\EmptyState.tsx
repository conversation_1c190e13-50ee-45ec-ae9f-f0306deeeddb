import React from 'react';

import { Icon, IconName, Typography } from '@/shared/components/common';

export interface EmptyStateProps {
  /**
   * Icon hiển thị ở trên cùng
   */
  icon?: IconName;
  
  /**
   * Tiêu đề của trạng thái trống
   */
  title: string;
  
  /**
   * Mô tả chi tiết
   */
  description?: string;
  
  /**
   * Các action có thể thực hiện
   */
  actions?: React.ReactNode;
  
  /**
   * Class CSS bổ sung
   */
  className?: string;
}

/**
 * Component hiển thị trạng thái trống (không có dữ liệu)
 */
const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  actions,
  className = '',
}) => {
  return (
    <div className={`flex flex-col items-center justify-center p-6 text-center ${className}`}>
      {icon && (
        <div className="mb-4 text-gray-400">
          <Icon name={icon} size="xl" />
        </div>
      )}
      
      <Typography variant="h6" className="mb-2">
        {title}
      </Typography>
      
      {description && (
        <Typography variant="body2" className="text-gray-500 mb-4 max-w-md">
          {description}
        </Typography>
      )}
      
      {actions && <div className="mt-2">{actions}</div>}
    </div>
  );
};

export default EmptyState;
