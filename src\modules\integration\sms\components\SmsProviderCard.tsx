import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Typography,
  Button,
  Icon,
  Badge,
  IconName,
} from '@/shared/components/common';

import { SMS_PROVIDER_TEMPLATES } from '../constants';
import { SmsProviderConfig, SmsProviderStatus } from '../types';

interface SmsProviderCardProps {
  /**
   * SMS Provider configuration
   */
  provider: SmsProviderConfig;

  /**
   * Callback when edit button is clicked
   */
  onEdit?: (provider: SmsProviderConfig) => void;

  /**
   * Callback when delete button is clicked
   */
  onDelete?: (provider: SmsProviderConfig) => void;

  /**
   * Callback when test button is clicked
   */
  onTest?: (provider: SmsProviderConfig) => void;

  /**
   * Callback when toggle status button is clicked
   */
  onToggleStatus?: (provider: SmsProviderConfig) => void;

  /**
   * Loading state
   */
  loading?: boolean;

  /**
   * Additional CSS classes
   */
  className?: string;
}

/**
 * Card component hiển thị thông tin SMS Provider
 */
const SmsProviderCard: React.FC<SmsProviderCardProps> = ({
  provider,
  onEdit,
  onDelete,
  onTest,
  onToggleStatus,
  loading = false,
  className = '',
}) => {
  const { t } = useTranslation(['integration', 'common']);

  // Get provider template for display info
  const template = SMS_PROVIDER_TEMPLATES[provider.type];

  // Status badge configuration
  const getStatusConfig = (status: SmsProviderStatus) => {
    switch (status) {
      case 'active':
        return {
          variant: 'success' as const,
          label: t('integration:sms.status.active', 'Hoạt động'),
          icon: 'check-circle' as IconName,
        };
      case 'inactive':
        return {
          variant: 'secondary' as const,
          label: t('integration:sms.status.inactive', 'Tạm dừng'),
          icon: 'pause-circle' as IconName,
        };
      case 'error':
        return {
          variant: 'danger' as const,
          label: t('integration:sms.status.error', 'Lỗi'),
          icon: 'alert-circle' as IconName,
        };
      case 'testing':
        return {
          variant: 'warning' as const,
          label: t('integration:sms.status.testing', 'Đang test'),
          icon: 'loader' as IconName,
        };
      case 'pending':
        return {
          variant: 'info' as const,
          label: t('integration:sms.status.pending', 'Chờ xử lý'),
          icon: 'clock' as IconName,
        };
      default:
        return {
          variant: 'info' as const,
          label: status,
          icon: 'help-circle' as IconName,
        };
    }
  };

  const statusConfig = getStatusConfig(provider.status);

  return (
    <Card
      className={`
        ${className}
        relative
        transition-all
        duration-200
        hover:shadow-lg
        ${provider.isDefault ? 'ring-2 ring-primary/20' : ''}
        ${loading ? 'opacity-50 pointer-events-none' : ''}
      `}
      variant="elevated"
    >
      {/* Default badge */}
      {provider.isDefault && (
        <div className="absolute top-3 right-3">
          <Badge variant="primary" size="sm">
            <Icon name="star" size="xs" className="mr-1" />
            {t('integration:sms.default', 'Mặc định')}
          </Badge>
        </div>
      )}

      <div className="p-6">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-gradient-to-br from-primary/10 to-primary/5 rounded-lg flex items-center justify-center">
                <Icon 
                  name={template.icon as IconName} 
                  size="lg" 
                  className="text-primary" 
                />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <Typography variant="h6" className="font-semibold truncate">
                {provider.displayName}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground">
                {template.displayName}
              </Typography>
            </div>
          </div>

          {/* Status Badge */}
          <Badge variant={statusConfig.variant} size="sm">
            <Icon name={statusConfig.icon} size="xs" className="mr-1" />
            {statusConfig.label}
          </Badge>
        </div>

        {/* Description */}
        {provider.description && (
          <Typography variant="body2" className="text-muted-foreground mb-4 line-clamp-2">
            {provider.description}
          </Typography>
        )}

        {/* Provider Info */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center justify-between text-sm">
            <Typography variant="body2" className="text-muted-foreground">
              {t('integration:sms.provider', 'Nhà cung cấp')}:
            </Typography>
            <Typography variant="body2" className="font-medium">
              {template.displayName}
            </Typography>
          </div>

          {provider.settings.fromNumber && (
            <div className="flex items-center justify-between text-sm">
              <Typography variant="body2" className="text-muted-foreground">
                {t('integration:sms.fromNumber', 'Số gửi')}:
              </Typography>
              <Typography variant="body2" className="font-medium font-mono">
                {provider.settings.fromNumber}
              </Typography>
            </div>
          )}

          {provider.settings.fromName && (
            <div className="flex items-center justify-between text-sm">
              <Typography variant="body2" className="text-muted-foreground">
                {t('integration:sms.fromName', 'Tên gửi')}:
              </Typography>
              <Typography variant="body2" className="font-medium">
                {provider.settings.fromName}
              </Typography>
            </div>
          )}

          {provider.lastTestedAt && (
            <div className="flex items-center justify-between text-sm">
              <Typography variant="body2" className="text-muted-foreground">
                {t('integration:sms.lastTested', 'Test lần cuối')}:
              </Typography>
              <Typography variant="body2" className="font-medium">
                {new Date(provider.lastTestedAt).toLocaleDateString('vi-VN')}
              </Typography>
            </div>
          )}
        </div>

        {/* Test Result */}
        {provider.testResult && (
          <div className={`
            p-3 rounded-lg mb-4 border
            ${provider.testResult.success 
              ? 'bg-success/5 border-success/20 text-success-foreground' 
              : 'bg-destructive/5 border-destructive/20 text-destructive-foreground'
            }
          `}>
            <div className="flex items-center space-x-2">
              <Icon 
                name={provider.testResult.success ? 'check-circle' : 'alert-circle'} 
                size="sm" 
              />
              <Typography variant="body2" className="font-medium">
                {provider.testResult.success 
                  ? t('integration:sms.testSuccess', 'Test thành công')
                  : t('integration:sms.testFailed', 'Test thất bại')
                }
              </Typography>
            </div>
            {provider.testResult.message && (
              <Typography variant="body2" className="mt-1 text-sm opacity-80">
                {provider.testResult.message}
              </Typography>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex items-center space-x-2">
            {onTest && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onTest(provider)}
                disabled={loading || provider.status === 'testing'}
              >
                <Icon name="zap" size="xs" className="mr-1" />
                {t('integration:sms.test', 'Test')}
              </Button>
            )}

            {onToggleStatus && (
              <Button
                variant={provider.status === 'active' ? 'outline' : 'primary'}
                size="sm"
                onClick={() => onToggleStatus(provider)}
                disabled={loading}
              >
                <Icon 
                  name={provider.status === 'active' ? 'pause' : 'play'} 
                  size="xs" 
                  className="mr-1" 
                />
                {provider.status === 'active' 
                  ? t('integration:sms.deactivate', 'Tạm dừng')
                  : t('integration:sms.activate', 'Kích hoạt')
                }
              </Button>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onEdit(provider)}
                disabled={loading}
              >
                <Icon name="edit" size="xs" />
              </Button>
            )}

            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onDelete(provider)}
                disabled={loading || provider.isDefault}
                className="text-destructive hover:text-destructive"
              >
                <Icon name="trash-2" size="xs" />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Loading overlay */}
      {loading && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center rounded-lg">
          <Icon name="loader" size="lg" className="animate-spin text-primary" />
        </div>
      )}
    </Card>
  );
};

export default SmsProviderCard;
