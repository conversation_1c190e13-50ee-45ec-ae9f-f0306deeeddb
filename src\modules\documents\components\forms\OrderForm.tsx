import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  IconCard,
  Select,
  Textarea,
  Typography,
  Divider,
} from '@/shared/components/common';
import { formatTimestamp } from '@/shared/utils/date';

import { Order, OrderStatus, PaymentMethod } from '../../services/order.service';

interface OrderFormProps {
  order?: Order | null;
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
  isSubmitting: boolean;
  isViewMode?: boolean;
}

/**
 * Form tạo/chỉnh sửa đơn hàng
 */
const OrderForm: React.FC<OrderFormProps> = ({
  order,
  onSubmit,
  onCancel,
  isSubmitting,
  isViewMode = false,
}) => {
  const { t } = useTranslation(['business', 'common']);
  const isEditMode = !!order;

  // Giá trị mặc định cho form
  const defaultValues = {
    customerId: order?.customerId || '',
    customerName: order?.customerInfo?.name || '',
    customerEmail: order?.customerInfo?.email || '',
    customerPhone: order?.customerInfo?.phone || '',
    customerAddress: order?.customerInfo?.address || '',
    status: order?.status || OrderStatus.PENDING,
    paymentMethod: order?.paymentMethod || PaymentMethod.CASH,
    paymentStatus: order?.paymentStatus || 'UNPAID',
    notes: order?.notes || '',
  };

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    onSubmit(values as Record<string, unknown>);
  };

  // Render danh sách sản phẩm trong đơn hàng
  const renderOrderItems = () => {
    if (!order || !order.items || order.items.length === 0) {
      return (
        <div className="text-center py-4">
          <Typography variant="body2">{t('business:order.noItems')}</Typography>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {order.items.map((item) => (
          <div key={item.id} className="flex justify-between items-center p-2 border-b">
            <div className="flex-1">
              <Typography variant="subtitle2">{item.productName}</Typography>
              <Typography variant="caption">
                {t('business:order.quantity')}: {item.quantity}
              </Typography>
            </div>
            <div className="text-right">
              <Typography variant="body2">
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                }).format(item.price)}
              </Typography>
              <Typography variant="subtitle2" className="font-semibold">
                {new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND',
                }).format(item.totalPrice)}
              </Typography>
            </div>
          </div>
        ))}
        <div className="flex justify-between items-center p-2 font-semibold">
          <Typography variant="subtitle1">{t('business:order.totalAmount')}</Typography>
          <Typography variant="subtitle1">
            {new Intl.NumberFormat('vi-VN', {
              style: 'currency',
              currency: 'VND',
            }).format(order.totalAmount)}
          </Typography>
        </div>
      </div>
    );
  };

  return (
    <Card>
      <div className="p-6">
        <Typography variant="h5" className="mb-4">
          {isViewMode
            ? t('business:order.viewOrder')
            : isEditMode
            ? t('business:order.editOrder')
            : t('business:order.createOrder')}
        </Typography>

        {isEditMode && (
          <div className="mb-4">
            <div className="flex justify-between mb-2">
              <Typography variant="subtitle2">{t('business:order.orderNumber')}</Typography>
              <Typography variant="body2">{order?.orderNumber}</Typography>
            </div>
            <div className="flex justify-between mb-2">
              <Typography variant="subtitle2">{t('common:createdAt')}</Typography>
              <Typography variant="body2">{formatTimestamp(order?.createdAt || 0)}</Typography>
            </div>
          </div>
        )}

        <Form
          defaultValues={defaultValues}
          onSubmit={handleSubmit}
        >
          <Typography variant="subtitle1" className="mb-2">
            {t('business:order.customerInfo')}
          </Typography>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <FormItem
              name="customerName"
              label={t('business:order.customerName')}
              required={!isViewMode}
            >
              <Input placeholder={t('business:order.form.customerNamePlaceholder')} disabled={isViewMode} />
            </FormItem>

            <FormItem
              name="customerEmail"
              label={t('business:order.customerEmail')}
              required={!isViewMode}
            >
              <Input placeholder={t('business:order.form.customerEmailPlaceholder')} disabled={isViewMode} />
            </FormItem>

            <FormItem
              name="customerPhone"
              label={t('business:order.customerPhone')}
              required={!isViewMode}
            >
              <Input placeholder={t('business:order.form.customerPhonePlaceholder')} disabled={isViewMode} />
            </FormItem>

            <FormItem
              name="customerAddress"
              label={t('business:order.customerAddress')}
            >
              <Input placeholder={t('business:order.form.customerAddressPlaceholder')} disabled={isViewMode} />
            </FormItem>
          </div>

          {isEditMode && (
            <>
              <Divider className="my-4" />
              <Typography variant="subtitle1" className="mb-2">
                {t('business:order.items')}
              </Typography>
              {renderOrderItems()}
              <Divider className="my-4" />
            </>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <FormItem
              name="status"
              label={t('business:order.status.title')}
              required={!isViewMode}
            >
              <Select
                options={[
                  { value: OrderStatus.PENDING, label: t('business:order.status.pending') },
                  { value: OrderStatus.PROCESSING, label: t('business:order.status.processing') },
                  { value: OrderStatus.COMPLETED, label: t('business:order.status.completed') },
                  { value: OrderStatus.CANCELLED, label: t('business:order.status.cancelled') },
                  { value: OrderStatus.REFUNDED, label: t('business:order.status.refunded') }
                ]}
                disabled={isViewMode}
              />
            </FormItem>

            <FormItem
              name="paymentMethod"
              label={t('business:order.paymentMethod')}
              required={!isViewMode}
            >
              <Select
                options={[
                  { value: PaymentMethod.CASH, label: t('business:order.paymentMethods.cash') },
                  { value: PaymentMethod.CREDIT_CARD, label: t('business:order.paymentMethods.creditCard') },
                  { value: PaymentMethod.BANK_TRANSFER, label: t('business:order.paymentMethods.bankTransfer') },
                  { value: PaymentMethod.DIGITAL_WALLET, label: t('business:order.paymentMethods.digitalWallet') }
                ]}
                disabled={isViewMode}
              />
            </FormItem>

            <FormItem
              name="paymentStatus"
              label={t('business:order.paymentStatus.title')}
              required={!isViewMode}
            >
              <Select
                options={[
                  { value: "PAID", label: t('business:order.paymentStatus.paid') },
                  { value: "UNPAID", label: t('business:order.paymentStatus.unpaid') },
                  { value: "PARTIALLY_PAID", label: t('business:order.paymentStatus.partiallyPaid') }
                ]}
                disabled={isViewMode}
              />
            </FormItem>
          </div>

          <FormItem
            name="notes"
            label={t('business:order.notes')}
          >
            <Textarea
              placeholder={t('business:order.form.notesPlaceholder')}
              rows={4}
              disabled={isViewMode}
            />
          </FormItem>

          <div className="flex justify-end space-x-2 pt-4">
            <IconCard
              icon="x"
              variant="secondary"
              size="md"
              title={t('common:cancel')}
              onClick={onCancel}
              disabled={isSubmitting}
            />
            {!isViewMode && (
              <Button
                type="submit"
                isLoading={isSubmitting}
              >
                {isEditMode ? t('common:update') : t('common:save')}
              </Button>
            )}
          </div>
        </Form>
      </div>
    </Card>
  );
};

export default OrderForm;
