import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import { CommentService, TodoCommentService } from '../services/comment.service';
import {
  CommentQueryDto,
  CreateCommentDto,
  CreateSystemEventDto,
  CreateTodoCommentDto,
  TodoCommentQueryDto,
  UpdateCommentDto,
} from '../types/comment.types';

// Query Keys
const TODO_COMMENTS_QUERY_KEY = 'todo-comments';
const COMMENTS_QUERY_KEY = 'comments'; // Legacy

/**
 * Hook to get all todo comments with pagination
 * @param params Query parameters
 * @returns Query result with comments
 */
export const useTodoComments = (params?: TodoCommentQueryDto) => {
  return useQuery({
    queryKey: [TODO_COMMENTS_QUERY_KEY, 'all', params],
    queryFn: () => TodoCommentService.getAllComments(params),
    select: data => data.result,
  });
};

/**
 * Hook to get comments for a specific todo
 * @param todoId Todo ID
 * @returns Query result with comments
 */
export const useTodoCommentsByTodoId = (todoId: number) => {
  return useQuery({
    queryKey: [TODO_COMMENTS_QUERY_KEY, 'by-todo', todoId],
    queryFn: () => TodoCommentService.getCommentsByTodoId(todoId),
    select: data => data.result,
    enabled: !!todoId,
  });
};

/**
 * Hook to get a todo comment by ID
 * @param commentId Comment ID
 * @returns Query result with the comment
 */
export const useTodoComment = (commentId: number) => {
  return useQuery({
    queryKey: [TODO_COMMENTS_QUERY_KEY, commentId],
    queryFn: () => TodoCommentService.getCommentById(commentId),
    select: data => data.result,
    enabled: !!commentId,
  });
};

/**
 * Hook to get replies for a comment
 * @param commentId Parent comment ID
 * @returns Query result with replies
 */
export const useTodoCommentReplies = (commentId: number) => {
  return useQuery({
    queryKey: [TODO_COMMENTS_QUERY_KEY, commentId, 'replies'],
    queryFn: () => TodoCommentService.getCommentReplies(commentId),
    select: data => data.result,
    enabled: !!commentId,
  });
};

/**
 * Hook to create a new todo comment with optimistic update
 * @returns Mutation result for creating a comment
 */
export const useCreateTodoComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTodoCommentDto) => {
      console.log('useCreateTodoComment mutation called with:', data);
      return TodoCommentService.createComment(data);
    },
    onMutate: async (variables) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({
        queryKey: [TODO_COMMENTS_QUERY_KEY, 'by-todo', variables.todoId],
      });

      // Snapshot the previous value
      const previousComments = queryClient.getQueryData([
        TODO_COMMENTS_QUERY_KEY,
        'by-todo',
        variables.todoId,
      ]);

      // Create optimistic comment
      const optimisticComment = {
        id: Date.now(), // Temporary ID
        todoId: variables.todoId,
        userId: null, // Will be filled by backend
        contentHtml: variables.contentHtml,
        createdAt: Date.now(),
        parentId: variables.parentId || null,
        commentType: variables.commentType || null,
        isSystemEvent: false,
        eventData: null,
        resources: variables.resources || null,
        mentions: variables.mentions || null,
        // Add pending flag for UI
        isPending: true,
      };

      // Optimistically update the cache
      queryClient.setQueryData(
        [TODO_COMMENTS_QUERY_KEY, 'by-todo', variables.todoId],
        (old: any) => {
          if (!old) return [optimisticComment];
          return [...old, optimisticComment];
        }
      );

      // Return a context object with the snapshotted value
      return { previousComments, optimisticComment };
    },
    onError: (err, variables, context) => {
      console.error('useCreateTodoComment error:', err);
      console.error('Error details:', {
        message: err.message,
        response: (err as any)?.response?.data,
        status: (err as any)?.response?.status,
      });

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousComments) {
        queryClient.setQueryData(
          [TODO_COMMENTS_QUERY_KEY, 'by-todo', variables.todoId],
          context.previousComments
        );
      }
    },
    onSuccess: (response, variables, context) => {
      console.log('useCreateTodoComment success:', response);

      // Replace the optimistic comment with the real one from server
      queryClient.setQueryData(
        [TODO_COMMENTS_QUERY_KEY, 'by-todo', variables.todoId],
        (old: any) => {
          if (!old) return [response.result];
          return old.map((comment: any) =>
            comment.id === context?.optimisticComment?.id
              ? response.result
              : comment
          );
        }
      );

      // Invalidate related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: [TODO_COMMENTS_QUERY_KEY] });
      if (variables.parentId) {
        queryClient.invalidateQueries({
          queryKey: [TODO_COMMENTS_QUERY_KEY, variables.parentId, 'replies'],
        });
      }
    },
    onSettled: (_, __, variables) => {
      // Always refetch after error or success to ensure consistency
      queryClient.invalidateQueries({
        queryKey: [TODO_COMMENTS_QUERY_KEY, 'by-todo', variables.todoId],
      });
    },
  });
};

/**
 * Hook to create a system event
 * @returns Mutation result for creating a system event
 */
export const useCreateSystemEvent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateSystemEventDto) => TodoCommentService.createSystemEvent(data),
    onSuccess: (_, variables) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: [TODO_COMMENTS_QUERY_KEY] });
      queryClient.invalidateQueries({
        queryKey: [TODO_COMMENTS_QUERY_KEY, 'by-todo', variables.todoId],
      });
    },
  });
};

/**
 * Hook to delete a todo comment
 * @returns Mutation result for deleting a comment
 */
export const useDeleteTodoComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (commentId: number) => TodoCommentService.deleteComment(commentId),
    onSuccess: () => {
      // Invalidate all comment queries
      queryClient.invalidateQueries({ queryKey: [TODO_COMMENTS_QUERY_KEY] });
    },
  });
};

// Legacy hooks for backward compatibility
/**
 * Hook to get comments for a task (legacy)
 * @param taskId Task ID
 * @param params Query parameters
 * @returns Query result with comments
 */
export const useComments = (taskId: number, params?: CommentQueryDto) => {
  return useQuery({
    queryKey: [COMMENTS_QUERY_KEY, taskId, params],
    queryFn: () => CommentService.getComments(taskId, params),
    select: data => data.result,
    enabled: !!taskId,
  });
};

/**
 * Hook to get a comment by ID (legacy)
 * @param taskId Task ID
 * @param commentId Comment ID
 * @returns Query result with the comment
 */
export const useComment = (taskId: number, commentId: number) => {
  return useQuery({
    queryKey: [COMMENTS_QUERY_KEY, taskId, commentId],
    queryFn: () => CommentService.getComment(taskId, commentId),
    select: data => data.result,
    enabled: !!taskId && !!commentId,
  });
};

/**
 * Hook to create a new comment (legacy)
 * @returns Mutation result for creating a comment
 */
export const useCreateComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCommentDto) => CommentService.createComment(data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [COMMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};

/**
 * Hook to update a comment (legacy)
 * @returns Mutation result for updating a comment
 */
export const useUpdateComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      taskId,
      commentId,
      data,
    }: {
      taskId: number;
      commentId: number;
      data: UpdateCommentDto;
    }) => CommentService.updateComment(taskId, commentId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [COMMENTS_QUERY_KEY, variables.taskId] });
      queryClient.invalidateQueries({
        queryKey: [COMMENTS_QUERY_KEY, variables.taskId, variables.commentId],
      });
    },
  });
};

/**
 * Hook to delete a comment (legacy)
 * @returns Mutation result for deleting a comment
 */
export const useDeleteComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ taskId, commentId }: { taskId: number; commentId: number }) =>
      CommentService.deleteComment(taskId, commentId),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: [COMMENTS_QUERY_KEY, variables.taskId] });
    },
  });
};
