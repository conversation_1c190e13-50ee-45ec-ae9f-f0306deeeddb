import React, { useState, useEffect, useMemo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';

import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { Card, Button, Input, FormItem, Icon, Typography } from '@/shared/components/common';
import { zodResolver } from '@hookform/resolvers/zod';

import { createProviderModelSchema } from '../schemas/provider-model.schema';
import {
  ProviderModel,
  CreateProviderModelDto,
  UpdateProviderModelDto,
  getProviderIcon
} from '../types/provider-model.types';

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
        } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-gray-900 dark:text-gray-100">{name}</div>
      </div>
    </Card>
  );
};

interface ProviderModelFormProps {
  /**
   * Dữ liệu ban đầu cho form (khi chỉnh sửa)
   */
  initialData?: ProviderModel | null;

  /**
   * Hàm xử lý khi submit form
   */
  onSubmit: (values: CreateProviderModelDto | UpdateProviderModelDto) => void;

  /**
   * Hàm xử lý khi hủy form
   */
  onCancel: () => void;

  /**
   * Trạng thái đang submit form
   */
  isSubmitting?: boolean;

  /**
   * Chế độ chỉ đọc
   */
  readOnly?: boolean;
}

type FormData = {
  name: string;
  type: TypeProviderEnum;
  apiKey?: string;
};

/**
 * Form tạo/chỉnh sửa Provider Model
 */
const ProviderModelForm: React.FC<ProviderModelFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isSubmitting = false,
  readOnly = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const isEditMode = !!initialData;

  // State để quản lý provider được chọn
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(
    initialData?.type || TypeProviderEnum.OPENAI
  );

  // Tạo schema động dựa trên mode
  const formSchema = useMemo(() => {
    if (isEditMode) {
      // Trong edit mode, API key không bắt buộc
      return z.object({
        name: z
          .string()
          .min(1, 'Tên không được để trống')
          .max(255, 'Tên không được vượt quá 255 ký tự')
          .trim(),
        type: z.nativeEnum(TypeProviderEnum),
        apiKey: z
          .string()
          .optional()
          .refine((val) => !val || val.length >= 10, {
            message: 'API key phải có ít nhất 10 ký tự'
          }),
      });
    }
    return createProviderModelSchema;
  }, [isEditMode]);

  // Setup form với react-hook-form
  const {
    control,
    handleSubmit,
    setValue,
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    mode: 'onChange',
    defaultValues: {
      name: initialData?.name || '',
      type: selectedProvider,
      apiKey: '', // Không hiển thị API key cũ vì lý do bảo mật
    },
  });

  // Cập nhật selectedProvider khi initialData thay đổi
  useEffect(() => {
    if (initialData?.type) {
      setSelectedProvider(initialData.type);
      setValue('type', initialData.type);
    }
  }, [initialData?.type, setValue]);

  // Xử lý submit form
  const handleFormSubmit = (data: FormData) => {
    if (isEditMode) {
      // Chỉ gửi các field đã thay đổi cho update
      const updateData: UpdateProviderModelDto = {};
      if (data.name && data.name.trim()) {updateData.name = data.name.trim();}
      if (data.apiKey && data.apiKey.trim()) {updateData.apiKey = data.apiKey.trim();}
      onSubmit(updateData);
    } else {
      // Gửi đầy đủ data cho create
      const createData: CreateProviderModelDto = {
        name: data.name.trim(),
        type: selectedProvider,
        apiKey: data.apiKey?.trim() || '',
      };
      onSubmit(createData);
    }
  };

  // Xử lý khi chọn provider
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    if (!readOnly && !isSubmitting && !isEditMode) {
      setSelectedProvider(provider);
    }
  };

  // Danh sách providers với tên hiển thị
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'Google' },
    { type: TypeProviderEnum.META, name: 'Meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  return (
    <div className="space-y-6">
      {/* Header với Title */}
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <div className="flex items-center space-x-3">
          <Icon
            name={readOnly ? "eye" : isEditMode ? "edit" : "plus"}
            size="lg"
            className="text-primary dark:text-primary-400"
          />
          <Typography variant="h5" className="font-semibold text-gray-900 dark:text-white">
            {readOnly
              ? t('admin:integration.providerModel.form.view', 'Xem Provider Model')
              : isEditMode
                ? t('admin:integration.providerModel.form.edit', 'Chỉnh sửa Provider Model')
                : t('admin:integration.providerModel.form.create', 'Tạo Provider Model')}
          </Typography>
        </div>
        <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mt-2">
          {readOnly
            ? t('admin:integration.providerModel.form.viewDescription', 'Xem thông tin chi tiết của Provider Model')
            : isEditMode
              ? t('admin:integration.providerModel.form.editDescription', 'Chỉnh sửa thông tin Provider Model. Chỉ có thể thay đổi tên và API key.')
              : t('admin:integration.providerModel.form.createDescription', 'Tạo mới Provider Model để tích hợp với các nhà cung cấp AI')}
        </Typography>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        <div className="space-y-6">

          {/* Provider Type */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              {t('admin:integration.providerModel.form.fields.type')}
              {!isEditMode && <span className="text-red-500 ml-1">*</span>}
            </label>

            {isEditMode ? (
              // Read-only display for edit mode
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                <div className="flex items-center">
                  <div className="mr-3">
                    <Icon name={getProviderIcon(selectedProvider)} size="md" />
                  </div>
                  <div className="font-medium text-gray-900 dark:text-gray-100">
                    {providers.find(p => p.type === selectedProvider)?.name}
                  </div>
                  <div className="ml-auto text-sm text-gray-500 dark:text-gray-400">
                    (Không thể thay đổi)
                  </div>
                </div>
              </div>
            ) : (
              // Selectable cards for create mode
              <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
                {providers.map((provider) => (
                  <ProviderCard
                    key={provider.type}
                    provider={provider.type}
                    name={provider.name}
                    isSelected={selectedProvider === provider.type}
                    onClick={handleProviderSelect}
                    disabled={readOnly || isSubmitting}
                  />
                ))}
              </div>
            )}

            {/* Hidden input để Form component có thể track giá trị */}
            <div className="hidden">
              <FormItem name="type">
                <Input value={selectedProvider} readOnly />
              </FormItem>
            </div>
          </div>

          {/* Provider Name */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Tên Provider Model
              <span className="text-red-500 ml-1">*</span>
            </label>
            <Controller
              name="name"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  placeholder="Nhập tên provider model"
                  disabled={readOnly || isSubmitting}
                  fullWidth
                  error={fieldState.error?.message}
                />
              )}
            />
          </div>

          {/* API Key */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              API Key
              {!isEditMode && <span className="text-red-500 ml-1">*</span>}
            </label>
            <Controller
              name="apiKey"
              control={control}
              render={({ field, fieldState }) => (
                <Input
                  {...field}
                  type="password"
                  placeholder={
                    isEditMode
                      ? 'Để trống nếu không muốn thay đổi API key...'
                      : 'Nhập API key'
                  }
                  disabled={readOnly || isSubmitting}
                  fullWidth
                  error={fieldState.error?.message}
                />
              )}
            />
          </div>

          {/* Hiển thị thông tin bổ sung khi ở chế độ xem */}
          {readOnly && initialData && (
            <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdAt')}:
                  </span>
                  <div className="mt-1">
                    {new Date(initialData.createdAt).toLocaleString()}
                  </div>
                </div>
                <div>
                  <span className="font-medium text-gray-500 dark:text-gray-400">
                    {t('common:createdBy')}:
                  </span>
                  <div className="mt-1">
                    {initialData.createdBy?.name || 'N/A'}
                  </div>
                </div>
                {initialData.updatedAt && (
                  <>
                    <div>
                      <span className="font-medium text-gray-500 dark:text-gray-400">
                        {t('common:updatedAt')}:
                      </span>
                      <div className="mt-1">
                        {new Date(initialData.updatedAt).toLocaleString()}
                      </div>
                    </div>
                    {initialData.updatedBy && (
                      <div>
                        <span className="font-medium text-gray-500 dark:text-gray-400">
                          {t('common:updatedBy')}:
                        </span>
                        <div className="mt-1">
                          {initialData.updatedBy.name}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
            {t('admin:integration.providerModel.actions.cancel')}
          </Button>
          {!readOnly && (
            <Button type="submit" variant="primary" isLoading={isSubmitting}>
              {t('admin:integration.providerModel.actions.save')}
            </Button>
          )}
        </div>
      </form>
    </div>
  );
};

export default ProviderModelForm;
