import { useTranslation } from 'react-i18next';

import { NotificationUtil } from '@/shared/utils/notification';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

import {
  UserFileService,
  QueryFileDto,
  CreateFileDto,
  UpdateFileDto
} from '../services/user-file.service';

/**
 * Query keys cho file
 */
export const FILE_QUERY_KEYS = {
  ALL: ['files'] as const,
  LIST: (params: QueryFileDto) => [...FILE_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: number) => [...FILE_QUERY_KEYS.ALL, 'detail', id] as const,
  BY_FOLDER: (folderId: number) => [...FILE_QUERY_KEYS.ALL, 'by-folder', folderId] as const,
};

/**
 * Hook lấy danh sách file với phân trang
 */
export const useFiles = (params: QueryFileDto) => {
  return useQuery({
    queryKey: FILE_QUERY_KEYS.LIST(params),
    queryFn: () => UserFileService.getFiles(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook lấy danh sách file theo folder ID
 */
export const useFilesByFolder = (folderId: number) => {
  return useQuery({
    queryKey: FILE_QUERY_KEYS.BY_FOLDER(folderId),
    queryFn: () => UserFileService.getFilesByFolderId(folderId),
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook lấy chi tiết file
 */
export const useFileDetail = (id: number) => {
  return useQuery({
    queryKey: FILE_QUERY_KEYS.DETAIL(id),
    queryFn: () => UserFileService.getFileById(id),
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook tạo file mới
 */
export const useCreateFile = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateFileDto) => UserFileService.createFile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.ALL });
      NotificationUtil.success({
        message: t('business:file.messages.createSuccess', 'Tạo file thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:file.messages.createError', 'Có lỗi xảy ra khi tạo file')
      });
    },
  });
};

/**
 * Hook cập nhật file
 */
export const useUpdateFile = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateFileDto }) =>
      UserFileService.updateFile(id, data),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.ALL });
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.DETAIL(id) });
      NotificationUtil.success({
        message: t('business:file.messages.updateSuccess', 'Cập nhật file thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:file.messages.updateError', 'Có lỗi xảy ra khi cập nhật file')
      });
    },
  });
};

/**
 * Hook xóa file
 */
export const useDeleteFile = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => UserFileService.deleteFile(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.ALL });
      NotificationUtil.success({
        message: t('business:file.messages.deleteSuccess', 'Xóa file thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:file.messages.deleteError', 'Có lỗi xảy ra khi xóa file')
      });
    },
  });
};

/**
 * Hook upload file
 */
export const useUploadFile = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ file, folderId, warehouseId }: {
      file: File;
      folderId?: number;
      warehouseId?: number;
    }) => UserFileService.createFileForUpload(file, folderId, warehouseId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.ALL });
      NotificationUtil.success({
        message: t('business:file.messages.uploadSuccess', 'Tải file lên thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:file.messages.uploadError', 'Có lỗi xảy ra khi tải file lên')
      });
    },
  });
};

/**
 * Hook upload multiple files
 */
export const useUploadFiles = () => {
  const { t } = useTranslation(['business', 'common']);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ files, folderId, warehouseId }: {
      files: File[];
      folderId?: number;
      warehouseId?: number;
    }) => Promise.all(files.map(file => UserFileService.createFileForUpload(file, folderId, warehouseId))),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: FILE_QUERY_KEYS.ALL });
      NotificationUtil.success({
        message: t('business:file.messages.uploadMultipleSuccess', 'Tải nhiều file lên thành công')
      });
    },
    onError: () => {
      NotificationUtil.error({
        message: t('business:file.messages.uploadMultipleError', 'Có lỗi xảy ra khi tải nhiều file lên')
      });
    },
  });
};
