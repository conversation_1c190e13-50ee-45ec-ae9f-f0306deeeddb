import React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useNavigate } from 'react-router-dom';

import { 
  Card, 
  Typography, 
  Button, 
  IconCard, 
  Tooltip 
} from '@/shared/components/common';

import { useDocument, useDocumentDownloadUrl } from '../hooks/useDocuments';
import { DocumentType, ProcessingStatus } from '../types/document.types';

/**
 * Trang chi tiết tài liệu
 */
const DocumentDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation(['documents', 'common']);

  const documentId = id ? parseInt(id, 10) : 0;
  
  // Lấy thông tin tài liệu
  const { data: document, isLoading, error } = useDocument(documentId);
  
  // Lấy URL download (chỉ khi cần)
  const { data: downloadData, refetch: getDownloadUrl } = useDocumentDownloadUrl(documentId);

  // Xử lý quay lại
  const handleBack = () => {
    navigate('/documents');
  };

  // Xử lý download
  const handleDownload = async () => {
    try {
      const result = await getDownloadUrl();
      if (result.data?.downloadUrl) {
        window.open(result.data.downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Error getting download URL:', error);
    }
  };

  // Xử lý edit
  const handleEdit = () => {
    // TODO: Implement edit functionality
    console.log('Edit document:', documentId);
  };

  if (isLoading) {
    return (
      <div className="w-full bg-background text-foreground">
        <Card>
          <div className="flex items-center justify-center h-64">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </div>
        </Card>
      </div>
    );
  }

  if (error || !document) {
    return (
      <div className="w-full bg-background text-foreground">
        <Card>
          <div className="text-center py-8">
            <Typography variant="h6" className="text-red-600 mb-4">
              {t('documents:error.notFound', 'Không tìm thấy tài liệu')}
            </Typography>
            <Button variant="outline" onClick={handleBack}>
              {t('common:back', 'Quay lại')}
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  // Mapping cho document type
  const documentTypeLabels = {
    [DocumentType.POLICY]: t('documents:type.policy', 'Chính sách'),
    [DocumentType.PROCEDURE]: t('documents:type.procedure', 'Quy trình'),
    [DocumentType.MANUAL]: t('documents:type.manual', 'Hướng dẫn'),
    [DocumentType.FORM]: t('documents:type.form', 'Biểu mẫu'),
    [DocumentType.TEMPLATE]: t('documents:type.template', 'Mẫu'),
    [DocumentType.REPORT]: t('documents:type.report', 'Báo cáo'),
    [DocumentType.CONTRACT]: t('documents:type.contract', 'Hợp đồng'),
    [DocumentType.PRESENTATION]: t('documents:type.presentation', 'Thuyết trình'),
    [DocumentType.SPREADSHEET]: t('documents:type.spreadsheet', 'Bảng tính'),
    [DocumentType.IMAGE]: t('documents:type.image', 'Hình ảnh'),
    [DocumentType.VIDEO]: t('documents:type.video', 'Video'),
    [DocumentType.AUDIO]: t('documents:type.audio', 'Âm thanh'),
    [DocumentType.OTHER]: t('documents:type.other', 'Khác'),
  };

  // Mapping cho processing status
  const statusConfig = {
    [ProcessingStatus.PENDING]: {
      label: t('documents:status.pending', 'Chờ xử lý'),
      className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400',
    },
    [ProcessingStatus.PROCESSING]: {
      label: t('documents:status.processing', 'Đang xử lý'),
      className: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400',
    },
    [ProcessingStatus.COMPLETED]: {
      label: t('documents:status.completed', 'Hoàn thành'),
      className: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    },
    [ProcessingStatus.FAILED]: {
      label: t('documents:status.failed', 'Thất bại'),
      className: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
    },
    [ProcessingStatus.CANCELLED]: {
      label: t('documents:status.cancelled', 'Đã hủy'),
      className: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    },
  };

  const currentStatus = statusConfig[document.processingStatus];

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={handleBack}>
            <span className="mr-2">←</span>
            {t('common:back', 'Quay lại')}
          </Button>
          <Typography variant="h4">{document.title}</Typography>
        </div>
        
        <div className="flex items-center space-x-2">
          <Tooltip content={t('common:download', 'Tải xuống')}>
            <IconCard
              icon="download"
              variant="primary"
              onClick={handleDownload}
            />
          </Tooltip>
          <Tooltip content={t('common:edit', 'Chỉnh sửa')}>
            <IconCard
              icon="edit"
              variant="outline"
              onClick={handleEdit}
            />
          </Tooltip>
        </div>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2">
          <Card>
            <Typography variant="h6" className="mb-4">
              {t('documents:details.information', 'Thông tin tài liệu')}
            </Typography>
            
            <div className="space-y-4">
              <div>
                <Typography variant="body2" className="text-muted-foreground mb-1">
                  {t('documents:form.description', 'Mô tả')}
                </Typography>
                <Typography variant="body1">
                  {document.description || t('common:noData', 'Không có dữ liệu')}
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-muted-foreground mb-1">
                  {t('documents:form.type', 'Loại tài liệu')}
                </Typography>
                <Typography variant="body1">
                  {documentTypeLabels[document.documentType]}
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-muted-foreground mb-1">
                  {t('documents:table.status', 'Trạng thái')}
                </Typography>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${currentStatus?.className || ''}`}
                >
                  {currentStatus?.label || document.processingStatus}
                </span>
              </div>

              {document.tags && document.tags.length > 0 && (
                <div>
                  <Typography variant="body2" className="text-muted-foreground mb-1">
                    {t('documents:form.tags', 'Thẻ tags')}
                  </Typography>
                  <div className="flex flex-wrap gap-2">
                    {document.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-primary/10 text-primary rounded-md text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Sidebar */}
        <div>
          <Card>
            <Typography variant="h6" className="mb-4">
              {t('documents:details.fileInfo', 'Thông tin file')}
            </Typography>
            
            <div className="space-y-3">
              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('documents:details.fileName', 'Tên file')}
                </Typography>
                <Typography variant="body1" className="break-all">
                  {document.fileName}
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('documents:table.size', 'Kích thước')}
                </Typography>
                <Typography variant="body1">
                  {document.fileSizeMB.toFixed(2)} MB
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('documents:details.mimeType', 'Loại file')}
                </Typography>
                <Typography variant="body1">
                  {document.mimeType}
                </Typography>
              </div>

              {document.pageCount && (
                <div>
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('documents:details.pageCount', 'Số trang')}
                  </Typography>
                  <Typography variant="body1">
                    {document.pageCount}
                  </Typography>
                </div>
              )}

              {document.wordCount && (
                <div>
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('documents:details.wordCount', 'Số từ')}
                  </Typography>
                  <Typography variant="body1">
                    {document.wordCount.toLocaleString()}
                  </Typography>
                </div>
              )}

              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('documents:details.language', 'Ngôn ngữ')}
                </Typography>
                <Typography variant="body1">
                  {document.language}
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('documents:details.privacy', 'Quyền truy cập')}
                </Typography>
                <Typography variant="body1">
                  {document.isPublic 
                    ? t('documents:privacy.public', 'Công khai')
                    : t('documents:privacy.private', 'Riêng tư')
                  }
                </Typography>
              </div>

              <div>
                <Typography variant="body2" className="text-muted-foreground">
                  {t('documents:details.createdAt', 'Ngày tạo')}
                </Typography>
                <Typography variant="body1">
                  {new Date(document.createdAt).toLocaleDateString('vi-VN')}
                </Typography>
              </div>

              {document.updatedAt && (
                <div>
                  <Typography variant="body2" className="text-muted-foreground">
                    {t('documents:details.updatedAt', 'Ngày cập nhật')}
                  </Typography>
                  <Typography variant="body1">
                    {new Date(document.updatedAt).toLocaleDateString('vi-VN')}
                  </Typography>
                </div>
              )}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DocumentDetails;
