import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';

import {
  getFacebookPages,
  createFacebookAuthUrl,
  handleFacebookCallback,
  deleteFacebookPage,
  deleteManyFacebookPages,
  deleteFacebookPersonal,
  connectAgentToFacebookPage,
  disconnectAgentFromFacebookPage,
  getFacebookPageDetail,
  updateFacebookPageStatus,
  syncFacebookPages,
} from '../services/facebook.api';
import {
  FacebookPageDto,
  FacebookPageQueryDto,
  FacebookAuthResponseDto,
  CreateFacebookUrlAuthDto,
} from '../types/facebook.types';

/**
 * Facebook Integration Hooks
 * React Query hooks cho Facebook integration
 */

// Query Keys
export const FACEBOOK_QUERY_KEYS = {
  FACEBOOK_PAGES: 'facebook-pages',
  FACEBOOK_PAGE_DETAIL: 'facebook-page-detail',
  FACEBOOK_AUTH_URL: 'facebook-auth-url',
} as const;

/**
 * Hook để lấy danh sách Facebook Pages
 * @param params Query parameters
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetFacebookPages = (
  params?: FacebookPageQueryDto,
  options?: UseQueryOptions<ApiResponseDto<PaginatedResult<FacebookPageDto>>>
) => {
  return useQuery({
    queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES, params],
    queryFn: () => getFacebookPages(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để lấy chi tiết Facebook Page
 * @param pageId ID của Facebook Page
 * @param options TanStack Query options
 * @returns Query result
 */
export const useGetFacebookPageDetail = (
  pageId: string,
  options?: UseQueryOptions<ApiResponseDto<FacebookPageDto>>
) => {
  return useQuery({
    queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGE_DETAIL, pageId],
    queryFn: () => getFacebookPageDetail(pageId),
    enabled: !!pageId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
};

/**
 * Hook để tạo Facebook Auth URL
 * @param params Parameters cho auth URL
 * @param options TanStack Query options
 * @returns Query result
 */
export const useCreateFacebookAuthUrl = (
  params: CreateFacebookUrlAuthDto,
  options?: UseQueryOptions<ApiResponseDto<FacebookAuthResponseDto>>
) => {
  return useQuery({
    queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_AUTH_URL, params],
    queryFn: () => createFacebookAuthUrl(params),
    enabled: !!params.redirectEndpoint,
    staleTime: 0, // Không cache auth URL
    ...options,
  });
};

/**
 * Hook để xử lý Facebook callback
 * @returns Mutation function
 */
export const useHandleFacebookCallback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { code: string; redirectEndpoint: string }) =>
      handleFacebookCallback(data),
    onSuccess: () => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
    },
  });
};

/**
 * Hook để xóa Facebook Page
 * @returns Mutation function
 */
export const useDeleteFacebookPage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteFacebookPage,
    onSuccess: () => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
    },
  });
};

/**
 * Hook để xóa nhiều Facebook Pages
 * @returns Mutation function
 */
export const useDeleteManyFacebookPages = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { pageIds: string[] }) => deleteManyFacebookPages(data),
    onSuccess: () => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
    },
  });
};

/**
 * Hook để xóa Facebook Personal
 * @returns Mutation function
 */
export const useDeleteFacebookPersonal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteFacebookPersonal,
    onSuccess: () => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
    },
  });
};

/**
 * Hook để kết nối Agent với Facebook Page
 * @returns Mutation function
 */
export const useConnectAgentToFacebookPage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { agentId: string; facebookPageId: string }) =>
      connectAgentToFacebookPage(data),
    onSuccess: (_, variables) => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
      // Invalidate specific page detail
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGE_DETAIL, variables.facebookPageId],
      });
    },
  });
};

/**
 * Hook để ngắt kết nối Agent khỏi Facebook Page
 * @returns Mutation function
 */
export const useDisconnectAgentFromFacebookPage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { facebookPageId: string }) =>
      disconnectAgentFromFacebookPage(data),
    onSuccess: (_, variables) => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
      // Invalidate specific page detail
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGE_DETAIL, variables.facebookPageId],
      });
    },
  });
};

/**
 * Hook để cập nhật trạng thái Facebook Page
 * @returns Mutation function
 */
export const useUpdateFacebookPageStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ pageId, isActive }: { pageId: string; isActive: boolean }) =>
      updateFacebookPageStatus(pageId, isActive),
    onSuccess: (_, variables) => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
      // Invalidate specific page detail
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGE_DETAIL, variables.pageId],
      });
    },
  });
};

/**
 * Hook để đồng bộ Facebook Pages
 * @returns Mutation function
 */
export const useSyncFacebookPages = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: syncFacebookPages,
    onSuccess: () => {
      // Invalidate Facebook pages queries
      queryClient.invalidateQueries({
        queryKey: [FACEBOOK_QUERY_KEYS.FACEBOOK_PAGES],
      });
    },
  });
};
