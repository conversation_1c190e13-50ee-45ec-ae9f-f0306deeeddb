import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import { useQuery, useMutation, useQueryClient, UseQueryOptions } from '@tanstack/react-query';

import {
  getBankAccounts,
  getBankAccountDetail,
  createBankAccount,
  updateBankAccount,
  deleteBankAccount,
  deleteManyBankAccounts,
  sendOTP,
  verifyOTP,
  createVirtualAccount,
  getBankAccountStats,
  activateBankAccount,
  deactivateBankAccount,
  checkBankAccountConnection,
  syncBankAccount,
} from '../services/bank-account.api';
import {
  BankAccountDto,
  BankAccountQueryDto,
  CreateBankAccountDto,
  UpdateBankAccountDto,
  OTPVerificationDto,
  CreateVirtualAccountDto,
  BankAccountStatsDto,
} from '../types/bank-account.types';

/**
 * Bank Account Hooks
 * React Query hooks cho Bank Account integration
 */

// Query Keys
export const BANK_ACCOUNT_QUERY_KEYS = {
  BANK_ACCOUNTS: 'bank-accounts',
  BANK_ACCOUNT_DETAIL: 'bank-account-detail',
  BANK_ACCOUNT_STATS: 'bank-account-stats',
  BANK_ACCOUNT_CONNECTION: 'bank-account-connection',
} as const;

/**
 * Hook để lấy danh sách tài khoản ngân hàng
 */
export const useBankAccounts = (
  params: BankAccountQueryDto = {},
  options?: UseQueryOptions<ApiResponseDto<PaginatedResult<BankAccountDto>>>
) => {
  return useQuery({
    queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS, params],
    queryFn: () => getBankAccounts(params),
    ...options,
  });
};

/**
 * Hook để lấy chi tiết tài khoản ngân hàng
 */
export const useBankAccountDetail = (
  id: string,
  options?: UseQueryOptions<ApiResponseDto<BankAccountDto>>
) => {
  return useQuery({
    queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_DETAIL, id],
    queryFn: () => getBankAccountDetail(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook để tạo tài khoản ngân hàng mới
 */
export const useCreateBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateBankAccountDto) => createBankAccount(data),
    onSuccess: () => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để cập nhật tài khoản ngân hàng
 */
export const useUpdateBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateBankAccountDto }) =>
      updateBankAccount(id, data),
    onSuccess: (_, variables) => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      // Invalidate specific bank account detail
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_DETAIL, variables.id],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để xóa tài khoản ngân hàng
 */
export const useDeleteBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteBankAccount(id),
    onSuccess: () => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để xóa nhiều tài khoản ngân hàng
 */
export const useDeleteManyBankAccounts = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: string[]) => deleteManyBankAccounts(ids),
    onSuccess: () => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để gửi OTP
 */
export const useSendOTP = () => {
  return useMutation({
    mutationFn: (bankAccountId: string) => sendOTP(bankAccountId),
  });
};

/**
 * Hook để xác thực OTP
 */
export const useVerifyOTP = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: OTPVerificationDto) => verifyOTP(data),
    onSuccess: (_, variables) => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      // Invalidate specific bank account detail
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_DETAIL, variables.bankAccountId],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để tạo tài khoản ảo
 */
export const useCreateVirtualAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateVirtualAccountDto) => createVirtualAccount(data),
    onSuccess: (_, variables) => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      // Invalidate specific bank account detail
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_DETAIL, variables.bankAccountId],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để lấy thống kê tài khoản ngân hàng
 */
export const useBankAccountStats = (
  options?: UseQueryOptions<ApiResponseDto<BankAccountStatsDto>>
) => {
  return useQuery({
    queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
    queryFn: () => getBankAccountStats(),
    ...options,
  });
};

/**
 * Hook để kích hoạt tài khoản ngân hàng
 */
export const useActivateBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => activateBankAccount(id),
    onSuccess: (_, id) => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      // Invalidate specific bank account detail
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_DETAIL, id],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để vô hiệu hóa tài khoản ngân hàng
 */
export const useDeactivateBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deactivateBankAccount(id),
    onSuccess: (_, id) => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      // Invalidate specific bank account detail
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_DETAIL, id],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_STATS],
      });
    },
  });
};

/**
 * Hook để kiểm tra kết nối tài khoản ngân hàng
 */
export const useBankAccountConnection = (
  id: string,
  options?: UseQueryOptions<ApiResponseDto<{ isConnected: boolean; lastChecked: string }>>
) => {
  return useQuery({
    queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_CONNECTION, id],
    queryFn: () => checkBankAccountConnection(id),
    enabled: !!id,
    ...options,
  });
};

/**
 * Hook để đồng bộ tài khoản ngân hàng
 */
export const useSyncBankAccount = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => syncBankAccount(id),
    onSuccess: (_, id) => {
      // Invalidate bank accounts queries
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNTS],
      });
      // Invalidate specific bank account detail
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_DETAIL, id],
      });
      queryClient.invalidateQueries({
        queryKey: [BANK_ACCOUNT_QUERY_KEYS.BANK_ACCOUNT_CONNECTION, id],
      });
    },
  });
};
