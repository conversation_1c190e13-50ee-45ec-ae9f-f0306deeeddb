import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import {
  DocumentResponseDto,
  DocumentWithDownloadUrlResponseDto,
  DocumentQueryDto,
  DocumentSearchDto,
  CreateDocumentDto,
  UpdateDocumentDto,
  UploadDocumentDto,
  UploadDocumentResponseDto,
  CreateUploadUrlDto,
  UploadUrlResponseDto,
  ConfirmUploadDto,
} from '../types/document.types';

/**
 * Document API Layer
 * Raw API calls without business logic
 */

/**
 * L<PERSON>y danh sách tài liệu
 */
export const getDocuments = async (
  params?: DocumentQueryDto
): Promise<ApiResponseDto<PaginatedResult<DocumentResponseDto>>> => {
  return apiClient.get('/v1/api/documents', { params });
};

/**
 * Tìm kiếm full-text tài liệu
 */
export const searchDocuments = async (
  params: DocumentSearchDto
): Promise<ApiResponseDto<PaginatedResult<DocumentResponseDto>>> => {
  return apiClient.get('/v1/api/documents/search', { params });
};

/**
 * Lấy chi tiết tài liệu
 */
export const getDocumentById = async (
  id: number
): Promise<ApiResponseDto<DocumentResponseDto | null>> => {
  return apiClient.get(`/v1/api/documents/${id}`);
};

/**
 * Lấy URL download cho tài liệu
 */
export const getDocumentDownloadUrl = async (
  id: number
): Promise<ApiResponseDto<DocumentWithDownloadUrlResponseDto>> => {
  return apiClient.get(`/v1/api/documents/${id}/download-url`);
};

/**
 * Tạo tài liệu mới
 */
export const createDocument = async (
  data: CreateDocumentDto
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.post('/v1/api/documents', data);
};

/**
 * Cập nhật tài liệu
 */
export const updateDocument = async (
  id: number,
  data: UpdateDocumentDto
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.put(`/v1/api/documents/${id}`, data);
};

/**
 * Xóa tài liệu
 */
export const deleteDocument = async (
  id: number
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete(`/v1/api/documents/${id}`);
};

/**
 * Xóa nhiều tài liệu
 */
export const bulkDeleteDocuments = async (
  ids: number[]
): Promise<ApiResponseDto<void>> => {
  return apiClient.delete('/v1/api/documents/bulk-delete', {
    data: { ids },
  });
};

/**
 * Upload tài liệu trực tiếp
 */
export const uploadDocument = async (
  data: UploadDocumentDto,
  file: File
): Promise<ApiResponseDto<UploadDocumentResponseDto>> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('data', JSON.stringify(data));

  return apiClient.post('/v1/api/documents/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * Tạo URL upload presigned
 */
export const createUploadUrl = async (
  data: CreateUploadUrlDto
): Promise<ApiResponseDto<UploadUrlResponseDto>> => {
  return apiClient.post('/v1/api/documents/upload-url', data);
};

/**
 * Xác nhận upload thành công
 */
export const confirmUpload = async (
  data: ConfirmUploadDto
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.post('/v1/api/documents/confirm-upload', data);
};

/**
 * Thử lại xử lý tài liệu
 */
export const retryProcessing = async (
  id: number
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.post(`/v1/api/documents/${id}/retry-processing`);
};

/**
 * Hủy xử lý tài liệu
 */
export const cancelProcessing = async (
  id: number
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.post(`/v1/api/documents/${id}/cancel-processing`);
};

/**
 * Tạo bản sao tài liệu
 */
export const duplicateDocument = async (
  id: number,
  title?: string
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.post(`/v1/api/documents/${id}/duplicate`, { title });
};

/**
 * Chuyển tài liệu sang thư mục khác
 */
export const moveDocument = async (
  id: number,
  folderId: number | null
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.put(`/v1/api/documents/${id}/move`, { folderId });
};

/**
 * Cập nhật tags cho tài liệu
 */
export const updateDocumentTags = async (
  id: number,
  tags: string[]
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.put(`/v1/api/documents/${id}/tags`, { tags });
};

/**
 * Cập nhật metadata cho tài liệu
 */
export const updateDocumentMetadata = async (
  id: number,
  metadata: Record<string, any>
): Promise<ApiResponseDto<DocumentResponseDto>> => {
  return apiClient.put(`/v1/api/documents/${id}/metadata`, { metadata });
};

/**
 * Lấy thống kê tài liệu
 */
export const getDocumentStats = async (): Promise<ApiResponseDto<{
  totalDocuments: number;
  totalSize: number;
  documentsByType: Record<string, number>;
  documentsByStatus: Record<string, number>;
  recentDocuments: DocumentResponseDto[];
}>> => {
  return apiClient.get('/v1/api/documents/stats');
};
