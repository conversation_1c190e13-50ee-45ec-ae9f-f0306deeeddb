/**
 * File Message Component
 * Renders file attachments with download and preview functionality
 */

import React, { useState } from 'react';

import { Typography, Button, Icon } from '@/shared/components/common';
import { FileContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface FileMessageProps {
  data: FileContentData;
  className?: string;
}

/**
 * File Message Component
 */
const FileMessage: React.FC<FileMessageProps> = ({ data, className = '' }) => {
  const {
    url,
    name,
    size,
    type,
    downloadUrl,
    previewUrl,
  } = data;

  const [isDownloading, setIsDownloading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) {return '0 Bytes';}
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))  } ${  sizes[i]}`;
  };

  // Get file icon based on type
  const getFileIcon = () => {
    const fileType = type.toLowerCase();
    
    if (fileType.includes('image')) {return 'image';}
    if (fileType.includes('video')) {return 'video';}
    if (fileType.includes('audio')) {return 'music';}
    if (fileType.includes('pdf')) {return 'file-text';}
    if (fileType.includes('word') || fileType.includes('document')) {return 'file-text';}
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) {return 'file-text';}
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) {return 'file-text';}
    if (fileType.includes('zip') || fileType.includes('rar') || fileType.includes('archive')) {return 'archive';}
    if (fileType.includes('text') || fileType.includes('plain')) {return 'file-text';}
    if (fileType.includes('json') || fileType.includes('xml')) {return 'code';}
    if (fileType.includes('javascript') || fileType.includes('typescript') || fileType.includes('css') || fileType.includes('html')) {return 'code';}
    
    return 'file';
  };

  // Get file color based on type
  const getFileColor = () => {
    const fileType = type.toLowerCase();
    
    if (fileType.includes('image')) {return 'text-green-500';}
    if (fileType.includes('video')) {return 'text-purple-500';}
    if (fileType.includes('audio')) {return 'text-blue-500';}
    if (fileType.includes('pdf')) {return 'text-red-500';}
    if (fileType.includes('word') || fileType.includes('document')) {return 'text-blue-600';}
    if (fileType.includes('excel') || fileType.includes('spreadsheet')) {return 'text-green-600';}
    if (fileType.includes('powerpoint') || fileType.includes('presentation')) {return 'text-orange-500';}
    if (fileType.includes('zip') || fileType.includes('rar') || fileType.includes('archive')) {return 'text-yellow-600';}
    if (fileType.includes('code') || fileType.includes('javascript') || fileType.includes('typescript')) {return 'text-indigo-500';}
    
    return 'text-gray-500';
  };

  // Check if file can be previewed
  const canPreview = () => {
    const fileType = type.toLowerCase();
    return (
      previewUrl ||
      fileType.includes('image') ||
      fileType.includes('text') ||
      fileType.includes('pdf') ||
      fileType.includes('json') ||
      fileType.includes('xml')
    );
  };

  // Handle download
  const handleDownload = async () => {
    setIsDownloading(true);
    
    try {
      const downloadLink = downloadUrl || url;
      const link = document.createElement('a');
      link.href = downloadLink;
      link.download = name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle preview
  const handlePreview = () => {
    if (previewUrl) {
      window.open(previewUrl, '_blank', 'noopener,noreferrer');
    } else {
      // For images, open in new tab
      if (type.toLowerCase().includes('image')) {
        window.open(url, '_blank', 'noopener,noreferrer');
      } else {
        // For other files, try to open in new tab
        window.open(url, '_blank', 'noopener,noreferrer');
      }
    }
  };

  // Get file extension
  const getFileExtension = () => {
    const parts = name.split('.');
    return parts.length > 1 ? parts.pop()?.toUpperCase() : '';
  };

  return (
    <div className={`p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* File header */}
      <div className="flex items-start space-x-3">
        {/* File icon */}
        <div className={`flex-shrink-0 p-3 rounded-lg bg-gray-100 dark:bg-gray-700 ${getFileColor()}`}>
          <Icon name={getFileIcon()} size="lg" />
        </div>

        {/* File info */}
        <div className="flex-1 min-w-0">
          <Typography variant="body2" className="font-medium text-gray-900 dark:text-gray-100 truncate">
            {name}
          </Typography>
          
          <div className="flex items-center space-x-2 mt-1">
            <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
              {formatFileSize(size)}
            </Typography>
            
            {getFileExtension() && (
              <>
                <span className="text-gray-300 dark:text-gray-600">•</span>
                <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
                  {getFileExtension()}
                </Typography>
              </>
            )}
          </div>

          {/* File type */}
          <Typography variant="caption" className="text-gray-400 dark:text-gray-500 block mt-1">
            {type}
          </Typography>
        </div>
      </div>

      {/* File actions */}
      <div className="flex items-center space-x-2 mt-4">
        {/* Download button */}
        <Button
          variant="primary"
          size="sm"
          onClick={handleDownload}
          loading={isDownloading}
          className="flex-1"
        >
          <Icon name="download" size="sm" className="mr-2" />
          {isDownloading ? 'Downloading...' : 'Download'}
        </Button>

        {/* Preview button */}
        {canPreview() && (
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreview}
            className="flex-1"
          >
            <Icon name="eye" size="sm" className="mr-2" />
            Preview
          </Button>
        )}
      </div>

      {/* File preview for images */}
      {type.toLowerCase().includes('image') && (
        <div className="mt-4">
          <img
            src={previewUrl || url}
            alt={name}
            className="max-w-full h-auto rounded-lg cursor-pointer hover:opacity-90 transition-opacity"
            style={{ maxHeight: '200px' }}
            onClick={handlePreview}
            loading="lazy"
          />
        </div>
      )}

      {/* Additional file info */}
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>Click download to save file</span>
          {canPreview() && <span>Click preview to view content</span>}
        </div>
      </div>
    </div>
  );
};

export default FileMessage;
