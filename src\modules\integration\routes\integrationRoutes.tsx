import { lazy, Suspense } from 'react';
import { RouteObject } from 'react-router-dom';

import i18n from '@/lib/i18n';
import { Loading } from '@/shared/components/common';
import MainLayout from '@/shared/layouts/MainLayout';

import UserIntegrationManagementPage from '../pages/UserIntegrationManagementPage';

const FacebookIntegrationPage = lazy(() => import('../pages/FacebookIntegrationPage'));
const EmailServerManagementPage = lazy(() => import('../pages/EmailServerManagementPage'));

const t = (key: string, defaultValue?: string) => i18n.t(key, { defaultValue });

const integrationRoutes: RouteObject[] = [
  // Trang chính - Hiển thị tất cả các mạng xã hội
  {
    path: '/integrations',
    element: (
      <MainLayout title={t('integration.social.title')}>
        <Suspense fallback={<Loading />}>
          <UserIntegrationManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },

  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/email',
    element: (
      <MainLayout title={t('integration.accounts.title')}>
        <Suspense fallback={<Loading />}>
          <EmailServerManagementPage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Trang quản lý tài khoản đã liên kết
  {
    path: '/integrations/facebook',
    element: (
      <MainLayout title={t('integration.facebook.title')}>
        <Suspense fallback={<Loading />}>
          <FacebookIntegrationPage />
        </Suspense>
      </MainLayout>
    ),
  }
];

export default integrationRoutes;
