# C<PERSON>i Thiện Trang Chi Tiết Task (/todolist/tasks/54)

## Tổng Quan Thay Đổi

Đã cải thiện trang chi tiết task theo yêu cầu của người dùng với các thay đổi chính sau:

### 1. Thay Thế Menu Bằng Select Components

**Trước đây:**
- <PERSON><PERSON> dụng Menu + IconCard cho Status và Priority
- Người dùng phải click vào icon để mở menu dropdown

**Bây giờ:**
- Sử dụng Select component trực tiếp cho Status và Priority
- <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> tr<PERSON><PERSON>uan <PERSON>, dễ sử dụng hơn

### 2. Sử dụng AsyncSelectWithPagination Cho Assignee

**Trước đây:**
- Sử dụng modal popup với AsyncSelectWithPagination

**Bây giờ:**
- <PERSON><PERSON> dụng AsyncSelectWithPagination trực tiếp trong form
- <PERSON><PERSON><PERSON> di<PERSON><PERSON> gọ<PERSON> g<PERSON>, kh<PERSON>ng cần modal

### 3. <PERSON><PERSON><PERSON>hiện StarRating Component

**Thay đổi trong StarRating:**
- Thêm size 'xl' cho stars lớn hơn
- Cải thiện kích thước: sm, md (tăng lên), lg, xl
- Sử dụng button thay vì span để tránh vấn đề hiển thị
- Thêm focus states và accessibility
- Loại bỏ vấn đề button hiển thị bên dưới stars

### 4. Tạo RatingSelector Component Dùng Chung

**Component mới:** `src/shared/components/common/RatingSelector/RatingSelector.tsx`

**Tính năng:**
- Kết hợp StarRating với label và value display
- Có thể tái sử dụng trong toàn bộ ứng dụng
- Hỗ trợ interactive và read-only modes
- Hiển thị giá trị dạng "3/5" hoặc "Chưa đánh giá"

## Chi Tiết Thay Đổi

### Files Đã Thay Đổi:

1. **src/shared/components/common/StarRating/StarRating.tsx**
   - Thêm size 'xl'
   - Cải thiện kích thước các size hiện có
   - Sử dụng button elements thay vì span
   - Thêm accessibility attributes

2. **src/shared/components/common/RatingSelector/RatingSelector.tsx** (Mới)
   - Component dùng chung cho rating selection
   - Kết hợp StarRating với Typography
   - Hỗ trợ label và value display

3. **src/shared/components/common/index.ts**
   - Export RatingSelector component

4. **src/modules/todolist/components/task-detail/TaskDetailSidebar.tsx**
   - Thay thế Menu bằng Select cho Status và Priority
   - Sử dụng AsyncSelectWithPagination trực tiếp cho Assignee
   - Sử dụng RatingSelector thay vì StarRating
   - Loại bỏ state management cho menus
   - Cải thiện spacing và styling
   - Tích hợp User API service

5. **src/modules/todolist/components/task-detail/TaskDetailInfo.tsx**
   - Thêm chế độ chỉnh sửa description với Textarea
   - Thêm nút Edit/Save/Cancel
   - Cải thiện UI với card background

6. **Employee Service Integration** (Đã cập nhật)
   - Thay thế User API bằng Employee API
   - Sử dụng EmployeeService từ HRM module
   - Hỗ trợ pagination và filtering cho employees

7. **src/modules/todolist/pages/TaskDetailPage.tsx**
   - Thêm callback cho update description
   - Truyền callback cho TaskDetailInfo

8. **src/shared/components/common/Icon/Icon.tsx**
   - Thêm icons: refresh-cw, check-circle, circle
   - Sửa lỗi hiển thị icons trong header

9. **src/modules/todolist/services/todoService.ts** (Mới)
   - Service cho Todo API calls
   - Hỗ trợ score todo, update description, status, priority, assignee
   - Interface cho các DTO: ScoreTodoDto, UpdateTaskDescriptionDto, etc.

### Cải Thiện UX:

1. **Giao diện trực quan hơn:**
   - Select dropdown thay vì menu popup
   - Không cần click icon để chỉnh sửa

2. **Stars lớn hơn và rõ ràng hơn:**
   - Size 'lg' mặc định cho rating
   - Loại bỏ vấn đề hiển thị button

3. **Responsive và accessible:**
   - Proper focus states
   - Screen reader support
   - Touch-friendly

## Cách Sử Dụng RatingSelector

```tsx
import { RatingSelector } from '@/shared/components/common';

// Basic usage
<RatingSelector
  value={rating}
  onChange={setRating}
  label="Your Rating"
  size="lg"
/>

// Read-only
<RatingSelector
  value={rating}
  onChange={() => {}}
  label="Average Rating"
  interactive={false}
  size="md"
/>
```

## Testing

1. Truy cập `/todolist/tasks/54`
2. Kiểm tra các trường:
   - Status: Dropdown select hoạt động
   - Priority: Dropdown select hoạt động  
   - Assignee: AsyncSelect với search hoạt động
   - Expected Stars: Rating lớn, có thể click để chọn
   - Awarded Stars: Rating read-only (nếu có)

## Cập Nhật Mới

### 5. Cải Thiện TaskDetailInfo với Textarea
- **Thay đổi**: Chuyển từ hiển thị text thành Textarea có thể chỉnh sửa
- **Tính năng**:
  - Có nút Edit để chuyển sang chế độ chỉnh sửa
  - Sử dụng Textarea component với autoSize
  - Có nút Save/Cancel
  - Hiển thị trong card với background

### 6. Tích Hợp Employee API cho Assignee (Đã cập nhật)
- **API**: `/api/hrm/employees`
- **Service**: `EmployeeService.getEmployees()`
- **Tính năng**:
  - Search employees theo tên
  - Pagination support
  - Hiển thị thông tin: employeeName, employeeCode, department, jobTitle
  - Thay thế hoàn toàn User API bằng Employee API

### 7. Thêm Icons Thiếu
- **Icons mới**: `refresh-cw`, `check-circle`, `circle`
- **Vị trí**: TaskDetailHeader sử dụng các icons này
- **Mục đích**: Hiển thị đúng icons trong header (Back, Refresh, Mark Complete)

### 8. Tích Hợp Score Todo API
- **API**: `PATCH /api/todos/{id}/score`
- **Service**: `TodoService.scoreTodo()`
- **Tính năng**:
  - Chấm điểm sao (1-5) cho task
  - Gửi note kèm theo khi chấm điểm
  - Hiển thị notification thành công/lỗi
  - Tự động refresh data sau khi chấm điểm
- **UI**: Khi click vào sao trong "Số sao kỳ vọng" sẽ gọi API chấm điểm

## Bug Fixes

### Import Path Fix
- **Vấn đề**: `EditKeyResultForm.tsx` có import path sai: `@/shared/utils/notification.util`
- **Sửa**: Đổi thành `@/shared/utils/notification` (bỏ `.util`)
- **File**: `src/modules/okrs/components/EditKeyResultForm.tsx`

## Lưu Ý

- Component RatingSelector có thể được sử dụng ở các nơi khác trong ứng dụng
- StarRating component đã được cải thiện và tương thích ngược
- Tất cả thay đổi tuân thủ design system và coding standards của dự án
- Đã sửa lỗi import path trong EditKeyResultForm.tsx
