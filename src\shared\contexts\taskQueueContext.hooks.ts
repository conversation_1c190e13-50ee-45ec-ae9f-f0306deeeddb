/**
 * Hook cho Task Queue
 */
import { useContext } from 'react';

import { TaskQueueContext } from './taskQueueContext.context';
import { TaskQueueContextValue } from './taskQueueContext.types';

/**
 * Hook để sử dụng Task Queue
 */
export const useTaskQueueContext = (): TaskQueueContextValue => {
  const context = useContext(TaskQueueContext);

  if (!context) {
    throw new Error('useTaskQueueContext phải được sử dụng trong TaskQueueProvider');
  }

  return context;
};
