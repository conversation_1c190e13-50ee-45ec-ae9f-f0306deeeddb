/**
 * Component gốc cho Task Queue
 */
import React, { useEffect } from 'react';

import { TaskQueueProvider } from '@/shared/contexts/TaskQueueContext';
import { useTaskQueueContext } from '@/shared/contexts/taskQueueContext.hooks';
import createTaskQueueMiddleware from '@/shared/middleware/taskQueueMiddleware';
import { useQueryClient } from '@tanstack/react-query';

import TaskQueuePanel from './TaskQueuePanel';

/**
 * Props cho TaskQueueRoot
 */
export interface TaskQueueRootProps {
  /**
   * Có hiển thị panel không
   * @default true
   */
  showPanel?: boolean;

  /**
   * Chiều rộng của panel
   * @default 360
   */
  panelWidth?: number;

  /**
   * Chiều cao tối đa của panel
   * @default 480
   */
  panelMaxHeight?: number;

  /**
   * Có tự động thêm task cho tất cả các mutation không
   * @default false
   */
  autoAddAllMutations?: boolean;

  /**
   * Danh sách mutation key cần theo dõi
   */
  mutationKeys?: string[];

  /**
   * <PERSON><PERSON> lượng task tối đa có thể chạy đồng thời
   * @default 3
   */
  concurrency?: number;

  /**
   * Số lần thử lại mặc định cho các task
   * @default 3
   */
  defaultMaxRetries?: number;

  /**
   * Thời gian tự động xóa task đã hoàn thành (ms)
   * @default 60000 (1 phút)
   */
  autoRemoveCompletedAfter?: number;

  /**
   * Cho phép siêu thu gọn panel
   * @default true
   */
  allowSuperCollapse?: boolean;

  /**
   * Children
   */
  children?: React.ReactNode;
}

/**
 * Component nội bộ để tích hợp Task Queue với TanStack Query
 */
const TaskQueueIntegration: React.FC<{
  autoAddAllMutations?: boolean;
  mutationKeys?: string[];
  children?: React.ReactNode;
}> = ({ autoAddAllMutations = false, mutationKeys = [], children }) => {
  const queryClient = useQueryClient();
  const taskQueue = useTaskQueueContext();

  // Tạo middleware khi component mount
  useEffect(() => {
    const middleware = createTaskQueueMiddleware({
      queryClient,
      taskQueue,
      autoAddAllMutations,
      mutationKeys,
    });

    // Cleanup khi component unmount
    return () => {
      middleware.unsubscribe();
    };
  }, [queryClient, taskQueue, autoAddAllMutations, mutationKeys]);

  return <>{children}</>;
};

/**
 * Component gốc cho Task Queue
 */
const TaskQueueRoot: React.FC<TaskQueueRootProps> = ({
  showPanel = true,
  panelWidth = 360,
  panelMaxHeight = 480,
  autoAddAllMutations = false,
  mutationKeys = [],
  concurrency = 3,
  defaultMaxRetries = 3,
  autoRemoveCompletedAfter = 60000,
  allowSuperCollapse = true,
  children,
}) => {
  return (
    <TaskQueueProvider
      options={{
        concurrency,
        defaultMaxRetries,
        autoRemoveCompletedAfter,
      }}
    >
      <TaskQueueIntegration autoAddAllMutations={autoAddAllMutations} mutationKeys={mutationKeys}>
        {children}

        {showPanel && (
          <TaskQueuePanel
            width={panelWidth}
            maxHeight={panelMaxHeight}
            allowSuperCollapse={allowSuperCollapse}
          />
        )}
      </TaskQueueIntegration>
    </TaskQueueProvider>
  );
};

export default TaskQueueRoot;
