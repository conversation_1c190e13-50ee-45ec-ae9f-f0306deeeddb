/**
 * Enum trạng thái công việc
 */
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  APPROVED = 'approved',
  REJECTED = 'rejected',
}

/**
 * Enum mức độ ưu tiên
 */
export enum TaskPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

/**
 * Interface cho Tag
 */
export interface TagDto {
  id: number;
  name: string;
  tenantId: string;
}

/**
 * Interface cho Task
 */
export interface TaskDto {
  id: number;
  title: string;
  description: string | null;
  assigneeId: number;
  status: TaskStatus | null;
  priority: TaskPriority | null;
  expectedStars: number | null;
  awardedStars: number | null;
  createdBy: number;
  createdAt: number | null;
  updatedAt: number | null;
  completedAt: number | null;
  categoryId: number | null;
  parentId: number | null;
  startDate: number | null;
  deadline: number | null;
  tags?: TagDto[];
}

/**
 * Interface cho TaskQueryDto
 */
export interface TaskQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
  status?: TaskStatus;
  priority?: TaskPriority;
  assigneeId?: number;
  categoryId?: number;
  parentId?: number | null;
  startDate?: number;
  endDate?: number;
  tagIds?: string; // Backend mong đợi chuỗi phân tách bằng dấu phẩy "1,2,3"
}

/**
 * Interface cho CreateTaskDto
 */
export interface CreateTaskDto {
  title: string;
  description?: string;
  assigneeId?: number;
  priority?: TaskPriority;
  expectedStars?: number;
  categoryId?: number;
  parentId?: number;
  startDate?: number;
  deadline?: number;
  keyResultIds?: number[];
}

/**
 * Interface cho UpdateTaskDto
 */
export interface UpdateTaskDto {
  title?: string;
  description?: string;
  assigneeId?: number;
  priority?: TaskPriority;
  expectedStars?: number;
  categoryId?: number;
  parentId?: number;
  startDate?: number;
  deadline?: number;
}

/**
 * Interface cho UpdateTaskStatusDto
 */
export interface UpdateTaskStatusDto {
  status: TaskStatus;
}

/**
 * Interface cho tham số truy vấn danh sách tag
 */
export interface TagQueryDto {
  page?: number;
  limit?: number;
  search?: string;
  sortBy?: string;
  sortDirection?: 'ASC' | 'DESC';
}

/**
 * Interface cho tạo tag mới
 */
export interface CreateTagDto {
  name: string;
}

/**
 * Interface cho cập nhật tag
 */
export interface UpdateTagDto {
  name?: string;
}

/**
 * Interface cho xóa nhiều tag
 */
export interface BulkDeleteTagDto {
  ids: number[];
}

/**
 * Interface cho phản hồi xóa nhiều tag
 */
export interface BulkDeleteTagResponseDto {
  deletedCount: number;
  failedIds: number[];
}

/**
 * Interface cho xóa nhiều task
 */
export interface BulkDeleteTaskDto {
  ids: number[];
}

/**
 * Interface cho phản hồi xóa nhiều task
 */
export interface BulkDeleteTaskResponseDto {
  totalRequested: number;
  successCount: number;
  failureCount: number;
  deletedIds: number[];
  failures: Array<{
    id: number;
    reason: string;
  }>;
}
