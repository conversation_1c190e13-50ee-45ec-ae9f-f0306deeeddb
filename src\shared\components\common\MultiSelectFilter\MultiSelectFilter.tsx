import React, { useState, useRef, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { createPortal } from 'react-dom';

import { 
  Chip, 
  Icon, 
  Input, 
  ScrollArea, 
  Checkbox, 
  Button, 
  Typography 
} from '@/shared/components/common';

export interface FilterOption {
  id: string;
  label: string;
  value: string;
  category: string;
  icon?: string;
}

export interface FilterCategory {
  id: string;
  label: string;
  options: FilterOption[];
}

export interface SelectedFilter {
  id: string;
  label: string;
  value: string;
  category: string;
}

interface MultiSelectFilterProps {
  isOpen: boolean;
  onClose: () => void;
  selectedFilters: SelectedFilter[];
  onFiltersChange: (filters: SelectedFilter[]) => void;
  triggerRef?: React.RefObject<HTMLElement>;
  filterCategories: FilterCategory[];
}

/**
 * Component MultiSelectFilter cho phép chọn nhiều bộ lọc cùng lúc
 */
const MultiSelectFilter: React.FC<MultiSelectFilterProps> = ({
  isOpen,
  onClose,
  selectedFilters,
  onFiltersChange,
  triggerRef,
  filterCategories,
}) => {
  const { t } = useTranslation(['common', 'todolist']);
  const [searchTerm, setSearchTerm] = useState('');
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const menuRef = useRef<HTMLDivElement>(null);

  // Lọc các tùy chọn dựa trên từ khóa tìm kiếm
  const filteredCategories = useMemo(() => {
    if (!searchTerm.trim()) {
      return filterCategories;
    }

    return filterCategories
      .map(category => ({
        ...category,
        options: category.options.filter(option =>
          option.label.toLowerCase().includes(searchTerm.toLowerCase())
        ),
      }))
      .filter(category => category.options.length > 0);
  }, [filterCategories, searchTerm]);

  // Kiểm tra xem một tùy chọn có được chọn không
  const isOptionSelected = (option: FilterOption) => {
    return selectedFilters.some(filter => filter.id === option.id);
  };

  // Xử lý chọn/bỏ chọn tùy chọn
  const handleOptionToggle = (option: FilterOption) => {
    const isSelected = isOptionSelected(option);
    
    if (isSelected) {
      // Bỏ chọn
      const newFilters = selectedFilters.filter(filter => filter.id !== option.id);
      onFiltersChange(newFilters);
    } else {
      // Chọn
      const newFilter: SelectedFilter = {
        id: option.id,
        label: option.label,
        value: option.value,
        category: option.category,
      };
      onFiltersChange([...selectedFilters, newFilter]);
    }
  };

  // Xử lý chọn tất cả trong một danh mục
  const handleSelectAllCategory = (category: FilterCategory) => {
    const categoryOptions = category.options.filter(option => !isOptionSelected(option));
    const newFilters = categoryOptions.map(option => ({
      id: option.id,
      label: option.label,
      value: option.value,
      category: option.category,
    }));
    onFiltersChange([...selectedFilters, ...newFilters]);
  };

  // Xử lý bỏ chọn tất cả trong một danh mục
  const handleDeselectAllCategory = (category: FilterCategory) => {
    const newFilters = selectedFilters.filter(
      filter => filter.category !== category.id
    );
    onFiltersChange(newFilters);
  };

  // Xử lý xóa tất cả bộ lọc
  const handleClearAll = () => {
    onFiltersChange([]);
  };

  // Tính toán vị trí menu
  const getMenuPosition = () => {
    // Tìm MenuIconBar element để định vị
    const menuIconBar = document.querySelector('[class*="flex"][class*="items-center"][class*="space-x-2"]');
    if (!menuIconBar) {
      return { top: 100, left: 100 };
    }

    const menuIconBarRect = menuIconBar.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    const menuWidth = 320;
    const menuHeight = 400;
    const margin = 8;

    // Đặt menu bên dưới MenuIconBar, căn trái
    let top = menuIconBarRect.bottom + margin;
    let left = menuIconBarRect.left;

    // Điều chỉnh nếu menu bị tràn viewport
    if (left + menuWidth > viewportWidth - margin) {
      left = viewportWidth - menuWidth - margin;
    }
    if (left < margin) {
      left = margin;
    }

    if (top + menuHeight > viewportHeight - margin) {
      top = menuIconBarRect.top - menuHeight - margin;
    }
    if (top < margin) {
      top = margin;
    }

    return { top, left };
  };

  // Cập nhật vị trí menu khi mở
  useEffect(() => {
    if (isOpen) {
      const position = getMenuPosition();
      setMenuPosition(position);
    }
  }, [isOpen, triggerRef]);

  if (!isOpen) {
    return null;
  }

  const handleClickOutside = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClose();
  };

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
  };

  const menuContent = (
    <>
      <div className="fixed inset-0 z-[99999]" onClick={handleClickOutside} />
      <div
        ref={menuRef}
        className="fixed bg-card rounded-lg shadow-xl z-[100000] overflow-hidden animate-fade-in"
        style={{
          width: '320px',
          top: `${menuPosition.top}px`,
          left: `${menuPosition.left}px`,
          maxHeight: '400px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        }}
        onClick={handleMenuClick}
      >
      <div className="p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <Typography variant="subtitle2" weight="medium">
            {t('todolist:task.filters.title', 'Bộ lọc')}
          </Typography>
          {selectedFilters.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClearAll}
              className="text-destructive hover:text-destructive/80"
            >
              {t('todolist:task.filters.actions.clear', 'Xóa tất cả')}
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="mb-3">
          <Input
            type="text"
            placeholder={t('todolist:task.filters.placeholder', 'Tìm kiếm bộ lọc...')}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="text-sm w-full"
            leftIcon={<Icon name="search" size="sm" />}
          />
        </div>

        {/* Selected Filters */}
        {selectedFilters.length > 0 && (
          <div className="mb-3">
            <Typography variant="caption" color="muted" className="mb-2">
              {t('common:selected', 'Đã chọn')} ({selectedFilters.length})
            </Typography>
            <div className="flex flex-wrap gap-1">
              {selectedFilters.map(filter => (
                <Chip
                  key={filter.id}
                  size="sm"
                  variant="primary"
                  closable
                  onClose={() => handleOptionToggle({
                    id: filter.id,
                    label: filter.label,
                    value: filter.value,
                    category: filter.category,
                  })}
                >
                  {filter.label}
                </Chip>
              ))}
            </div>
          </div>
        )}

        {/* Filter Categories */}
        <ScrollArea className="max-h-64">
          <div className="space-y-3">
            {filteredCategories.map(category => {
              const selectedInCategory = selectedFilters.filter(
                filter => filter.category === category.id
              ).length;
              const totalInCategory = category.options.length;
              const allSelected = selectedInCategory === totalInCategory;
              const someSelected = selectedInCategory > 0;

              return (
                <div key={category.id} className="space-y-2">
                  {/* Category Header */}
                  <div className="flex items-center justify-between">
                    <Typography variant="caption" weight="medium">
                      {category.label}
                      {someSelected && (
                        <Typography variant="caption" color="muted" className="ml-1">
                          ({selectedInCategory}/{totalInCategory})
                        </Typography>
                      )}
                    </Typography>
                    <div className="flex gap-1">
                      {!allSelected && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleSelectAllCategory(category)}
                          className="text-primary hover:text-primary/80 text-xs px-2 py-1"
                        >
                          {t('todolist:task.filters.actions.selectAll', 'Chọn tất cả')}
                        </Button>
                      )}
                      {someSelected && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeselectAllCategory(category)}
                          className="text-muted-foreground hover:text-foreground text-xs px-2 py-1"
                        >
                          {t('todolist:task.filters.actions.deselectAll', 'Bỏ chọn tất cả')}
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Category Options */}
                  <div className="space-y-1">
                    {category.options.map(option => {
                      const isSelected = isOptionSelected(option);
                      return (
                        <div
                          key={option.id}
                          className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 cursor-pointer"
                          onClick={() => handleOptionToggle(option)}
                        >
                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleOptionToggle(option)}
                            size="sm"
                          />
                          {option.icon && (
                            <Icon name={option.icon as any} size="sm" className="text-muted-foreground" />
                          )}
                          <Typography variant="body2">
                            {option.label}
                          </Typography>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>

        {filteredCategories.length === 0 && (
          <div className="text-center py-4">
            <Typography variant="body2" color="muted">
              {t('common:noResults', 'Không có kết quả')}
            </Typography>
          </div>
        )}
      </div>
      </div>
    </>
  );

  // Sử dụng portal để render menu ở body level
  return createPortal(menuContent, document.body);
};

export default MultiSelectFilter;
