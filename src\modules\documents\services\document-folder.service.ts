import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

import * as DocumentFolderAPI from '../api/document-folder.api';
import {
  DocumentFolderResponseDto,
  DocumentFolderQueryDto,
  CreateDocumentFolderDto,
  UpdateDocumentFolderDto,
} from '../types/document.types';

/**
 * Document Folder Services Layer
 * Business logic for document folder operations
 */

/**
 * T<PERSON><PERSON> thư mục mới với logic nghiệp vụ
 */
export const createDocumentFolderWithBusinessLogic = async (
  data: CreateDocumentFolderDto
): Promise<DocumentFolderResponseDto> => {
  // Validate required fields
  if (!data.name || data.name.trim().length === 0) {
    throw new Error('Folder name is required');
  }

  // Validate name length
  if (data.name.trim().length > 255) {
    throw new Error('Folder name cannot exceed 255 characters');
  }

  // Clean up data
  const createData: CreateDocumentFolderDto = {
    ...data,
    name: data.name.trim(),
    description: data.description?.trim() || null,
  };

  const response = await DocumentFolderAPI.createDocumentFolder(createData);
  return response.result;
};

/**
 * Lấy danh sách thư mục với logic nghiệp vụ
 */
export const getDocumentFoldersWithBusinessLogic = async (
  params?: DocumentFolderQueryDto
): Promise<PaginatedResult<DocumentFolderResponseDto>> => {
  // Thiết lập giá trị mặc định
  const defaultParams: DocumentFolderQueryDto = {
    page: 1,
    limit: 20,
    sortBy: 'name',
    sortDirection: 'ASC',
    isActive: true,
    ...params,
  };

  // Validate limit
  if (defaultParams.limit && defaultParams.limit > 100) {
    throw new Error('Limit cannot exceed 100');
  }

  // Validate page
  if (defaultParams.page && defaultParams.page < 1) {
    defaultParams.page = 1;
  }

  const response = await DocumentFolderAPI.getDocumentFolders(defaultParams);
  return response.result;
};

/**
 * Lấy cây thư mục với logic nghiệp vụ
 */
export const getDocumentFolderTreeWithBusinessLogic = async (): Promise<
  DocumentFolderResponseDto[]
> => {
  const response = await DocumentFolderAPI.getDocumentFolderTree();
  return response.result;
};

/**
 * Lấy chi tiết thư mục với logic nghiệp vụ
 */
export const getDocumentFolderByIdWithBusinessLogic = async (
  id: number
): Promise<DocumentFolderResponseDto | null> => {
  if (!id || id <= 0) {
    throw new Error('Invalid folder ID');
  }

  const response = await DocumentFolderAPI.getDocumentFolderById(id);
  return response.result;
};

/**
 * Cập nhật thư mục với logic nghiệp vụ
 */
export const updateDocumentFolderWithBusinessLogic = async (
  id: number,
  data: UpdateDocumentFolderDto
): Promise<DocumentFolderResponseDto> => {
  if (!id || id <= 0) {
    throw new Error('Invalid folder ID');
  }

  // Validate name if provided
  if (data.name !== undefined) {
    if (data.name.trim().length === 0) {
      throw new Error('Folder name cannot be empty');
    }
    if (data.name.trim().length > 255) {
      throw new Error('Folder name cannot exceed 255 characters');
    }
  }

  // Clean up data
  const updateData: UpdateDocumentFolderDto = { ...data };
  if (updateData.name) {
    updateData.name = updateData.name.trim();
  }
  if (updateData.description) {
    updateData.description = updateData.description.trim();
  }

  const response = await DocumentFolderAPI.updateDocumentFolder(id, updateData);
  return response.result;
};

/**
 * Xóa thư mục với logic nghiệp vụ
 */
export const deleteDocumentFolderWithBusinessLogic = async (id: number): Promise<void> => {
  if (!id || id <= 0) {
    throw new Error('Invalid folder ID');
  }

  await DocumentFolderAPI.deleteDocumentFolder(id);
};

/**
 * Xóa nhiều thư mục với logic nghiệp vụ
 */
export const bulkDeleteDocumentFoldersWithBusinessLogic = async (ids: number[]): Promise<void> => {
  if (!ids || ids.length === 0) {
    throw new Error('No folder IDs provided');
  }

  // Validate all IDs
  const invalidIds = ids.filter(id => !id || id <= 0);
  if (invalidIds.length > 0) {
    throw new Error('Invalid folder IDs found');
  }

  // Limit bulk delete to 20 items
  if (ids.length > 20) {
    throw new Error('Cannot delete more than 20 folders at once');
  }

  await DocumentFolderAPI.bulkDeleteDocumentFolders(ids);
};

/**
 * Di chuyển thư mục với logic nghiệp vụ
 */
export const moveDocumentFolderWithBusinessLogic = async (
  id: number,
  parentId: number | null
): Promise<DocumentFolderResponseDto> => {
  if (!id || id <= 0) {
    throw new Error('Invalid folder ID');
  }

  // Prevent moving folder to itself
  if (parentId === id) {
    throw new Error('Cannot move folder to itself');
  }

  const response = await DocumentFolderAPI.moveDocumentFolder(id, parentId);
  return response.result;
};

/**
 * Sao chép thư mục với logic nghiệp vụ
 */
export const duplicateDocumentFolderWithBusinessLogic = async (
  id: number,
  name?: string,
  parentId?: number | null
): Promise<DocumentFolderResponseDto> => {
  if (!id || id <= 0) {
    throw new Error('Invalid folder ID');
  }

  // Validate name if provided
  if (name && name.trim().length === 0) {
    throw new Error('Folder name cannot be empty');
  }

  if (name && name.trim().length > 255) {
    throw new Error('Folder name cannot exceed 255 characters');
  }

  const response = await DocumentFolderAPI.duplicateDocumentFolder(
    id,
    name?.trim(),
    parentId
  );
  return response.result;
};

/**
 * Lấy đường dẫn thư mục với logic nghiệp vụ
 */
export const getDocumentFolderPathWithBusinessLogic = async (
  id: number
): Promise<DocumentFolderResponseDto[]> => {
  if (!id || id <= 0) {
    throw new Error('Invalid folder ID');
  }

  const response = await DocumentFolderAPI.getDocumentFolderPath(id);
  return response.result;
};

/**
 * Lấy thư mục con với logic nghiệp vụ
 */
export const getDocumentFolderChildrenWithBusinessLogic = async (
  id: number,
  params?: DocumentFolderQueryDto
): Promise<PaginatedResult<DocumentFolderResponseDto>> => {
  if (!id || id <= 0) {
    throw new Error('Invalid folder ID');
  }

  // Thiết lập giá trị mặc định
  const defaultParams: DocumentFolderQueryDto = {
    page: 1,
    limit: 20,
    sortBy: 'name',
    sortDirection: 'ASC',
    isActive: true,
    ...params,
  };

  const response = await DocumentFolderAPI.getDocumentFolderChildren(id, defaultParams);
  return response.result;
};
