import { FileText, Send, Eye, MousePointer, Users, TrendingUp } from 'lucide-react';

import ListOverviewCard from '../ListOverviewCard/ListOverviewCard';

import OverviewCard from './OverviewCard';

import type { OverviewCardProps } from './OverviewCard.types';

/**
 * Demo component cho OverviewCard và ListOverviewCard
 */
export function OverviewCardDemo() {
  // Dữ liệu demo cho single card
  const singleCardData: OverviewCardProps = {
    title: 'Tổng Templates',
    value: 24,
    description: '+3 template mới',
    icon: FileText,
    color: 'blue',
    hoverable: true,
    onClick: () => alert('Card clicked!'),
  };

  // Dữ liệu demo cho list cards
  const listCardData: OverviewCardProps[] = [
    {
      title: 'Tổng Templates',
      value: 24,
      description: '+3 template mới',
      icon: FileText,
      color: 'blue',
    },
    {
      title: '<PERSON>ail đã gửi',
      value: '15,420',
      description: '<PERSON>h<PERSON>g này',
      icon: Send,
      color: 'green',
    },
    {
      title: 'Tỷ lệ mở',
      value: '28.5%',
      description: 'Trung bình 30 ngày',
      icon: Eye,
      color: 'orange',
    },
    {
      title: 'Tỷ lệ click',
      value: '4.2%',
      description: 'Trung bình 30 ngày',
      icon: MousePointer,
      color: 'purple',
    },
    {
      title: 'Người dùng hoạt động',
      value: '1,234',
      description: '+12% so với tháng trước',
      icon: Users,
      color: 'red',
    },
    {
      title: 'Tăng trưởng',
      value: '23.5%',
      description: 'So với quý trước',
      icon: TrendingUp,
      color: 'gray',
    },
  ];

  return (
    <div className="space-y-8 p-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">OverviewCard Demo</h2>
        
        {/* Single Card */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Single Card</h3>
          <div className="max-w-xs">
            <OverviewCard {...singleCardData} />
          </div>
        </div>

        {/* Loading State */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Loading State</h3>
          <div className="max-w-xs">
            <OverviewCard
              title="Loading Card"
              value={0}
              description="Loading description"
              icon={FileText}
              color="blue"
              isLoading={true}
            />
          </div>
        </div>

        {/* Different Colors */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Different Colors</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <OverviewCard title="Blue" value="100" icon={FileText} color="blue" />
            <OverviewCard title="Green" value="200" icon={Send} color="green" />
            <OverviewCard title="Orange" value="300" icon={Eye} color="orange" />
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-2xl font-bold mb-4">ListOverviewCard Demo</h2>
        
        {/* List Cards */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Responsive Grid</h3>
          <ListOverviewCard
            items={listCardData}
            maxColumns={{ xs: 1, sm: 2, md: 3, lg: 4, xl: 6 }}
            gap={4}
          />
        </div>

        {/* Loading State */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Loading State</h3>
          <ListOverviewCard
            items={[]}
            maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
            gap={4}
            isLoading={true}
            skeletonCount={4}
          />
        </div>
      </div>
    </div>
  );
}

export default OverviewCardDemo;
