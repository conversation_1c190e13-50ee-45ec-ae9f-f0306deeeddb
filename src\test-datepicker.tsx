import React, { useState } from 'react';

import { DatePicker, DatePickerFormField } from '@/shared/components/common';

/**
 * Test component để kiểm tra DatePicker sau khi sửa lỗi
 */
const TestDatePicker: React.FC = () => {
  const [basicDate, setBasicDate] = useState<Date | null>(null);
  const [formDate, setFormDate] = useState<string>('');

  return (
    <div className="p-8 space-y-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test DatePicker - Sửa lỗi tụt ngày và input</h1>

      {/* Test Basic DatePicker */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">1. Basic DatePicker (Date object)</h2>
        <DatePicker
          label="Chọn ngày (có thể nhập trực tiếp)"
          value={basicDate}
          onChange={setBasicDate}
          placeholder="dd/MM/yyyy"
          fullWidth
        />
        <div className="text-sm text-gray-600">
          <p>
            <strong>Selected Date:</strong>{' '}
            {basicDate ? basicDate.toLocaleDateString('vi-VN') : 'Chưa chọn'}
          </p>
          <p>
            <strong>ISO String:</strong> {basicDate ? basicDate.toISOString() : 'N/A'}
          </p>
          <p>
            <strong>Local Date String:</strong>{' '}
            {basicDate
              ? `${basicDate.getFullYear()}-${String(basicDate.getMonth() + 1).padStart(2, '0')}-${String(basicDate.getDate()).padStart(2, '0')}`
              : 'N/A'}
          </p>
        </div>
      </div>

      {/* Test DatePickerFormField */}
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">2. DatePickerFormField (String value)</h2>
        <DatePickerFormField
          label="Ngày sinh (Form field)"
          value={formDate}
          onChange={setFormDate}
          placeholder="dd/MM/yyyy"
          fullWidth
        />
        <div className="text-sm text-gray-600">
          <p>
            <strong>Form Value (string):</strong> {formDate || 'Chưa chọn'}
          </p>
          <p>
            <strong>Parsed Date:</strong>{' '}
            {formDate ? new Date(formDate).toLocaleDateString('vi-VN') : 'N/A'}
          </p>
        </div>
      </div>

      {/* Test Instructions */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h3 className="font-semibold text-blue-800 mb-2">Hướng dẫn test:</h3>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>
            1. <strong>Test nhập trực tiếp:</strong> Thử nhập ngày theo format dd/MM/yyyy (ví dụ:
            03/12/2024)
          </li>
          <li>
            2. <strong>Test chọn từ calendar:</strong> Click vào icon calendar và chọn ngày 3
          </li>
          <li>
            3. <strong>Kiểm tra kết quả:</strong> Xem ngày hiển thị có đúng là ngày 3 không (không
            bị tụt thành ngày 2)
          </li>
          <li>
            4. <strong>Test clear:</strong> Click vào icon X để xóa ngày đã chọn
          </li>
        </ul>
      </div>

      {/* Test Cases */}
      <div className="bg-green-50 p-4 rounded-lg">
        <h3 className="font-semibold text-green-800 mb-2">Test Cases:</h3>
        <div className="text-sm text-green-700 space-y-2">
          <div>
            <strong>Test 1:</strong> Chọn ngày 3/12/2024 từ calendar
            <br />
            <em>Kết quả mong đợi:</em> Hiển thị đúng 03/12/2024, không bị tụt thành 02/12/2024
          </div>
          <div>
            <strong>Test 2:</strong> Nhập trực tiếp "03/12/2024"
            <br />
            <em>Kết quả mong đợi:</em> Chấp nhận input và hiển thị đúng ngày
          </div>
          <div>
            <strong>Test 3:</strong> Nhập sai format "3/12/24"
            <br />
            <em>Kết quả mong đợi:</em> Khi blur, format lại thành "03/12/2024" hoặc reset về giá trị
            cũ
          </div>
          <div>
            <strong>Test 4:</strong> Hover vào ngày đã chọn trong calendar
            <br />
            <em>Kết quả mong đợi:</em> Ngày đã chọn vẫn giữ màu highlight, không bị mất màu khi
            hover
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestDatePicker;
