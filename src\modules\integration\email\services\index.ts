import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';

import {
  EmailServerConfiguration,
  CreateEmailServerDto,
  UpdateEmailServerDto,
  TestEmailServerDto,
  TestEmailServerWithConfigDto,
  EmailServerQueryParams,
  EmailServerTestResult,
} from '../types';

// Export provider services
export * from './providers';

/**
 * Email Server Configuration Service
 */
export class EmailServerService {
  private static readonly BASE_URL = '/user/integration/email-server';

  /**
   * L<PERSON>y danh sách cấu hình máy chủ email có phân trang
   */
  static async getEmailServers(
    params?: EmailServerQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<EmailServerConfiguration>>> {
    return apiClient.get(`${this.BASE_URL}/paginated`, { params });
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết cấu hình máy chủ email
   */
  static async getEmailServer(id: number): Promise<ApiResponseDto<EmailServerConfiguration>> {
    return apiClient.get(`${this.BASE_URL}/${id}`);
  }

  /**
   * Tạo mới cấu hình máy chủ email
   */
  static async createEmailServer(
    data: CreateEmailServerDto
  ): Promise<ApiResponseDto<EmailServerConfiguration>> {
    return apiClient.post(this.BASE_URL, data);
  }

  /**
   * Cập nhật cấu hình máy chủ email
   */
  static async updateEmailServer(
    id: number,
    data: UpdateEmailServerDto
  ): Promise<ApiResponseDto<EmailServerConfiguration>> {
    return apiClient.put(`${this.BASE_URL}/${id}`, data);
  }

  /**
   * Xóa cấu hình máy chủ email
   */
  static async deleteEmailServer(id: number): Promise<ApiResponseDto<{ message: string }>> {
    return apiClient.delete(`${this.BASE_URL}/${id}`);
  }

  /**
   * Kiểm tra kết nối máy chủ email
   */
  static async testEmailServerConnection(
    id: number,
    data: TestEmailServerDto
  ): Promise<ApiResponseDto<EmailServerTestResult>> {
    return apiClient.post(`${this.BASE_URL}/${id}/test`, data);
  }

  /**
   * Kiểm tra kết nối máy chủ email với cấu hình trực tiếp
   */
  static async testEmailServerWithConfig(
    data: TestEmailServerWithConfigDto
  ): Promise<ApiResponseDto<{ success: boolean; message: string; details?: Record<string, unknown> }>> {
    return apiClient.post(`${this.BASE_URL}/test-with-config`, data);
  }
}
