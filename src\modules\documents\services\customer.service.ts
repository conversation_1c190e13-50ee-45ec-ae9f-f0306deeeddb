import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

import {
  QueryUserConvertCustomerDto,
  PaginatedUserConvertCustomerResult,
  UserConvertCustomerListItemDto,
} from '../types/customer.types';

/**
 * Interface cho dữ liệu tạo khách hàng chuyển đổi mới
 */
export interface CreateConvertCustomerDto {
  name: string;
  phone: string;
  email: {
    primary: string;
  };
  tags?: string[];
}

/**
 * Interface cho cập nhật thông tin cơ bản khách hàng
 */
export interface UpdateCustomerBasicInfoDto {
  name: string;
  phone: string;
  email: {
    primary: string;
    secondary?: string;
  };
  address?: string;
  avatarFile?: {
    fileName: string;
    mimeType: string;
  };
}

/**
 * Interface cho response khi cập nhật thông tin cơ bản có avatar
 */
export interface UpdateCustomerBasicInfoResponseDto {
  id: string;
  name: string;
  phone: string;
  email: {
    primary: string;
    secondary: string;
  };
  address: string;
  avatar: string;
  updatedAt: string | null;
  avatarUpload?: {
    uploadUrl: string;
    publicUrl: string;
  };
}

/**
 * Interface cho metadata field
 */
export interface MetadataFieldDto {
  configId: string;
  value: unknown;
}

/**
 * Interface cho cập nhật custom fields khách hàng
 */
export interface UpdateCustomerCustomFieldsDto {
  metadata: MetadataFieldDto[];
}

/**
 * Interface cho cập nhật social links khách hàng
 */
export interface UpdateCustomerSocialLinksDto {
  facebookLink?: string;
  twitterLink?: string;
  linkedinLink?: string;
  zaloLink?: string;
  websiteLink?: string;
}

/**
 * Interface cho dữ liệu tạo khách hàng chuyển đổi (bulk)
 */
export interface CreateUserConvertCustomerDto {
  name: string;
  phone: string;
  email?: Record<string, string> | string;
  avatar?: {
    fileName: string;
    mimeType: string;
  };
  platform?: string;
  timezone?: string;
  agentId?: string;
  tags?: string[];
  metadata?: MetadataFieldDto[];
}

/**
 * Interface cho bulk create request
 */
export interface CreateBulkUserConvertCustomerDto {
  customers: CreateUserConvertCustomerDto[];
  skipDuplicates?: boolean;
  continueOnError?: boolean;
}

/**
 * Interface cho kết quả bulk create item
 */
export interface BulkCreateResultItemDto {
  index: number;
  status: 'success' | 'error' | 'skipped';
  customer?: unknown;
  message?: string;
  errorCode?: string;
  originalData: unknown;
}

/**
 * Interface cho response bulk create
 */
export interface BulkUserConvertCustomerResponseDto {
  totalRequested: number;
  successCount: number;
  errorCount: number;
  skippedCount: number;
  results: BulkCreateResultItemDto[];
  processingTimeMs: number;
}

/**
 * Service xử lý API liên quan đến khách hàng chuyển đổi
 */
export const CustomerService = {
  /**
   * Lấy danh sách khách hàng chuyển đổi của người dùng
   * @param params Tham số truy vấn
   * @returns Danh sách khách hàng chuyển đổi với phân trang
   */
  getConvertCustomers: async (
    params?: QueryUserConvertCustomerDto
  ): Promise<ApiResponseDto<PaginatedUserConvertCustomerResult>> => {
    return apiClient.get('/user/convert-customers', { params });
  },

  /**
   * Lấy chi tiết khách hàng chuyển đổi theo ID
   * @param id ID của khách hàng
   * @returns Chi tiết khách hàng chuyển đổi
   */
  getConvertCustomerById: async (
    id: number
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.get(`/user/convert-customers/${id}`);
  },

  /**
   * Tạo khách hàng chuyển đổi mới
   * @param data Dữ liệu khách hàng
   * @returns Khách hàng được tạo
   */
  createConvertCustomer: async (
    data: CreateConvertCustomerDto
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.post('/user/convert-customers', data);
  },

  /**
   * Cập nhật khách hàng chuyển đổi
   * @param id ID của khách hàng
   * @param data Dữ liệu cập nhật
   * @returns Khách hàng được cập nhật
   */
  updateConvertCustomer: async (
    id: number,
    data: Partial<UserConvertCustomerListItemDto>
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.put(`/user/convert-customers/${id}`, data);
  },

  /**
   * Cập nhật thông tin cơ bản khách hàng
   * @param id ID của khách hàng
   * @param data Dữ liệu thông tin cơ bản
   * @returns Khách hàng được cập nhật và URL upload avatar nếu có
   */
  updateCustomerBasicInfo: async (
    id: number,
    data: UpdateCustomerBasicInfoDto
  ): Promise<ApiResponseDto<UpdateCustomerBasicInfoResponseDto>> => {
    return apiClient.put(`/user/convert-customers/${id}/basic-info`, data);
  },

  /**
   * Cập nhật custom fields khách hàng
   * @param id ID của khách hàng
   * @param data Dữ liệu custom fields
   * @returns Khách hàng được cập nhật
   */
  updateCustomerCustomFields: async (
    id: number,
    data: UpdateCustomerCustomFieldsDto
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.put(`/user/convert-customers/${id}/custom-fields`, data);
  },

  /**
   * Cập nhật social links khách hàng
   * @param id ID của khách hàng
   * @param data Dữ liệu social links
   * @returns Khách hàng được cập nhật
   */
  updateCustomerSocialLinks: async (
    id: number,
    data: UpdateCustomerSocialLinksDto
  ): Promise<ApiResponseDto<UserConvertCustomerListItemDto>> => {
    return apiClient.put(`/user/convert-customers/${id}/social-links`, data);
  },

  /**
   * Xóa khách hàng chuyển đổi
   * @param id ID của khách hàng
   * @returns Kết quả xóa
   */
  deleteConvertCustomer: async (id: number): Promise<ApiResponseDto<void>> => {
    return apiClient.delete(`/user/convert-customers/${id}`);
  },

  /**
   * Xóa nhiều khách hàng chuyển đổi cùng lúc
   * @param customerIds Danh sách ID khách hàng cần xóa
   * @returns Kết quả bulk delete
   */
  deleteBulkConvertCustomers: async (customerIds: number[]): Promise<ApiResponseDto<void>> => {
    return apiClient.delete('/user/convert-customers/bulk', {
      data: { customerIds }
    });
  },

  /**
   * Tạo nhiều khách hàng chuyển đổi cùng lúc
   * @param data Dữ liệu bulk create
   * @returns Kết quả bulk create
   */
  createBulkConvertCustomers: async (
    data: CreateBulkUserConvertCustomerDto
  ): Promise<ApiResponseDto<BulkUserConvertCustomerResponseDto>> => {
    return apiClient.post('/user/convert-customers/bulk', data);
  },
};

export default CustomerService;
