import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Card, Typography } from '@/shared/components/common';

import CompanyObjectiveForm from './CompanyObjectiveForm';
import DepartmentObjectiveForm from './DepartmentObjectiveForm';
import ObjectiveForm from './ObjectiveForm';

/**
 * Demo page để test các form tạo objective với trường parent objective
 */
const ObjectiveFormDemo: React.FC = () => {
  const { t } = useTranslation(['okrs', 'common']);
  const [activeForm, setActiveForm] = useState<'company' | 'department' | 'individual' | null>(null);

  const handleFormSuccess = () => {
    console.log('Form submitted successfully!');
    setActiveForm(null);
  };

  const handleFormCancel = () => {
    setActiveForm(null);
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <Card className="mb-6">
        <div className="p-6">
          <Typography variant="h1" className="mb-4">
            Demo: Objective Forms với Parent Objective
          </Typography>
          <Typography variant="body1" className="mb-6 text-muted-foreground">
            Test các form tạo objective với trường chọn mục tiêu cha sử dụng AsyncSelectWithPagination
          </Typography>

          <div className="flex gap-4 flex-wrap">
            <Button
              variant="primary"
              onClick={() => setActiveForm('company')}
              disabled={activeForm !== null}
            >
              {t('okrs:objective.form.companyTitle', 'Tạo mục tiêu công ty')}
            </Button>
            <Button
              variant="primary"
              onClick={() => setActiveForm('department')}
              disabled={activeForm !== null}
            >
              {t('okrs:objective.form.departmentTitle', 'Tạo mục tiêu phòng ban')}
            </Button>
            <Button
              variant="primary"
              onClick={() => setActiveForm('individual')}
              disabled={activeForm !== null}
            >
              {t('okrs:objective.form.individualTitle', 'Tạo mục tiêu cá nhân')}
            </Button>
          </div>
        </div>
      </Card>

      {activeForm === 'company' && (
        <CompanyObjectiveForm onSuccess={handleFormSuccess} onCancel={handleFormCancel} />
      )}

      {activeForm === 'department' && (
        <DepartmentObjectiveForm onSuccess={handleFormSuccess} onCancel={handleFormCancel} />
      )}

      {activeForm === 'individual' && (
        <ObjectiveForm onSuccess={handleFormSuccess} onCancel={handleFormCancel} />
      )}

      {activeForm === null && (
        <Card>
          <div className="p-6">
            <Typography variant="h3" className="mb-4">
              Hướng dẫn sử dụng
            </Typography>
            <div className="space-y-3">
              <Typography variant="body1">
                <strong>1. Company Objective Form:</strong> Có thể chọn mục tiêu cha từ các mục tiêu công ty khác
              </Typography>
              <Typography variant="body1">
                <strong>2. Department Objective Form:</strong> Có thể chọn mục tiêu cha từ các mục tiêu công ty
              </Typography>
              <Typography variant="body1">
                <strong>3. Individual Objective Form:</strong> Có thể chọn mục tiêu cha từ các mục tiêu công ty hoặc phòng ban
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                Trường "Mục tiêu cha" là tùy chọn và sử dụng AsyncSelectWithPagination để tìm kiếm và chọn.
              </Typography>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ObjectiveFormDemo;
