import { AxiosError } from 'axios';

import { NotificationUtil } from '@/shared/utils/notification';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { InventoryService } from '../services/inventory.service';
import {
  InventoryQueryParams,
  UpdateInventoryQuantityDto,
  CreateInventoryDto,
} from '../types/inventory.types';

/**
 * Interface cho API Error Response
 */
interface ApiErrorResponse {
  response?: {
    data?: {
      message?: string;
      code?: number;
    };
  };
  message?: string;
}

// Query keys
export const INVENTORY_QUERY_KEYS = {
  all: ['inventory'] as const,
  lists: () => [...INVENTORY_QUERY_KEYS.all, 'list'] as const,
  list: (params: InventoryQueryParams) => [...INVENTORY_QUERY_KEYS.lists(), params] as const,
  details: () => [...INVENTORY_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: number) => [...INVENTORY_QUERY_KEYS.details(), id] as const,
  warehouse: (warehouseId: number) => [...INVENTORY_QUERY_KEYS.all, 'warehouse', warehouseId] as const,
};

/**
 * Hook để lấy danh sách inventory items
 * @param params Tham số truy vấn
 * @returns Query object
 */
export const useInventoryItems = (params?: InventoryQueryParams) => {
  const defaultParams: InventoryQueryParams = { page: 1, limit: 10 };
  const queryParams = params || defaultParams;

  return useQuery({
    queryKey: INVENTORY_QUERY_KEYS.list(queryParams),
    queryFn: () => InventoryService.getInventoryItems(params),
    select: (data) => data.result,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để lấy danh sách inventory items theo warehouse
 * @param warehouseId ID của kho
 * @param params Tham số truy vấn bổ sung
 * @returns Query object
 */
export const useInventoryItemsByWarehouse = (
  warehouseId: number,
  params?: Omit<InventoryQueryParams, 'warehouseId'>
) => {
  return useQuery({
    queryKey: INVENTORY_QUERY_KEYS.warehouse(warehouseId),
    queryFn: () => InventoryService.getInventoryItemsByWarehouse(warehouseId, params),
    select: (data) => data.result,
    enabled: !!warehouseId,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để lấy chi tiết inventory item
 * @param id ID của inventory item
 * @returns Query object
 */
export const useInventoryItem = (id: number) => {
  return useQuery({
    queryKey: INVENTORY_QUERY_KEYS.detail(id),
    queryFn: () => InventoryService.getInventoryItemById(id),
    select: (data) => data.result,
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 phút
  });
};

/**
 * Hook để cập nhật số lượng inventory
 * @returns Mutation object
 */
export const useUpdateInventoryQuantity = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateInventoryQuantityDto }) =>
      InventoryService.updateInventoryQuantity(id, data),
    onSuccess: (_, variables) => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.detail(variables.id) });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Cập nhật số lượng thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật số lượng';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để tạo mới inventory item
 * @returns Mutation object
 */
export const useCreateInventoryItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateInventoryDto) => InventoryService.createInventoryItem(data),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Tạo mới inventory thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi tạo mới inventory';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để xóa inventory item
 * @returns Mutation object
 */
export const useDeleteInventoryItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => InventoryService.deleteInventoryItem(id),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Xóa inventory thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi xóa inventory';
      NotificationUtil.error({ message });
    },
  });
};

/**
 * Hook để cập nhật số lượng hàng loạt
 * @returns Mutation object
 */
export const useBulkUpdateQuantities = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (updates: Array<{ id: number; data: UpdateInventoryQuantityDto }>) =>
      InventoryService.bulkUpdateQuantities(updates),
    onSuccess: () => {
      // Invalidate và refetch các query liên quan
      queryClient.invalidateQueries({ queryKey: INVENTORY_QUERY_KEYS.all });

      // Hiển thị thông báo thành công
      NotificationUtil.success({ message: 'Cập nhật số lượng hàng loạt thành công' });
    },
    onError: (error: AxiosError<ApiErrorResponse>) => {
      // Hiển thị thông báo lỗi
      const message = error?.response?.data?.message || 'Có lỗi xảy ra khi cập nhật số lượng hàng loạt';
      NotificationUtil.error({ message });
    },
  });
};
