import React from 'react';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import * as z from 'zod';

import {
  AsyncSelectWithPagination,
  Button,
  Card,
  Form,
  FormItem,
  Input,
  Select,
  Textarea,
  DatePicker,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useFormErrors } from '@/shared/hooks';
import { NotificationUtil } from '@/shared/utils/notification';

import { useCreateTask, useUpdateTask } from '../hooks/useTasks';
import { ProjectService } from '../services/project.service';
import { EmployeeService } from '@/modules/hrm/services/employee.service';
import { EmployeeQueryDto } from '@/modules/hrm/types/employee.types';
import { TaskDto, TaskPriority } from '../types/task.types';

// Định nghĩa schema validation
const taskSchema = z.object({
  title: z
    .string()
    .min(1, 'Tiêu đề công việc không được để trống')
    .max(255, 'Tiêu đề công việc không được vượt quá 255 ký tự'),
  description: z.string().max(1000, 'Mô tả không được vượt quá 1000 ký tự').optional().nullable(),
  assigneeId: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val ? parseInt(val, 10) : null)),
  priority: z
    .string()
    .transform(val => {
      // Chuyển đổi string thành TaskPriority enum
      switch (val) {
        case TaskPriority.LOW:
          return TaskPriority.LOW;
        case TaskPriority.MEDIUM:
          return TaskPriority.MEDIUM;
        case TaskPriority.HIGH:
          return TaskPriority.HIGH;
        case TaskPriority.URGENT:
          return TaskPriority.URGENT;
        default:
          return TaskPriority.MEDIUM;
      }
    })
    .optional()
    .nullable(),
  expectedStars: z
    .number()
    .min(1, 'Số sao kỳ vọng phải từ 1 đến 5')
    .max(5, 'Số sao kỳ vọng phải từ 1 đến 5')
    .optional()
    .nullable(),
  categoryId: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val ? parseInt(val, 10) : null)),
  parentId: z
    .string()
    .optional()
    .nullable()
    .transform(val => (val ? parseInt(val, 10) : null)),
  deadline: z.date().optional().nullable(),
});

// Kiểu dữ liệu form
type TaskFormValues = {
  title: string;
  description?: string | null;
  assigneeId: string;
  priority?: TaskPriority | null;
  expectedStars?: number | null;
  categoryId: string;
  parentId: string;
  deadline?: Date | null;
};

// Props component
interface TaskFormProps {
  task?: TaskDto;
  parentId?: number;
  onSubmit: () => void;
  onCancel: () => void;
}

/**
 * Form tạo/cập nhật công việc
 */
const TaskForm: React.FC<TaskFormProps> = ({ task, parentId, onSubmit, onCancel }) => {
  const { t } = useTranslation(['common', 'todolist']);
  const isEditMode = !!task;

  // Không cần lấy danh sách dự án trước vì sử dụng AsyncSelectWithPagination

  // Hooks mutation
  const { mutateAsync: createTask, isPending: isCreating } = useCreateTask();
  const { mutateAsync: updateTask, isPending: isUpdating } = useUpdateTask();

  // Form hook with error handling
  const { formRef, setFormErrors } = useFormErrors<TaskFormValues>();

  // Xử lý submit form
  const handleSubmit = async (values: unknown) => {
    // Type assertion to TaskFormValues
    const taskValues = values as TaskFormValues;

    try {
      // Reset form errors
      setFormErrors({});

      if (isEditMode && task) {
        await updateTask({
          id: task.id,
          data: {
            title: taskValues.title,
            description: taskValues.description || undefined,
            assigneeId: taskValues.assigneeId ? parseInt(taskValues.assigneeId, 10) : undefined,
            priority: taskValues.priority || undefined,
            expectedStars: taskValues.expectedStars || undefined,
            categoryId: taskValues.categoryId ? parseInt(taskValues.categoryId, 10) : undefined,
            parentId: taskValues.parentId ? parseInt(taskValues.parentId, 10) : undefined,
            deadline: taskValues.deadline ? taskValues.deadline.getTime() : undefined,
          },
        });
        NotificationUtil.success({
          message: t('todolist:task.notifications.updateSuccess', 'Cập nhật công việc thành công'),
        });
      } else {
        await createTask({
          title: taskValues.title,
          description: taskValues.description || undefined,
          assigneeId: taskValues.assigneeId ? parseInt(taskValues.assigneeId, 10) : undefined,
          priority: taskValues.priority || undefined,
          expectedStars: taskValues.expectedStars || undefined,
          categoryId: taskValues.categoryId ? parseInt(taskValues.categoryId, 10) : undefined,
          parentId: taskValues.parentId ? parseInt(taskValues.parentId, 10) : undefined,
          deadline: taskValues.deadline ? taskValues.deadline.getTime() : undefined,
        });
        NotificationUtil.success({
          message: t('todolist:task.notifications.createSuccess', 'Tạo công việc thành công'),
        });
      }
      onSubmit();
    } catch (error) {
      console.error('Error submitting task form:', error);

      // Check if error has field-specific errors
      if (error && typeof error === 'object' && 'response' in error && error.response) {
        const axiosError = error as {
          response: { data?: { message?: string; errors?: Record<string, string> } };
        };

        // If there are field-specific errors, set them
        if (axiosError.response.data?.errors) {
          setFormErrors(axiosError.response.data.errors);
        } else {
          // Otherwise set a general error
          NotificationUtil.error({
            message: isEditMode
              ? t('todolist:task.notifications.updateError', 'Lỗi khi cập nhật công việc')
              : t('todolist:task.notifications.createError', 'Lỗi khi tạo công việc'),
          });
        }
      } else {
        // For other errors, show a general notification
        NotificationUtil.error({
          message: isEditMode
            ? t('todolist:task.notifications.updateError', 'Lỗi khi cập nhật công việc')
            : t('todolist:task.notifications.createError', 'Lỗi khi tạo công việc'),
        });
      }
    }
  };

  // Danh sách mức độ ưu tiên
  const priorityOptions = [
    { value: TaskPriority.LOW, label: t('todolist:task.priority.low', 'Thấp') },
    { value: TaskPriority.MEDIUM, label: t('todolist:task.priority.medium', 'Trung bình') },
    { value: TaskPriority.HIGH, label: t('todolist:task.priority.high', 'Cao') },
    { value: TaskPriority.URGENT, label: t('todolist:task.priority.urgent', 'Khẩn cấp') },
  ];

  // Function để load employees cho AsyncSelectWithPagination
  const loadEmployeesForSelect = async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const { search = '', page = 1, limit = 20 } = params;

      const query: EmployeeQueryDto = {
        page,
        limit,
        search: search.trim() || undefined,
      };

      const response = await EmployeeService.getEmployees(query);

      const items = response.result.items.map(employee => ({
        value: employee.id.toString(),
        label: `${employee.employeeName} - ${employee.department?.name || 'N/A'}`,
        data: {
          employeeName: employee.employeeName,
          employeeCode: employee.employeeCode,
          departmentName: employee.department?.name,
          jobTitle: employee.jobTitle,
        }
      }));

      return {
        items,
        totalItems: response.result.meta.totalItems,
        totalPages: response.result.meta.totalPages,
        currentPage: response.result.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading employees:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };

  // Function để load projects cho AsyncSelectWithPagination
  const loadProjectsForSelect = async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await ProjectService.getProjects({
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
      });

      const result = response.result;
      return {
        items: result.items.map((project: any) => ({
          value: project.id.toString(),
          label: project.title,
        })),
        totalItems: result.meta.totalItems,
        totalPages: result.meta.totalPages,
        currentPage: result.meta.currentPage,
      };
    } catch (error) {
      console.error('Error loading projects:', error);
      return {
        items: [],
        totalItems: 0,
        totalPages: 0,
        currentPage: 1,
      };
    }
  };

  return (
    <Card>
      <Form
        ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
        schema={taskSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
        defaultValues={{
          title: task?.title || '',
          description: task?.description || '',
          assigneeId: task?.assigneeId ? task.assigneeId.toString() : '',
          priority: task?.priority || TaskPriority.MEDIUM,
          expectedStars: task?.expectedStars || 3,
          categoryId: task?.categoryId ? task.categoryId.toString() : '',
          parentId: parentId ? parentId.toString() : task?.parentId ? task.parentId.toString() : '',
          deadline: task?.deadline ? new Date(task.deadline) : null,
        }}
      >
        <FormItem name="title" label={t('todolist:task.fields.title', 'Tiêu đề')} required>
          <Input
            placeholder={t('todolist:task.placeholders.title', 'Nhập tiêu đề công việc')}
            fullWidth
          />
        </FormItem>

        <FormItem name="description" label={t('todolist:task.fields.description', 'Mô tả')}>
          <Textarea
            placeholder={t('todolist:task.placeholders.description', 'Nhập mô tả công việc')}
            rows={4}
          />
        </FormItem>

        <div className="grid grid-cols-12 gap-4">
          <FormItem
            name="priority"
            label={t('todolist:task.fields.priority', 'Mức độ ưu tiên')}
            className="col-span-8"
          >
            <Select
              placeholder={t('todolist:task.placeholders.priority', 'Chọn mức độ ưu tiên')}
              options={priorityOptions}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="expectedStars"
            label={t('todolist:task.fields.expectedStars', 'Số sao kỳ vọng')}
            className="col-span-4"
          >
            <Input
              type="number"
              min={1}
              max={5}
              placeholder={t(
                'todolist:task.placeholders.expectedStars',
                'Nhập số sao kỳ vọng (1-5)'
              )}
              fullWidth
            />
          </FormItem>
        </div>

        <FormItem name="assigneeId" label={t('todolist:task.fields.assignee', 'Người thực hiện')}>
          <AsyncSelectWithPagination
            placeholder={t('todolist:task.placeholders.assignee', 'Chọn người thực hiện')}
            loadOptions={loadEmployeesForSelect}
            debounceTime={300}
            itemsPerPage={20}
            noOptionsMessage={t('todolist:task.noEmployees', 'Không tìm thấy nhân viên nào')}
            loadingMessage={t('todolist:task.loadingEmployees', 'Đang tải danh sách nhân viên...')}
            fullWidth
          />
        </FormItem>

        <FormItem name="categoryId" label={t('todolist:task.fields.project', 'Dự án')}>
          <AsyncSelectWithPagination
            placeholder={t('todolist:task.placeholders.project', 'Chọn dự án')}
            loadOptions={loadProjectsForSelect}
            debounceTime={300}
            itemsPerPage={20}
            noOptionsMessage={t('todolist:task.noProjects', 'Không tìm thấy dự án nào')}
            loadingMessage={t('todolist:task.loadingProjects', 'Đang tải danh sách dự án...')}
            fullWidth
          />
        </FormItem>

        <FormItem name="deadline" label={t('todolist:task.fields.deadline', 'Hạn chót')}>
          <DatePicker
            placeholder={t('todolist:task.placeholders.deadline', 'Chọn hạn chót')}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={onCancel} disabled={isCreating || isUpdating}>
            {t('common:cancel', 'Hủy')}
          </Button>
          <Button type="submit" isLoading={isCreating || isUpdating}>
            {isEditMode ? t('common:update', 'Cập nhật') : t('common:create', 'Tạo mới')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default TaskForm;
