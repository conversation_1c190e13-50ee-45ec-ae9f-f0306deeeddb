import React from 'react';
import { useTranslation } from 'react-i18next';

import { Typography } from '@/shared/components/common';

import StarRating from '../StarRating/StarRating';

interface RatingSelectorProps {
  /**
   * Current rating value (1-5)
   */
  value: number;
  
  /**
   * Callback when rating changes
   */
  onChange: (rating: number) => void;
  
  /**
   * Label for the rating selector
   */
  label?: string;
  
  /**
   * Whether the rating is interactive
   */
  interactive?: boolean;
  
  /**
   * Size of the stars
   */
  size?: 'sm' | 'md' | 'lg' | 'xl';
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Show rating value as text
   */
  showValue?: boolean;
}

/**
 * Rating Selector Component - Reusable component for star rating selection
 */
const RatingSelector: React.FC<RatingSelectorProps> = ({
  value,
  onChange,
  label,
  interactive = true,
  size = 'lg',
  className = '',
  showValue = true,
}) => {
  const { t } = useTranslation();

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <Typography variant="subtitle2" className="text-gray-500 dark:text-gray-400">
          {label}
        </Typography>
      )}
      
      <div className="flex items-center gap-3">
        <StarRating
          value={value}
          onChange={onChange}
          interactive={interactive}
          size={size}
          maxStars={5}
        />
        
        {showValue && (
          <Typography variant="body2" className="text-muted-foreground min-w-[60px]">
            {value > 0 ? `${value}/5` : t('common:notRated', 'Chưa đánh giá')}
          </Typography>
        )}
      </div>
    </div>
  );
};

export default RatingSelector;
