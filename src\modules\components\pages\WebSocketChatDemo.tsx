/**
 * WebSocket Chat Demo Page
 * Demonstrates the new WebSocket chat functionality
 */

import React, { useState } from 'react';

import { 
  Card, 
  Button, 
  Typography, 
  Input, 
  FormItem,
  Form,
  Modal
} from '@/shared/components/common';
import { ChatPanelWebSocket } from '@/shared/components/layout/chat-panel';
import { useFormErrors } from '@/shared/hooks/useFormErrors';

// Demo configuration interface
interface DemoConfig {
  websocketUrl: string;
  roomId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
}

/**
 * WebSocket Chat Demo Page
 */
const WebSocketChatDemo: React.FC = () => {
  const [showChat, setShowChat] = useState(false);
  const [showConfig, setShowConfig] = useState(false);
  const [config, setConfig] = useState<DemoConfig>({
    websocketUrl: import.meta.env.VITE_WEBSOCKET_URL || 'ws://localhost:3001',
    roomId: 'demo-room',
    userId: `user-${Date.now()}`,
    userName: 'Demo User',
    userAvatar: undefined,
  });

  const { formRef, setFormErrors } = useFormErrors<DemoConfig>();

  // Handle configuration save
  const handleConfigSave = (formData: DemoConfig) => {
    setConfig(formData);
    setShowConfig(false);
    setShowChat(true);
  };

  // Handle chat close
  const handleChatClose = () => {
    setShowChat(false);
  };

  // Handle keyword detection
  const handleKeywordDetected = (keyword: string) => {
    console.log('Keyword detected:', keyword);
  };

  // Demo message examples
  const demoMessages = [
    {
      type: 'text',
      title: 'Text Message',
      description: 'Send a simple text message',
      example: 'Hello, this is a text message!',
    },
    {
      type: 'markdown',
      title: 'Markdown Message',
      description: 'Send formatted markdown content',
      example: '## Hello World\n\nThis is **bold** and *italic* text with [links](https://example.com).',
    },
    {
      type: 'form',
      title: 'Form Message',
      description: 'Interactive form with real-time sync',
      example: 'Form will be rendered with fields for user input',
    },
    {
      type: 'navigation',
      title: 'Navigation Message',
      description: 'Navigation buttons and page redirects',
      example: 'Buttons to navigate to different pages',
    },
    {
      type: 'stream',
      title: 'Streaming Message',
      description: 'Real-time streaming content',
      example: 'Content that streams in real-time chunks',
    },
  ];

  // Render configuration modal
  const renderConfigModal = () => (
    <Modal
      isOpen={showConfig}
      onClose={() => setShowConfig(false)}
      title="WebSocket Chat Configuration"
      size="md"
    >
      <div className="p-6">
        <Form
          ref={formRef}
          onSubmit={handleConfigSave}
          defaultValues={config}
          className="space-y-4"
        >
          <FormItem name="websocketUrl" label="WebSocket URL" required>
            <Input 
              type="url" 
              placeholder="ws://localhost:3001"
              defaultValue={config.websocketUrl}
            />
          </FormItem>

          <FormItem name="roomId" label="Room ID" required>
            <Input 
              placeholder="demo-room"
              defaultValue={config.roomId}
            />
          </FormItem>

          <FormItem name="userId" label="User ID" required>
            <Input 
              placeholder="user-123"
              defaultValue={config.userId}
            />
          </FormItem>

          <FormItem name="userName" label="User Name" required>
            <Input 
              placeholder="Demo User"
              defaultValue={config.userName}
            />
          </FormItem>

          <FormItem name="userAvatar" label="User Avatar URL">
            <Input 
              type="url"
              placeholder="https://example.com/avatar.jpg"
              defaultValue={config.userAvatar}
            />
          </FormItem>

          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowConfig(false)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              className="flex-1"
            >
              Save & Start Chat
            </Button>
          </div>
        </Form>
      </div>
    </Modal>
  );

  // Render demo page
  if (showChat) {
    return (
      <div className="w-full h-screen bg-background text-foreground">
        <ChatPanelWebSocket
          onClose={handleChatClose}
          onKeywordDetected={handleKeywordDetected}
          roomId={config.roomId}
          userId={config.userId}
          userInfo={{
            name: config.userName,
            avatar: config.userAvatar,
          }}
          websocketConfig={{
            url: config.websocketUrl,
            auth: {
              userId: config.userId,
            },
          }}
        />
      </div>
    );
  }

  return (
    <div className="w-full bg-background text-foreground p-6">
      {/* Header */}
      <div className="mb-8">
        <Typography variant="h1" className="mb-4">
          WebSocket Chat Demo
        </Typography>
        <Typography variant="body1" className="text-gray-600 dark:text-gray-400 max-w-3xl">
          This demo showcases the new WebSocket-powered chat system with support for real-time messaging, 
          forms, streaming content, navigation, and multimedia messages.
        </Typography>
      </div>

      {/* Features Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <Card className="p-6">
          <Typography variant="h6" className="mb-3">
            🔄 Real-time Messaging
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Instant message delivery with WebSocket connections, typing indicators, and message status.
          </Typography>
        </Card>

        <Card className="p-6">
          <Typography variant="h6" className="mb-3">
            📝 Interactive Forms
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Real-time form synchronization with validation and automatic data filling from backend.
          </Typography>
        </Card>

        <Card className="p-6">
          <Typography variant="h6" className="mb-3">
            🎥 Rich Media
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Support for images, videos, audio, files, and link previews with interactive controls.
          </Typography>
        </Card>

        <Card className="p-6">
          <Typography variant="h6" className="mb-3">
            📊 Streaming Content
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Real-time content streaming with progress indicators and chunk acknowledgments.
          </Typography>
        </Card>

        <Card className="p-6">
          <Typography variant="h6" className="mb-3">
            🧭 Navigation
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Interactive navigation buttons for page redirects, modals, and downloads.
          </Typography>
        </Card>

        <Card className="p-6">
          <Typography variant="h6" className="mb-3">
            📝 Markdown Support
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
            Full markdown rendering with syntax highlighting and custom components.
          </Typography>
        </Card>
      </div>

      {/* Message Types */}
      <div className="mb-8">
        <Typography variant="h2" className="mb-4">
          Supported Message Types
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {demoMessages.map((message) => (
            <Card key={message.type} className="p-4">
              <Typography variant="h6" className="mb-2">
                {message.title}
              </Typography>
              <Typography variant="body2" className="text-gray-600 dark:text-gray-400 mb-3">
                {message.description}
              </Typography>
              <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded text-sm">
                <Typography variant="caption" className="text-gray-500">
                  Example: {message.example}
                </Typography>
              </div>
            </Card>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="flex gap-4">
        <Button
          variant="primary"
          size="lg"
          onClick={() => setShowConfig(true)}
        >
          Configure & Start Demo
        </Button>
        
        <Button
          variant="outline"
          size="lg"
          onClick={() => {
            setShowChat(true);
          }}
        >
          Quick Start (Default Config)
        </Button>
      </div>

      {/* Configuration Modal */}
      {renderConfigModal()}
    </div>
  );
};

export default WebSocketChatDemo;
