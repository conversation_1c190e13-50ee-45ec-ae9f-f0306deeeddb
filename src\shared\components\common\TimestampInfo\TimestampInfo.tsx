import React from 'react';

import { formatTimestamp, getRelativeTimeLabel } from '@/shared/utils/form-date-utils';

type Props = {
  value: unknown;
};

const TimestampInfo: React.FC<Props> = ({ value }) => {
  return (
    <div className="flex flex-col text-sm">
      <div>{getRelativeTimeLabel(value)}</div>
      <div>{formatTimestamp(value)}</div>
    </div>
  );
};

export default TimestampInfo;
