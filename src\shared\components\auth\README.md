# Authentication Components

Các component và utilities để quản lý authentication trong ứng dụng.

## Components

### ProtectedRoute

Component HOC để bảo vệ các route yêu cầu authentication.

```tsx
import { ProtectedRoute } from '@/shared/components/auth';

// Sử dụng trực tiếp
<ProtectedRoute redirectTo="/auth/login">
  <YourComponent />
</ProtectedRoute>

// Hoặc sử dụng với router
const protectedRoutes = [
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <DashboardPage />
      </ProtectedRoute>
    ),
  },
];
```

### AuthProvider

Component để khôi phục và kiểm tra trạng thái authentication khi ứng dụng khởi động.

```tsx
import { AuthProvider } from '@/shared/components/auth';

function App() {
  return (
    <AuthProvider>
      <Router />
    </AuthProvider>
  );
}
```

## HOC (Higher-Order Components)

### withAuth

HOC để wrap component với authentication protection.

```tsx
import { withAuth } from '@/shared/hoc';

const ProtectedComponent = withAuth(YourComponent);

// Hoặc với custom redirect
const ProtectedComponent = withAuth(YourComponent, '/custom-login');
```

### withAuthPage

Tương tự `withAuth` nhưng được tối ưu cho page components.

```tsx
import { withAuthPage } from '@/shared/hoc';

const ProtectedPage = withAuthPage(YourPage);
```

## Utilities

### protectRoutes

Utility function để wrap nhiều routes với ProtectedRoute.

```tsx
import { protectRoutes } from '@/shared/utils/routeUtils';

const router = createBrowserRouter([
  // Routes không cần bảo vệ
  ...authRoutes,
  
  // Routes cần bảo vệ
  ...protectRoutes(homeRoutes),
  ...protectRoutes(dashboardRoutes),
]);
```

### createProtectedRoute

Utility function để tạo một protected route đơn lẻ.

```tsx
import { createProtectedRoute } from '@/shared/utils/routeUtils';

const protectedRoute = createProtectedRoute({
  path: '/dashboard',
  element: <DashboardPage />,
});
```

## Cách hoạt động

1. **Token Validation**: Sử dụng `useAuth` hook để kiểm tra token hợp lệ
2. **Auto Cleanup**: Tự động xóa token hết hạn và redirect về login
3. **State Preservation**: Lưu đường dẫn hiện tại để redirect sau khi đăng nhập
4. **Centralized Logic**: Tất cả logic authentication được tập trung trong `useAuth` hook

## Lưu ý

- Tất cả components đều sử dụng `useAuth` hook thay vì truy cập localStorage trực tiếp
- Điều này giúp dễ dàng thay đổi logic authentication sau này
- Token validation được thực hiện tự động khi component mount và khi dependencies thay đổi
