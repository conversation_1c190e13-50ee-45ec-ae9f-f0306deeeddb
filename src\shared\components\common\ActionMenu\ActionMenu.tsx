import React, { useRef, useState } from 'react';

import { IconCard, Tooltip } from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import ModernMenu from '@/shared/components/common/ModernMenu/ModernMenu';

// Định nghĩa type cho divider item
export interface ActionMenuDividerItem {
  /**
   * ID duy nhất của divider
   */
  id: string;

  /**
   * Đánh dấu đây là divider
   */
  divider: true;
}

// Định nghĩa type cho action item thông thường
export interface ActionMenuActionItem {
  /**
   * ID duy nhất của action
   */
  id: string;

  /**
   * Nhãn hiển thị trong menu
   */
  label: React.ReactNode;

  /**
   * Icon hiển thị trong menu
   */
  icon?: IconName;

  /**
   * Hàm xử lý khi click vào item
   */
  onClick: () => void;

  /**
   * Trạng thái disabled của item
   */
  disabled?: boolean;

  /**
   * Đ<PERSON>h dấu đây không phải là divider
   */
  divider?: false;

  /**
   * Tooltip hiển thị khi hover vào item trong trường hợp hiển thị trực tiếp
   */
  tooltip?: string;

  /**
   * Có hiển thị item này trực tiếp không (ngoài menu)
   */
  showDirect?: boolean;
}

// Union type cho cả hai loại item
export type ActionMenuItem = ActionMenuActionItem | ActionMenuDividerItem;

export interface ActionMenuProps {
  /**
   * Danh sách các action
   */
  items: ActionMenuItem[];

  /**
   * Tooltip cho nút menu
   */
  menuTooltip?: string;

  /**
   * Vị trí hiển thị menu
   */
  placement?: 'top' | 'right' | 'bottom' | 'left';

  /**
   * Chiều rộng của menu
   */
  menuWidth?: string;

  /**
   * Kích thước của các icon
   */
  iconSize?: 'sm' | 'md' | 'lg';

  /**
   * Variant của các icon
   */
  iconVariant?: 'default' | 'primary' | 'secondary' | 'ghost';

  /**
   * Icon hiển thị cho nút menu
   */
  menuIcon?: IconName;

  /**
   * Hiển thị tất cả action trong menu thay vì trực tiếp
   */
  showAllInMenu?: boolean;

  /**
   * Ưu tiên hiển thị menu bên phải nếu không đủ không gian bên trái
   */
  preferRight?: boolean;

  /**
   * Ưu tiên hiển thị menu bên trên nếu không đủ không gian bên dưới
   */
  preferTop?: boolean;
}

/**
 * Component ActionMenu hiển thị menu các hành động với khả năng tùy chỉnh
 */
const ActionMenu: React.FC<ActionMenuProps> = ({
  items,
  menuTooltip = 'Menu',
  placement = 'bottom',
  menuWidth = '180px',
  iconSize = 'sm',
  iconVariant = 'default',
  menuIcon = 'more-vertical',
  showAllInMenu = false,
  preferRight = true, // Mặc định ưu tiên hiển thị bên phải
  preferTop = true, // Mặc định ưu tiên hiển thị bên trên
}) => {
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Lọc các item hiển thị trực tiếp và các item trong menu
  const directItems = showAllInMenu
    ? []
    : items.filter(
        (item): item is ActionMenuActionItem => 'showDirect' in item && !!item.showDirect
      );

  // Nếu showAllInMenu = true, tất cả các item sẽ hiển thị trong menu
  // Nếu không, chỉ các item không có showDirect hoặc showDirect = false sẽ hiển thị trong menu
  const menuItems = showAllInMenu
    ? items.filter(
        (item): item is ActionMenuActionItem => !('divider' in item && item.divider === true)
      )
    : items.filter(item => !('showDirect' in item) || !item.showDirect);

  // Chuyển đổi từ ActionMenuItem sang ModernMenuItem
  const modernMenuItems = menuItems.map(item => {
    if ('divider' in item && item.divider === true) {
      return {
        id: item.id,
        divider: true,
      };
    } else {
      return {
        id: item.id,
        label: (item as ActionMenuActionItem).label,
        icon: (item as ActionMenuActionItem).icon,
        onClick: (item as ActionMenuActionItem).onClick,
        disabled: (item as ActionMenuActionItem).disabled,
        divider: false,
      };
    }
  });

  const handleToggleMenu = () => {
    setShowMenu(!showMenu);
  };

  const handleCloseMenu = () => {
    setShowMenu(false);
  };

  return (
    <div className="action-menu-container flex items-center space-x-2 relative">
      {/* Hiển thị các item trực tiếp */}
      {directItems.map(item => (
        <Tooltip key={item.id} content={item.tooltip || item.label} position="top">
          <IconCard
            icon={item.icon as IconName}
            variant={iconVariant}
            size={iconSize}
            onClick={item.onClick}
            disabled={item.disabled}
            className="relative z-10"
          />
        </Tooltip>
      ))}

      {/* Hiển thị menu nếu có các item trong menu */}
      {menuItems.length > 0 && (
        <div className="relative inline-block" ref={menuRef}>
          <Tooltip content={menuTooltip} position="top">
            <IconCard
              icon={menuIcon}
              variant={showMenu ? 'primary' : iconVariant}
              size={iconSize}
              onClick={handleToggleMenu}
              active={showMenu}
              className="relative z-10"
            />
          </Tooltip>

          {showMenu && (
            <ModernMenu
              isOpen={showMenu}
              onClose={handleCloseMenu}
              placement={placement}
              width={menuWidth}
              items={modernMenuItems}
              preferRight={preferRight}
              preferTop={preferTop}
              triggerRef={menuRef}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default ActionMenu;
