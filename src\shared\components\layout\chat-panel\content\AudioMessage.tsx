/**
 * Audio Message Component
 * Renders audio content with custom controls
 */

import React, { useState, useRef } from 'react';

import { Typography, Button, Icon } from '@/shared/components/common';
import { AudioContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface AudioMessageProps {
  data: AudioContentData;
  className?: string;
}

/**
 * Audio Message Component
 */
const AudioMessage: React.FC<AudioMessageProps> = ({ data, className = '' }) => {
  const {
    url,
    title,
    duration,
    autoplay = false,
    controls = true,
    muted = false,
    loop = false,
  } = data;

  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [audioDuration, setAudioDuration] = useState(duration || 0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(muted);
  const [audioLoaded, setAudioLoaded] = useState(false);
  const [audioError, setAudioError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Format time
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle play/pause
  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
    }
  };

  // Handle audio events
  const handleAudioLoad = () => {
    setAudioLoaded(true);
    setAudioError(false);
    setIsLoading(false);
    if (audioRef.current) {
      setAudioDuration(audioRef.current.duration);
    }
  };

  const handleAudioError = () => {
    setAudioError(true);
    setAudioLoaded(false);
    setIsLoading(false);
  };

  const handlePlay = () => {
    setIsPlaying(true);
  };

  const handlePause = () => {
    setIsPlaying(false);
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleLoadStart = () => {
    setIsLoading(true);
  };

  // Handle seek
  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const time = parseFloat(e.target.value);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
      setCurrentTime(time);
    }
  };

  // Handle volume change
  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
    if (newVolume === 0) {
      setIsMuted(true);
    } else if (isMuted) {
      setIsMuted(false);
    }
  };

  // Handle mute toggle
  const handleMuteToggle = () => {
    if (audioRef.current) {
      const newMuted = !isMuted;
      setIsMuted(newMuted);
      audioRef.current.muted = newMuted;
    }
  };

  // Handle skip forward/backward
  const handleSkip = (seconds: number) => {
    if (audioRef.current) {
      const newTime = Math.max(0, Math.min(audioDuration, currentTime + seconds));
      audioRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  // Calculate progress percentage
  const progressPercentage = audioDuration > 0 ? (currentTime / audioDuration) * 100 : 0;

  // Render loading state
  const renderLoading = () => (
    <div className="flex items-center justify-center p-4">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
      <Typography variant="caption" className="text-gray-500">
        Loading audio...
      </Typography>
    </div>
  );

  // Render error state
  const renderError = () => (
    <div className="flex items-center justify-center p-4">
      <Icon name="alert-circle" size="sm" className="text-red-500 mr-2" />
      <Typography variant="caption" className="text-red-500">
        Failed to load audio
      </Typography>
    </div>
  );

  // Render audio controls
  const renderControls = () => (
    <div className="p-4 space-y-3">
      {/* Progress bar */}
      <div className="relative">
        <input
          type="range"
          min="0"
          max={audioDuration}
          value={currentTime}
          onChange={handleSeek}
          className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
          style={{
            background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${progressPercentage}%, #e5e7eb ${progressPercentage}%, #e5e7eb 100%)`
          }}
        />
      </div>

      {/* Time display */}
      <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
        <span>{formatTime(currentTime)}</span>
        <span>{formatTime(audioDuration)}</span>
      </div>

      {/* Main controls */}
      <div className="flex items-center justify-center space-x-4">
        {/* Skip backward */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleSkip(-10)}
          className="p-2"
          disabled={!audioLoaded}
        >
          <Icon name="skip-back" size="sm" />
        </Button>

        {/* Play/Pause */}
        <Button
          variant="primary"
          size="lg"
          onClick={handlePlayPause}
          className="rounded-full p-3"
          disabled={!audioLoaded}
        >
          <Icon name={isPlaying ? 'pause' : 'play'} size="md" />
        </Button>

        {/* Skip forward */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleSkip(10)}
          className="p-2"
          disabled={!audioLoaded}
        >
          <Icon name="skip-forward" size="sm" />
        </Button>
      </div>

      {/* Volume controls */}
      <div className="flex items-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMuteToggle}
          className="p-2"
        >
          <Icon 
            name={isMuted || volume === 0 ? 'volume-x' : volume < 0.5 ? 'volume-1' : 'volume-2'} 
            size="sm" 
          />
        </Button>
        
        <input
          type="range"
          min="0"
          max="1"
          step="0.1"
          value={isMuted ? 0 : volume}
          onChange={handleVolumeChange}
          className="flex-1 h-1 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer"
        />
      </div>
    </div>
  );

  return (
    <div className={`rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Audio element */}
      <audio
        ref={audioRef}
        src={url}
        autoPlay={autoplay}
        muted={muted}
        loop={loop}
        onLoadedData={handleAudioLoad}
        onError={handleAudioError}
        onPlay={handlePlay}
        onPause={handlePause}
        onTimeUpdate={handleTimeUpdate}
        onLoadStart={handleLoadStart}
        style={{ display: 'none' }}
      />

      {/* Header */}
      <div className="flex items-center p-4 border-b border-gray-200 dark:border-gray-700">
        <Icon name="music" size="sm" className="text-blue-500 mr-3" />
        <div className="flex-1">
          <Typography variant="body2" className="font-medium">
            {title || 'Audio Message'}
          </Typography>
          {audioDuration > 0 && (
            <Typography variant="caption" className="text-gray-500 dark:text-gray-400">
              Duration: {formatTime(audioDuration)}
            </Typography>
          )}
        </div>
      </div>

      {/* Content */}
      {isLoading && renderLoading()}
      {audioError && renderError()}
      {audioLoaded && !controls && renderControls()}
      
      {/* Native controls fallback */}
      {audioLoaded && controls && (
        <div className="p-4">
          <audio
            src={url}
            controls
            className="w-full"
            autoPlay={autoplay}
            muted={muted}
            loop={loop}
          />
        </div>
      )}
    </div>
  );
};

export default AudioMessage;
