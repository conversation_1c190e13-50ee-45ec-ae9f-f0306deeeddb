/**
 * Message Stream Hook
 * Handles streaming messages in real-time
 */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { getChatWebSocketService } from '../../services/chat-websocket.service';
import {
  StreamChunkData,
  StreamCompleteData,
  StreamErrorData,
  StreamStartData,
} from '../types/websocket-events.types';

// Stream status
export type StreamStatus = 'idle' | 'streaming' | 'complete' | 'error';

// Stream chunk
export interface StreamChunk {
  index: number;
  content: string;
  timestamp: string;
}

// Stream state
export interface StreamState {
  streamId: string;
  messageId: string;
  status: StreamStatus;
  chunks: StreamChunk[];
  currentContent: string;
  totalChunks?: number;
  error?: string;
  startTime?: string;
  endTime?: string;
}

// Hook options
export interface UseMessageStreamOptions {
  messageId: string;
  onStreamStart?: (data: StreamStartData) => void;
  onStreamChunk?: (chunk: StreamChunk) => void;
  onStreamComplete?: (finalContent: string) => void;
  onStreamError?: (error: string) => void;
  autoAcknowledge?: boolean;
  bufferSize?: number;
  maxRetries?: number;
}

// Hook return type
export interface UseMessageStreamReturn {
  // Stream state
  streamState: StreamState | null;
  isStreaming: boolean;
  isComplete: boolean;
  hasError: boolean;

  // Content
  currentContent: string;
  chunks: StreamChunk[];
  progress: number; // 0-100

  // Controls
  acknowledgeChunk: (chunkIndex: number) => void;
  retryStream: () => void;
  cancelStream: () => void;

  // Status
  error: string | null;
  totalChunks: number | null;
  receivedChunks: number;
}

/**
 * Message Stream Hook
 */
export function useMessageStream(options: UseMessageStreamOptions): UseMessageStreamReturn {
  const {
    messageId,
    onStreamStart,
    onStreamChunk,
    onStreamComplete,
    onStreamError,
    autoAcknowledge = true,
    bufferSize = 1000,
    maxRetries = 3,
  } = options;

  // State
  const [streamState, setStreamState] = useState<StreamState | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Refs
  const serviceRef = useRef(getChatWebSocketService());
  const bufferRef = useRef<string>('');
  const lastChunkIndexRef = useRef(-1);

  // Initialize stream state
  const initializeStream = useCallback(
    (streamId: string, totalChunks?: number) => {
      setStreamState({
        streamId,
        messageId,
        status: 'streaming',
        chunks: [],
        currentContent: '',
        totalChunks,
        startTime: new Date().toISOString(),
      });
      bufferRef.current = '';
      lastChunkIndexRef.current = -1;
    },
    [messageId]
  );

  // Add chunk to stream
  const addChunk = useCallback(
    (chunkData: StreamChunkData) => {
      const chunk: StreamChunk = {
        index: chunkData.chunkIndex,
        content: chunkData.chunk,
        timestamp: new Date().toISOString(),
      };

      setStreamState(prev => {
        if (!prev || prev.streamId !== chunkData.streamId) {
          return prev;
        }

        const newChunks = [...prev.chunks];

        // Insert chunk at correct position
        const existingIndex = newChunks.findIndex(c => c.index === chunk.index);
        if (existingIndex >= 0) {
          newChunks[existingIndex] = chunk;
        } else {
          newChunks.push(chunk);
          newChunks.sort((a, b) => a.index - b.index);
        }

        // Build current content from ordered chunks
        let currentContent = '';
        for (let i = 0; i <= Math.max(...newChunks.map(c => c.index)); i++) {
          const chunkAtIndex = newChunks.find(c => c.index === i);
          if (chunkAtIndex) {
            currentContent += chunkAtIndex.content;
          } else {
            // Missing chunk, stop building content
            break;
          }
        }

        return {
          ...prev,
          chunks: newChunks,
          currentContent,
          status: chunkData.isComplete
            ? ('complete' as StreamStatus)
            : ('streaming' as StreamStatus),
          endTime: chunkData.isComplete ? new Date().toISOString() : prev.endTime,
        };
      });

      // Call callback
      if (onStreamChunk) {
        onStreamChunk(chunk);
      }

      // Auto-acknowledge if enabled
      if (autoAcknowledge) {
        acknowledgeChunk(chunk.index);
      }

      // Update last chunk index
      lastChunkIndexRef.current = Math.max(lastChunkIndexRef.current, chunk.index);
    },
    [onStreamChunk, autoAcknowledge]
  );

  // Complete stream
  const completeStream = useCallback(
    (data: StreamCompleteData) => {
      setStreamState(prev => {
        if (!prev || prev.streamId !== data.streamId) {
          return prev;
        }

        return {
          ...prev,
          status: 'complete' as StreamStatus,
          currentContent: data.finalContent,
          endTime: new Date().toISOString(),
        };
      });

      if (onStreamComplete) {
        onStreamComplete(data.finalContent);
      }
    },
    [onStreamComplete]
  );

  // Handle stream error
  const handleStreamError = useCallback(
    (data: StreamErrorData) => {
      setStreamState(prev => {
        if (!prev || prev.streamId !== data.streamId) {
          return prev;
        }

        return {
          ...prev,
          status: 'error',
          error: data.error,
          endTime: new Date().toISOString(),
        };
      });

      if (onStreamError) {
        onStreamError(data.error);
      }
    },
    [onStreamError]
  );

  // Set up event listeners
  useEffect(() => {
    const service = serviceRef.current;
    if (!service) {return;}

    const unsubscribers: Array<() => void> = [];

    // Stream start
    unsubscribers.push(
      service.on('stream_start', (data: StreamStartData) => {
        if (data.messageId === messageId) {
          initializeStream(data.streamId, data.totalChunks);
          if (onStreamStart) {
            onStreamStart(data);
          }
        }
      })
    );

    // Stream chunk
    unsubscribers.push(
      service.on('stream_chunk', (data: StreamChunkData) => {
        if (data.messageId === messageId) {
          addChunk(data);
        }
      })
    );

    // Stream complete
    unsubscribers.push(
      service.on('stream_complete', (data: StreamCompleteData) => {
        if (data.messageId === messageId) {
          completeStream(data);
        }
      })
    );

    // Stream error
    unsubscribers.push(
      service.on('stream_error', (data: StreamErrorData) => {
        if (data.messageId === messageId) {
          handleStreamError(data);
        }
      })
    );

    return () => {
      for (const unsubscribe of unsubscribers) {unsubscribe();}
    };
  }, [messageId, initializeStream, addChunk, completeStream, handleStreamError, onStreamStart]);

  // Acknowledge chunk
  const acknowledgeChunk = useCallback(
    (chunkIndex: number) => {
      const service = serviceRef.current;
      if (service && streamState) {
        service.socket?.emit('stream_acknowledge', {
          streamId: streamState.streamId,
          messageId,
          chunkIndex,
        });
      }
    },
    [streamState, messageId]
  );

  // Retry stream
  const retryStream = useCallback(() => {
    if (retryCount < maxRetries && streamState) {
      setRetryCount(prev => prev + 1);

      // Reset stream state
      setStreamState(prev =>
        prev
          ? {
              ...prev,
              status: 'streaming',
              error: undefined,
              chunks: [],
              currentContent: '',
            }
          : null
      );

      // Request retry from server (implementation depends on server API)
      const service = serviceRef.current;
      if (service) {
        // This would need to be implemented on the server side
        service.socket?.emit('stream_retry', {
          messageId,
          streamId: streamState.streamId,
        });
      }
    }
  }, [retryCount, maxRetries, streamState, messageId]);

  // Cancel stream
  const cancelStream = useCallback(() => {
    if (streamState) {
      setStreamState(prev =>
        prev
          ? {
              ...prev,
              status: 'error',
              error: 'Stream cancelled by user',
              endTime: new Date().toISOString(),
            }
          : null
      );

      // Notify server about cancellation
      const service = serviceRef.current;
      if (service) {
        service.socket?.emit('stream_cancel', {
          messageId,
          streamId: streamState.streamId,
        });
      }
    }
  }, [streamState, messageId]);

  // Computed values
  const isStreaming = streamState?.status === 'streaming';
  const isComplete = streamState?.status === 'complete';
  const hasError = streamState?.status === 'error';
  const currentContent = streamState?.currentContent || '';
  const chunks = streamState?.chunks || [];
  const error = streamState?.error || null;
  const totalChunks = streamState?.totalChunks || null;
  const receivedChunks = chunks.length;

  // Calculate progress
  const progress = useMemo(() => {
    if (!streamState || !streamState.totalChunks) {
      return 0;
    }
    return Math.round((receivedChunks / streamState.totalChunks) * 100);
  }, [streamState, receivedChunks]);

  return {
    // Stream state
    streamState,
    isStreaming,
    isComplete,
    hasError,

    // Content
    currentContent,
    chunks,
    progress,

    // Controls
    acknowledgeChunk,
    retryStream,
    cancelStream,

    // Status
    error,
    totalChunks,
    receivedChunks,
  };
}

export default useMessageStream;
