import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

import { ContractTemplateService } from '../services/contract-template.service';

import type {
  ContractTemplateQueryParams,
  ContractTemplateCreateDto,
  ContractTemplateUpdateDto,
} from '../types/contract-template.types';

/**
 * Query keys for contract templates
 */
export const CONTRACT_TEMPLATE_QUERY_KEYS = {
  ALL: ['contract-templates'] as const,
  LIST: (params: ContractTemplateQueryParams) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'list', params] as const,
  DETAIL: (id: string) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'detail', id] as const,
  POPULAR: (limit: number) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'popular', limit] as const,
  RECENT: (limit: number) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'recent', limit] as const,
  CATEGORIES: () => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'categories'] as const,
  BY_CATEGORY: (category: string) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'category', category] as const,
  TAGS: () => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'tags'] as const,
  BY_TAG: (tag: string) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'tag', tag] as const,
  SEARCH: (query: string, filters?: any) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'search', query, filters] as const,
  STATS: (id: string) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'stats', id] as const,
  VERSIONS: (id: string) => [...CONTRACT_TEMPLATE_QUERY_KEYS.ALL, 'versions', id] as const,
};

/**
 * Hook to get contract templates list
 */
export const useContractTemplates = (params?: ContractTemplateQueryParams) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.LIST(params || {}),
    queryFn: () => ContractTemplateService.getTemplates(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to get contract template by ID
 */
export const useContractTemplate = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.DETAIL(id),
    queryFn: () => ContractTemplateService.getTemplate(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to get popular templates
 */
export const usePopularTemplates = (limit = 10) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.POPULAR(limit),
    queryFn: () => ContractTemplateService.getPopularTemplates(limit),
    staleTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook to get recent templates
 */
export const useRecentTemplates = (limit = 10) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.RECENT(limit),
    queryFn: () => ContractTemplateService.getRecentTemplates(limit),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to get template categories
 */
export const useTemplateCategories = () => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.CATEGORIES(),
    queryFn: () => ContractTemplateService.getTemplateCategories(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook to get templates by category
 */
export const useTemplatesByCategory = (category: string) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.BY_CATEGORY(category),
    queryFn: () => ContractTemplateService.getTemplatesByCategory(category),
    enabled: !!category,
    staleTime: 30 * 60 * 1000,
  });
};

/**
 * Hook to get template tags
 */
export const useTemplateTags = () => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.TAGS(),
    queryFn: () => ContractTemplateService.getTemplateTags(),
    staleTime: 60 * 60 * 1000, // 1 hour
  });
};

/**
 * Hook to get templates by tag
 */
export const useTemplatesByTag = (tag: string) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.BY_TAG(tag),
    queryFn: () => ContractTemplateService.getTemplatesByTag(tag),
    enabled: !!tag,
    staleTime: 30 * 60 * 1000,
  });
};

/**
 * Hook to search templates
 */
export const useSearchTemplates = (query: string, filters?: any) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.SEARCH(query, filters),
    queryFn: () => ContractTemplateService.searchTemplates(query, filters),
    enabled: !!query && query.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook to get template statistics
 */
export const useTemplateStats = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.STATS(id),
    queryFn: () => ContractTemplateService.getTemplateStats(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
  });
};

/**
 * Hook to get template versions
 */
export const useTemplateVersions = (id: string) => {
  return useQuery({
    queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.VERSIONS(id),
    queryFn: () => ContractTemplateService.getTemplateVersions(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook to create template
 */
export const useCreateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ContractTemplateCreateDto) => ContractTemplateService.createTemplate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to update template
 */
export const useUpdateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: ContractTemplateUpdateDto }) =>
      ContractTemplateService.updateTemplate(id, data),
    onSuccess: (updatedTemplate) => {
      queryClient.setQueryData(
        CONTRACT_TEMPLATE_QUERY_KEYS.DETAIL(updatedTemplate.id),
        updatedTemplate
      );
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to delete template
 */
export const useDeleteTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractTemplateService.deleteTemplate(id),
    onSuccess: (_, deletedId) => {
      queryClient.removeQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.DETAIL(deletedId) });
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to duplicate template
 */
export const useDuplicateTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractTemplateService.duplicateTemplate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to validate template
 */
export const useValidateTemplate = () => {
  return useMutation({
    mutationFn: ({ id, variables }: { id: string; variables: Record<string, any> }) =>
      ContractTemplateService.validateTemplate(id, variables),
  });
};

/**
 * Hook to generate content from template
 */
export const useGenerateContent = () => {
  return useMutation({
    mutationFn: ({ id, variables }: { id: string; variables: Record<string, any> }) =>
      ContractTemplateService.generateContent(id, variables),
  });
};

/**
 * Hook to preview template
 */
export const usePreviewTemplate = () => {
  return useMutation({
    mutationFn: ({ id, variables }: { id: string; variables: Record<string, any> }) =>
      ContractTemplateService.previewTemplate(id, variables),
  });
};

/**
 * Hook to archive template
 */
export const useArchiveTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractTemplateService.archiveTemplate(id),
    onSuccess: (updatedTemplate) => {
      queryClient.setQueryData(
        CONTRACT_TEMPLATE_QUERY_KEYS.DETAIL(updatedTemplate.id),
        updatedTemplate
      );
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to restore template
 */
export const useRestoreTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractTemplateService.restoreTemplate(id),
    onSuccess: (updatedTemplate) => {
      queryClient.setQueryData(
        CONTRACT_TEMPLATE_QUERY_KEYS.DETAIL(updatedTemplate.id),
        updatedTemplate
      );
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to publish template
 */
export const usePublishTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractTemplateService.publishTemplate(id),
    onSuccess: (updatedTemplate) => {
      queryClient.setQueryData(
        CONTRACT_TEMPLATE_QUERY_KEYS.DETAIL(updatedTemplate.id),
        updatedTemplate
      );
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook to unpublish template
 */
export const useUnpublishTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => ContractTemplateService.unpublishTemplate(id),
    onSuccess: (updatedTemplate) => {
      queryClient.setQueryData(
        CONTRACT_TEMPLATE_QUERY_KEYS.DETAIL(updatedTemplate.id),
        updatedTemplate
      );
      queryClient.invalidateQueries({ queryKey: CONTRACT_TEMPLATE_QUERY_KEYS.ALL });
    },
  });
};
