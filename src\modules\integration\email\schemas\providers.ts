import { z } from 'zod';

/**
 * Provider Validation Schemas
 */

/**
 * Provider Auth Method Schema
 */
export const providerAuthMethodSchema = z.enum(['password', 'oauth', 'apikey']);

/**
 * OAuth Tokens Schema
 */
export const oauthTokensSchema = z.object({
  accessToken: z.string().min(1, 'Access token is required'),
  refreshToken: z.string().min(1, 'Refresh token is required'),
  expiresAt: z.string().min(1, 'Expiration date is required'),
  tokenType: z.string().optional(),
  scope: z.string().optional(),
});

/**
 * Provider Credentials Schema
 */
export const providerCredentialsSchema = z.object({
  username: z.string().email('Invalid email format').optional(),
  password: z.string().min(1, 'Password is required').optional(),
  apiKey: z.string().min(1, 'API key is required').optional(),
  clientId: z.string().min(1, 'Client ID is required').optional(),
  clientSecret: z.string().min(1, 'Client secret is required').optional(),
});

/**
 * Provider Configuration Data Schema
 */
export const providerConfigurationDataSchema = z.object({
  providerId: z.string().min(1, 'Provider ID is required'),
  providerName: z.string().min(1, 'Provider name is required'),
  authMethod: providerAuthMethodSchema,
  credentials: providerCredentialsSchema,
  oauthTokens: oauthTokensSchema.optional(),
  customSettings: z.record(z.union([z.string(), z.number(), z.boolean()])).optional(),
});

/**
 * Email Server Configuration V2 Schema
 */
export const emailServerConfigurationV2Schema = z.object({
  serverName: z
    .string()
    .min(1, 'Server name is required')
    .max(100, 'Server name must be less than 100 characters'),
  host: z
    .string()
    .min(1, 'Host is required')
    .max(255, 'Host must be less than 255 characters'),
  port: z
    .number()
    .min(1, 'Port must be greater than 0')
    .max(65535, 'Port must be less than 65536'),
  username: z
    .string()
    .min(1, 'Username is required')
    .email('Username must be a valid email'),
  password: z.string().min(1, 'Password is required'),
  useSsl: z.boolean(),
  useStartTls: z.boolean(),
  isActive: z.boolean(),
  additionalSettings: z.record(z.union([z.string(), z.number(), z.boolean()])).optional(),
  
  // Provider-specific fields
  providerId: z.string().min(1, 'Provider ID is required').optional(),
  providerName: z.string().min(1, 'Provider name is required').optional(),
  authMethod: providerAuthMethodSchema.optional(),
  apiKey: z.string().optional(),
  oauthTokens: oauthTokensSchema.optional(),
  providerSettings: z.record(z.union([z.string(), z.number(), z.boolean()])).optional(),
});

/**
 * Provider-specific validation schemas
 */

/**
 * Gmail Configuration Schema
 */
export const gmailConfigurationSchema = emailServerConfigurationV2Schema.extend({
  providerId: z.literal('gmail'),
  username: z.string().email('Must be a valid Gmail address').refine(
    (email) => email.endsWith('@gmail.com'),
    'Must be a Gmail address (@gmail.com)'
  ),
  authMethod: z.literal('password'),
  appPassword: z.string().min(16, 'App password must be at least 16 characters'),
});

/**
 * Outlook Configuration Schema
 */
export const outlookConfigurationSchema = emailServerConfigurationV2Schema.extend({
  providerId: z.literal('outlook'),
  username: z.string().email('Must be a valid email address'),
  authMethod: z.enum(['password', 'oauth']),
});

/**
 * SendGrid Configuration Schema
 */
export const sendgridConfigurationSchema = emailServerConfigurationV2Schema.extend({
  providerId: z.literal('sendgrid'),
  authMethod: z.literal('apikey'),
  apiKey: z.string().refine(
    (key) => key.startsWith('SG.'),
    'SendGrid API key must start with "SG."'
  ),
  username: z.literal('apikey'),
});

/**
 * Mailgun Configuration Schema
 */
export const mailgunConfigurationSchema = emailServerConfigurationV2Schema.extend({
  providerId: z.literal('mailgun'),
  authMethod: z.enum(['apikey', 'password']),
  apiKey: z.string().refine(
    (key) => key.startsWith('key-'),
    'Mailgun API key must start with "key-"'
  ),
  domain: z.string().min(1, 'Mailgun domain is required'),
});

/**
 * Amazon SES Configuration Schema
 */
export const amazonSesConfigurationSchema = emailServerConfigurationV2Schema.extend({
  providerId: z.literal('amazon-ses'),
  authMethod: z.literal('password'),
  region: z.enum(['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1']),
  accessKeyId: z.string().min(1, 'AWS Access Key ID is required'),
  secretAccessKey: z.string().min(1, 'AWS Secret Access Key is required'),
});

/**
 * Provider Test Connection Schema
 */
export const providerTestConnectionSchema = z.object({
  providerId: z.string().min(1, 'Provider ID is required'),
  configuration: providerConfigurationDataSchema,
  testEmail: z.object({
    recipientEmail: z.string().email('Invalid recipient email'),
    subject: z.string().min(1, 'Subject is required').optional(),
    body: z.string().optional(),
  }),
});

/**
 * Dynamic schema selector based on provider ID
 */
export const getProviderSchema = (providerId: string) => {
  switch (providerId) {
    case 'gmail':
      return gmailConfigurationSchema;
    case 'outlook':
      return outlookConfigurationSchema;
    case 'sendgrid':
      return sendgridConfigurationSchema;
    case 'mailgun':
      return mailgunConfigurationSchema;
    case 'amazon-ses':
      return amazonSesConfigurationSchema;
    default:
      return emailServerConfigurationV2Schema;
  }
};

/**
 * Validate provider configuration with dynamic schema
 */
export const validateProviderConfiguration = (providerId: string, data: unknown) => {
  const schema = getProviderSchema(providerId);
  return schema.safeParse(data);
};

/**
 * Common validation helpers
 */
export const validationHelpers = {
  /**
   * Validate email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  /**
   * Validate port number
   */
  isValidPort: (port: number): boolean => {
    return port >= 1 && port <= 65535;
  },

  /**
   * Validate API key format for specific providers
   */
  isValidApiKey: (providerId: string, apiKey: string): boolean => {
    switch (providerId) {
      case 'sendgrid':
        return apiKey.startsWith('SG.');
      case 'mailgun':
        return apiKey.startsWith('key-');
      case 'mailchimp-transactional':
        return apiKey.startsWith('md-');
      default:
        return apiKey.length > 0;
    }
  },

  /**
   * Validate OAuth tokens
   */
  isValidOAuthTokens: (tokens: unknown): boolean => {
    const result = oauthTokensSchema.safeParse(tokens);
    return result.success;
  },

  /**
   * Get validation error messages
   */
  getErrorMessages: (error: z.ZodError): Record<string, string> => {
    const errors: Record<string, string> = {};
    for (const err of error.errors) {
      if (err.path.length > 0) {
        errors[err.path.join('.')] = err.message;
      }
    }
    return errors;
  },
};

/**
 * Provider-specific validation rules
 */
export const providerValidationRules = {
  gmail: {
    requiresAppPassword: true,
    supportedAuthMethods: ['password', 'oauth'],
    emailDomain: '@gmail.com',
    defaultPort: 587,
    requiresSSL: false,
    requiresStartTLS: true,
  },
  outlook: {
    requiresAppPassword: false,
    supportedAuthMethods: ['password', 'oauth'],
    emailDomains: ['@outlook.com', '@hotmail.com', '@live.com'],
    defaultPort: 587,
    requiresSSL: false,
    requiresStartTLS: true,
  },
  sendgrid: {
    requiresApiKey: true,
    supportedAuthMethods: ['apikey'],
    apiKeyPrefix: 'SG.',
    defaultPort: 587,
    requiresSSL: false,
    requiresStartTLS: true,
  },
  mailgun: {
    requiresApiKey: true,
    requiresDomain: true,
    supportedAuthMethods: ['apikey', 'password'],
    apiKeyPrefix: 'key-',
    defaultPort: 587,
    requiresSSL: false,
    requiresStartTLS: true,
  },
  'amazon-ses': {
    requiresAccessKey: true,
    requiresSecretKey: true,
    requiresRegion: true,
    supportedAuthMethods: ['password'],
    supportedRegions: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1'],
    defaultPort: 587,
    requiresSSL: false,
    requiresStartTLS: true,
  },
};
