import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { ModuleCard } from '@/modules/components/card';
import { Card, Typography, ResponsiveGrid } from '@/shared/components/common';

import { useContractStats } from '../hooks/useContracts';

/**
 * Contracts Home Page
 * Trang chủ module quản lý hợp đồng
 */
const ContractsHomePage: React.FC = () => {
  const { t } = useTranslation(['common', 'contracts']);
  const navigate = useNavigate();
  const { data: stats, isLoading: statsLoading } = useContractStats();

  const modules = [
    {
      title: t('contracts:all_contracts'),
      description: t('contracts:all_contracts_desc'),
      icon: 'file-text',
      color: 'blue' as const,
      linkTo: '/contracts/list',
      stats: stats?.total || 0,
    },
    {
      title: t('contracts:create_contract'),
      description: t('contracts:create_contract_desc'),
      icon: 'plus-circle',
      color: 'green' as const,
      linkTo: '/contracts/create',
    },
    {
      title: t('contracts:templates'),
      description: t('contracts:templates_desc'),
      icon: 'layout-template',
      color: 'purple' as const,
      linkTo: '/contracts/templates',
    },
    {
      title: t('contracts:pending_approvals'),
      description: t('contracts:pending_approvals_desc'),
      icon: 'check-circle',
      color: 'orange' as const,
      linkTo: '/contracts/approvals',
      stats: stats?.pendingApprovals || 0,
    },
    {
      title: t('contracts:pending_signatures'),
      description: t('contracts:pending_signatures_desc'),
      icon: 'pen-tool',
      color: 'red' as const,
      linkTo: '/contracts/signatures',
      stats: stats?.pendingSignatures || 0,
    },
    {
      title: t('contracts:reports'),
      description: t('contracts:reports_desc'),
      icon: 'bar-chart-3',
      color: 'indigo' as const,
      linkTo: '/contracts/reports',
    },
  ];

  const quickStats = [
    {
      title: t('contracts:total_contracts'),
      value: stats?.total || 0,
      icon: 'file-text',
      color: 'blue',
    },
    {
      title: t('contracts:active_contracts'),
      value: stats?.byStatus?.active || 0,
      icon: 'check-circle',
      color: 'green',
    },
    {
      title: t('contracts:expiring_soon'),
      value: stats?.expiringThisMonth || 0,
      icon: 'clock',
      color: 'orange',
    },
    {
      title: t('contracts:total_value'),
      value: stats?.totalValue ? `$${stats.totalValue.toLocaleString()}` : '$0',
      icon: 'dollar-sign',
      color: 'purple',
    },
  ];

  return (
    <div className="w-full bg-background text-foreground">
      {/* Header */}
      <div className="mb-6">
        <Typography variant="h1" className="mb-2">
          {t('contracts:module_title')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('contracts:module_description')}
        </Typography>
      </div>

      {/* Quick Stats */}
      <div className="mb-8">
        <Typography variant="h2" className="mb-4">
          {t('contracts:quick_stats')}
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }}>
          {quickStats.map((stat, index) => (
            <Card key={index} className="p-4">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg bg-${stat.color}-100 text-${stat.color}-600`}>
                  <i className={`lucide-${stat.icon} w-6 h-6`} />
                </div>
                <div>
                  <Typography variant="h3" className="text-2xl font-bold">
                    {statsLoading ? '...' : stat.value}
                  </Typography>
                  <Typography variant="body2" className="text-muted-foreground">
                    {stat.title}
                  </Typography>
                </div>
              </div>
            </Card>
          ))}
        </ResponsiveGrid>
      </div>

      {/* Module Navigation */}
      <div className="mb-8">
        <Typography variant="h2" className="mb-4">
          {t('contracts:quick_actions')}
        </Typography>
        <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 3 }}>
          {modules.map((module, index) => (
            <ModuleCard
              key={index}
              title={module.title}
              description={module.description}
              icon={module.icon}
              color={module.color}
              linkTo={module.linkTo}
              stats={module.stats}
            />
          ))}
        </ResponsiveGrid>
      </div>

      {/* Recent Activity */}
      <div className="mb-8">
        <Typography variant="h2" className="mb-4">
          {t('contracts:recent_activity')}
        </Typography>
        <Card className="p-6">
          <div className="text-center py-8">
            <Typography variant="body1" className="text-muted-foreground">
              {t('contracts:recent_activity_placeholder')}
            </Typography>
          </div>
        </Card>
      </div>

      {/* Contract Status Overview */}
      {stats && (
        <div className="mb-8">
          <Typography variant="h2" className="mb-4">
            {t('contracts:status_overview')}
          </Typography>
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }}>
            {Object.entries(stats.byStatus).map(([status, count]) => (
              <Card key={status} className="p-4">
                <div className="text-center">
                  <Typography variant="h3" className="text-xl font-bold mb-2">
                    {count}
                  </Typography>
                  <Typography variant="body2" className="text-muted-foreground capitalize">
                    {t(`contracts:status_${status}`)}
                  </Typography>
                </div>
              </Card>
            ))}
          </ResponsiveGrid>
        </div>
      )}

      {/* Contract Type Overview */}
      {stats && (
        <div>
          <Typography variant="h2" className="mb-4">
            {t('contracts:type_overview')}
          </Typography>
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 4 }}>
            {Object.entries(stats.byType).map(([type, count]) => (
              <Card key={type} className="p-4">
                <div className="text-center">
                  <Typography variant="h3" className="text-xl font-bold mb-2">
                    {count}
                  </Typography>
                  <Typography variant="body2" className="text-muted-foreground capitalize">
                    {t(`contracts:type_${type}`)}
                  </Typography>
                </div>
              </Card>
            ))}
          </ResponsiveGrid>
        </div>
      )}
    </div>
  );
};

export default ContractsHomePage;
