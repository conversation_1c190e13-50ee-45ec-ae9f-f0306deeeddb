import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { Card, Typography, Button, Icon } from '@/shared/components/common';

import { EmailProvider, ProviderConfigurationData } from '../../types/providers';
import { CreateEmailServerV2Dto } from '../../types/providers';

import ProviderConfigurationStep from './ProviderConfigurationStep';
import ProviderSelectionStep from './ProviderSelectionStep';

interface EmailServerWizardProps {
  onComplete: (data: CreateEmailServerV2Dto) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

type WizardStep = 'provider-selection' | 'configuration' | 'review';

interface WizardState {
  currentStep: WizardStep;
  selectedProvider?: EmailProvider;
  configurationData?: ProviderConfigurationData;
}

/**
 * EmailServerWizard Component
 * Multi-step wizard để tạo email server configuration
 */
const EmailServerWizard: React.FC<EmailServerWizardProps> = ({
  onComplete,
  onCancel,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['integration', 'common']);
  
  const [wizardState, setWizardState] = useState<WizardState>({
    currentStep: 'provider-selection',
  });

  // Wizard steps configuration
  const steps = [
    {
      id: 'provider-selection',
      title: t('integration:wizard.steps.selectProvider'),
      description: t('integration:wizard.steps.selectProviderDesc'),
      icon: 'grid',
    },
    {
      id: 'configuration',
      title: t('integration:wizard.steps.configure'),
      description: t('integration:wizard.steps.configureDesc'),
      icon: 'settings',
    },
    {
      id: 'review',
      title: t('integration:wizard.steps.review'),
      description: t('integration:wizard.steps.reviewDesc'),
      icon: 'check-circle',
    },
  ];

  const currentStepIndex = steps.findIndex(step => step.id === wizardState.currentStep);

  // Step handlers
  const handleProviderSelect = useCallback((provider: EmailProvider) => {
    setWizardState(prev => ({
      ...prev,
      selectedProvider: provider,
    }));
  }, []);

  const handleProviderSelectionNext = useCallback(() => {
    if (wizardState.selectedProvider) {
      setWizardState(prev => ({
        ...prev,
        currentStep: 'configuration',
      }));
    }
  }, [wizardState.selectedProvider]);

  const handleConfigurationChange = useCallback((data: ProviderConfigurationData) => {
    setWizardState(prev => ({
      ...prev,
      configurationData: data,
    }));
  }, []);

  const handleConfigurationNext = useCallback(() => {
    if (wizardState.configurationData) {
      setWizardState(prev => ({
        ...prev,
        currentStep: 'review',
      }));
    }
  }, [wizardState.configurationData]);

  const handleBack = useCallback(() => {
    switch (wizardState.currentStep) {
      case 'configuration':
        setWizardState(prev => ({
          ...prev,
          currentStep: 'provider-selection',
        }));
        break;
      case 'review':
        setWizardState(prev => ({
          ...prev,
          currentStep: 'configuration',
        }));
        break;
    }
  }, [wizardState.currentStep]);

  const handleComplete = useCallback(async () => {
    if (!wizardState.selectedProvider || !wizardState.configurationData) {
      return;
    }

    const { selectedProvider, configurationData } = wizardState;

    // Convert wizard data to CreateEmailServerV2Dto
    const emailServerData: CreateEmailServerV2Dto = {
      serverName: `${selectedProvider.displayName} Server`,
      host: selectedProvider.defaultConfig.host,
      port: selectedProvider.defaultConfig.port,
      username: configurationData.credentials.username || '',
      password: configurationData.credentials.password || '',
      useSsl: selectedProvider.defaultConfig.useSsl,
      useStartTls: selectedProvider.defaultConfig.useStartTls,
      isActive: true,
      
      // Provider-specific fields
      providerId: selectedProvider.id,
      providerName: selectedProvider.displayName,
      authMethod: configurationData.authMethod,
      apiKey: configurationData.credentials.apiKey,
      oauthTokens: configurationData.oauthTokens,
      providerSettings: configurationData.customSettings,
    };

    await onComplete(emailServerData);
  }, [wizardState, onComplete]);

  const handleTestConnection = useCallback(async (): Promise<boolean> => {
    // Mock test connection - replace with actual API call
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(Math.random() > 0.3); // 70% success rate for demo
      }, 2000);
    });
  }, []);

  // Render current step
  const renderCurrentStep = () => {
    switch (wizardState.currentStep) {
      case 'provider-selection':
        return (
          <ProviderSelectionStep
            selectedProvider={wizardState.selectedProvider}
            onProviderSelect={handleProviderSelect}
            onNext={handleProviderSelectionNext}
          />
        );

      case 'configuration':
        if (!wizardState.selectedProvider) {
          return null;
        }
        return (
          <ProviderConfigurationStep
            provider={wizardState.selectedProvider}
            initialData={wizardState.configurationData}
            onConfigurationChange={handleConfigurationChange}
            onNext={handleConfigurationNext}
            onBack={handleBack}
            onTestConnection={handleTestConnection}
            isSubmitting={isSubmitting}
          />
        );

      case 'review':
        return (
          <ReviewStep
            provider={wizardState.selectedProvider!}
            configuration={wizardState.configurationData!}
            onComplete={handleComplete}
            onBack={handleBack}
            isSubmitting={isSubmitting}
          />
        );

      default:
        return null;
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Wizard Header */}
      <Card className="mb-6">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <Typography variant="h3" className="mb-2">
                {t('integration:wizard.title')}
              </Typography>
              <Typography variant="body1" className="text-muted-foreground">
                {t('integration:wizard.description')}
              </Typography>
            </div>
            <Button variant="outline" onClick={onCancel}>
              <Icon name="x" className="w-4 h-4 mr-2" />
              {t('common:cancel')}
            </Button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const isActive = index === currentStepIndex;
              const isCompleted = index < currentStepIndex;

              return (
                <div key={step.id} className="flex items-center flex-1">
                  <div className="flex flex-col items-center">
                    <div className={`
                      flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                      ${isCompleted ? 'bg-success border-success text-white' :
                        isActive ? 'bg-primary border-primary text-white' :
                        'bg-background border-border text-muted-foreground'}
                    `}>
                      {isCompleted ? (
                        <Icon name="check" className="w-5 h-5" />
                      ) : (
                        <Icon name={step.icon} className="w-5 h-5" />
                      )}
                    </div>
                    <div className="mt-2 text-center">
                      <Typography variant="body2" className={`font-medium ${
                        isActive ? 'text-primary' :
                        isCompleted ? 'text-success' : 'text-muted-foreground'
                      }`}>
                        {step.title}
                      </Typography>
                      <Typography variant="caption" className="text-muted-foreground">
                        {step.description}
                      </Typography>
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-0.5 mx-4 ${
                      isCompleted ? 'bg-success' : 'bg-border'
                    }`} />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </Card>

      {/* Current Step Content */}
      <div className="min-h-[600px]">
        {renderCurrentStep()}
      </div>
    </div>
  );
};

/**
 * Review Step Component
 */
interface ReviewStepProps {
  provider: EmailProvider;
  configuration: ProviderConfigurationData;
  onComplete: () => void;
  onBack: () => void;
  isSubmitting: boolean;
}

const ReviewStep: React.FC<ReviewStepProps> = ({
  provider,
  configuration,
  onComplete,
  onBack,
  isSubmitting,
}) => {
  const { t } = useTranslation(['integration', 'common']);

  return (
    <div className="w-full">
      <div className="mb-6">
        <Typography variant="h4" className="mb-2">
          {t('integration:wizard.review.title')}
        </Typography>
        <Typography variant="body1" className="text-muted-foreground">
          {t('integration:wizard.review.description')}
        </Typography>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Provider Information */}
        <Card>
          <div className="p-6">
            <Typography variant="h6" className="mb-4">
              {t('integration:wizard.review.providerInfo')}
            </Typography>
            
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 flex items-center justify-center bg-gray-100 rounded-lg">
                {provider.logoUrl ? (
                  <img
                    src={provider.logoUrl}
                    alt={provider.displayName}
                    className="w-8 h-8 object-contain"
                  />
                ) : (
                  <Icon name="mail" className="w-6 h-6 text-gray-500" />
                )}
              </div>
              <div>
                <Typography variant="body1" className="font-medium">
                  {provider.displayName}
                </Typography>
                <Typography variant="caption" className="text-muted-foreground">
                  {provider.description}
                </Typography>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:wizard.review.category')}:
                </Typography>
                <Typography variant="caption">
                  {provider.category}
                </Typography>
              </div>
              <div className="flex justify-between">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:wizard.review.authMethod')}:
                </Typography>
                <Typography variant="caption">
                  {configuration.authMethod}
                </Typography>
              </div>
            </div>
          </div>
        </Card>

        {/* Configuration Summary */}
        <Card>
          <div className="p-6">
            <Typography variant="h6" className="mb-4">
              {t('integration:wizard.review.configuration')}
            </Typography>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:wizard.review.host')}:
                </Typography>
                <Typography variant="caption" className="font-mono">
                  {provider.defaultConfig.host}
                </Typography>
              </div>
              <div className="flex justify-between">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:wizard.review.port')}:
                </Typography>
                <Typography variant="caption" className="font-mono">
                  {provider.defaultConfig.port}
                </Typography>
              </div>
              <div className="flex justify-between">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:wizard.review.username')}:
                </Typography>
                <Typography variant="caption" className="font-mono">
                  {configuration.credentials.username}
                </Typography>
              </div>
              <div className="flex justify-between">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:wizard.review.ssl')}:
                </Typography>
                <Typography variant="caption">
                  {provider.defaultConfig.useSsl ? t('common:yes') : t('common:no')}
                </Typography>
              </div>
              <div className="flex justify-between">
                <Typography variant="caption" className="text-muted-foreground">
                  {t('integration:wizard.review.startTls')}:
                </Typography>
                <Typography variant="caption">
                  {provider.defaultConfig.useStartTls ? t('common:yes') : t('common:no')}
                </Typography>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button variant="outline" onClick={onBack}>
          <Icon name="arrow-left" className="w-4 h-4 mr-2" />
          {t('common:back')}
        </Button>
        
        <Button
          variant="primary"
          onClick={onComplete}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <Icon name="loader" className="w-4 h-4 mr-2 animate-spin" />
          ) : (
            <Icon name="check" className="w-4 h-4 mr-2" />
          )}
          {t('integration:wizard.review.complete')}
        </Button>
      </div>
    </div>
  );
};

export default EmailServerWizard;
