import { useState, useEffect } from 'react';

/**
 * Hook xử lý responsive design cho Calendar
 * Tự động điều chỉnh layout và behavior dựa trên screen size
 */
export interface UseCalendarResponsiveOptions {
  breakpoints?: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
}

export interface UseCalendarResponsiveReturn {
  // Screen size detection
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  
  // Calendar sizing
  calendarSize: 'sm' | 'md' | 'lg';
  cellSize: number;
  fontSize: 'sm' | 'md' | 'lg';
  
  // Touch optimization
  touchOptimized: boolean;
  minTouchTarget: number;
  
  // Layout adjustments
  showWeekNumbers: boolean;
  compactMode: boolean;
  
  // Interaction modes
  preferTouch: boolean;
  enableSwipe: boolean;
}

const DEFAULT_BREAKPOINTS = {
  mobile: 768,
  tablet: 1024,
  desktop: 1200,
};

export const useCalendarResponsive = (
  options: UseCalendarResponsiveOptions = {}
): UseCalendarResponsiveReturn => {
  const { breakpoints = DEFAULT_BREAKPOINTS } = options;

  const [screenWidth, setScreenWidth] = useState(() => {
    if (typeof window !== 'undefined') {
      return window.innerWidth;
    }
    return 1024; // Default fallback
  });

  // Update screen width on resize
  useEffect(() => {
    if (typeof window === 'undefined') {return;}

    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };

    window.addEventListener('resize', handleResize);
    
    // Initial measurement
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Screen size detection
  const isMobile = screenWidth < breakpoints.mobile;
  const isTablet = screenWidth >= breakpoints.mobile && screenWidth < breakpoints.desktop;
  const isDesktop = screenWidth >= breakpoints.desktop;

  // Calendar sizing based on screen size
  const calendarSize = isMobile ? 'sm' : isTablet ? 'md' : 'lg';
  
  const cellSize = isMobile ? 32 : isTablet ? 36 : 40;
  
  const fontSize = isMobile ? 'sm' : isTablet ? 'md' : 'lg';

  // Touch optimization
  const touchOptimized = isMobile || isTablet;
  const minTouchTarget = touchOptimized ? 44 : 32; // 44px minimum for touch targets

  // Layout adjustments
  const showWeekNumbers = !isMobile; // Hide week numbers on mobile to save space
  const compactMode = isMobile;

  // Interaction modes
  const preferTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const enableSwipe = touchOptimized;

  return {
    // Screen size detection
    isMobile,
    isTablet,
    isDesktop,
    screenWidth,
    
    // Calendar sizing
    calendarSize,
    cellSize,
    fontSize,
    
    // Touch optimization
    touchOptimized,
    minTouchTarget,
    
    // Layout adjustments
    showWeekNumbers,
    compactMode,
    
    // Interaction modes
    preferTouch,
    enableSwipe,
  };
};

export default useCalendarResponsive;
