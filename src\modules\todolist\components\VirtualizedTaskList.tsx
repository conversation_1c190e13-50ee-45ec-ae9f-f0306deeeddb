import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { Chip, IconCard, Tooltip, Loading, Typography } from '@/shared/components/common';
import { Checkbox } from '@/shared/components/common';
import { NotificationUtil } from '@/shared/utils/notification';

import { useDeleteTask, useUpdateTaskStatus } from '../hooks/useTasks';
import { useVirtualizedList } from '../hooks/useVirtualizedList';
import { TaskDto, TaskStatus, TaskPriority } from '../types/task.types';

interface VirtualizedTaskListProps {
  tasks: TaskDto[];
  isLoading: boolean;
  onRefresh: () => void;
  itemHeight?: number;
  maxHeight?: number;
  selectedTasks?: string[];
  onSelectionChange?: (selectedTasks: string[]) => void;
  multiSelect?: boolean;
}

/**
 * Virtualized task list component for improved performance with large datasets
 */
const VirtualizedTaskList: React.FC<VirtualizedTaskListProps> = ({
  tasks,
  isLoading,
  onRefresh,
  itemHeight = 80,
  maxHeight = 600,
  selectedTasks = [],
  onSelectionChange,
  multiSelect = false,
}) => {
  const { t } = useTranslation(['common', 'todolist']);
  const navigate = useNavigate();
  const { mutateAsync: deleteTask } = useDeleteTask();
  const { mutateAsync: updateTaskStatus } = useUpdateTaskStatus();

  // Handle task selection
  const handleTaskSelection = useCallback((taskId: string, isSelected: boolean) => {
    if (!onSelectionChange) return;

    if (isSelected) {
      onSelectionChange([...selectedTasks, taskId]);
    } else {
      onSelectionChange(selectedTasks.filter(id => id !== taskId));
    }
  }, [selectedTasks, onSelectionChange]);

  // Handle select all
  const handleSelectAll = useCallback((isSelected: boolean) => {
    if (!onSelectionChange) return;

    if (isSelected) {
      onSelectionChange(tasks.map(task => task.id.toString()));
    } else {
      onSelectionChange([]);
    }
  }, [tasks, onSelectionChange]);

  // Set up virtualized list
  const { virtualItems, totalHeight, containerRef, isScrolling } = useVirtualizedList({
    items: tasks,
    itemHeight,
  });

  // Get status chip variant
  const getStatusChipVariant = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return 'default';
      case TaskStatus.IN_PROGRESS:
        return 'primary';
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return 'success';
      case TaskStatus.REJECTED:
        return 'danger';
      default:
        return 'default';
    }
  };

  // Get priority chip variant
  const getPriorityChipVariant = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return 'success';
      case TaskPriority.MEDIUM:
        return 'warning';
      case TaskPriority.HIGH:
        return 'warning';
      case TaskPriority.URGENT:
        return 'danger';
      default:
        return 'default';
    }
  };

  // Get status text
  const getStatusText = (status: TaskStatus | null) => {
    switch (status) {
      case TaskStatus.PENDING:
        return t('todolist:task.status.todo', 'To Do');
      case TaskStatus.IN_PROGRESS:
        return t('todolist:task.status.inProgress', 'In Progress');
      case TaskStatus.COMPLETED:
      case TaskStatus.APPROVED:
        return t('todolist:task.status.done', 'Done');
      case TaskStatus.REJECTED:
        return t('todolist:task.status.cancelled', 'Cancelled');
      default:
        return '';
    }
  };

  // Get priority text
  const getPriorityText = (priority: TaskPriority | null) => {
    switch (priority) {
      case TaskPriority.LOW:
        return t('todolist:task.priority.low', 'Low');
      case TaskPriority.MEDIUM:
        return t('todolist:task.priority.medium', 'Medium');
      case TaskPriority.HIGH:
        return t('todolist:task.priority.high', 'High');
      case TaskPriority.URGENT:
        return t('todolist:task.priority.urgent', 'Urgent');
      default:
        return '';
    }
  };

  // Handle delete task
  const handleDeleteTask = async (id: number, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteTask(id);
      NotificationUtil.success({
        message: t('todolist:task.notifications.deleteSuccess', 'Task deleted successfully'),
      });
      onRefresh();
    } catch (error) {
      console.error('Error deleting task:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.deleteError', 'Error deleting task'),
      });
    }
  };

  // Handle status change
  const handleStatusChange = async (id: number, status: TaskStatus, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await updateTaskStatus({ id, data: { status } });
      NotificationUtil.success({
        message: t(
          'todolist:task.notifications.statusUpdateSuccess',
          'Task status updated successfully'
        ),
      });
      onRefresh();
    } catch (error) {
      console.error('Error updating task status:', error);
      NotificationUtil.error({
        message: t('todolist:task.notifications.statusUpdateError', 'Error updating task status'),
      });
    }
  };

  // Handle task click
  const handleTaskClick = (id: number) => {
    navigate(`/todolist/tasks/${id}`);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-32">
        <Loading size="md" />
      </div>
    );
  }

  if (tasks.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        {t('todolist:task.empty', 'No tasks found')}
      </div>
    );
  }

  const isAllSelected = tasks.length > 0 && selectedTasks.length === tasks.length;
  const isIndeterminate = selectedTasks.length > 0 && selectedTasks.length < tasks.length;

  return (
    <div className="space-y-2">
      {/* Header with select all checkbox */}
      {multiSelect && tasks.length > 0 && (
        <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={isAllSelected}
              indeterminate={isIndeterminate}
              onChange={handleSelectAll}
            />
            <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
              {selectedTasks.length > 0
                ? t('common:selected', `${selectedTasks.length} selected`)
                : t('common:selectAll', 'Select all')}
            </Typography>
          </div>
        </div>
      )}

      {/* Virtualized task list */}
      <div
        ref={containerRef}
        className="overflow-auto"
        style={{ height: Math.min(totalHeight, maxHeight), maxHeight }}
      >
        <div style={{ height: totalHeight, position: 'relative' }}>
          {virtualItems.map(({ item: task, offsetTop }) => {
            const isSelected = selectedTasks.includes(task.id.toString());

            return (
              <div
                key={task.id}
                className={`
                  absolute w-full border border-gray-200 dark:border-gray-700 rounded-lg mb-2
                  transition-all duration-200 hover:shadow-md
                  ${isSelected ? 'ring-2 ring-primary ring-offset-1 bg-primary/5' : 'hover:bg-gray-50 dark:hover:bg-gray-800'}
                `}
                style={{
                  height: itemHeight,
                  top: offsetTop,
                  transform: isScrolling ? 'translateZ(0)' : undefined,
                }}
              >
                <div className="p-3 h-full flex items-center space-x-3">
                  {/* Selection checkbox */}
                  {multiSelect && (
                    <div onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        checked={isSelected}
                        onChange={(checked) => handleTaskSelection(task.id.toString(), checked)}
                      />
                    </div>
                  )}

                  {/* Task content */}
                  <div
                    className="flex-1 min-w-0 cursor-pointer"
                    onClick={() => handleTaskClick(task.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <Typography variant="subtitle2" className="font-medium truncate">
                          {task.title}
                        </Typography>
                        {task.description && (
                          <Typography variant="caption" className="text-gray-500 truncate mt-1">
                            {task.description}
                          </Typography>
                        )}
                      </div>

                      {/* Status and Priority chips */}
                      <div className="flex items-center space-x-2 ml-3">
                        <Chip
                          variant={getStatusChipVariant(task.status) as any}
                          size="sm"
                        >
                          {getStatusText(task.status)}
                        </Chip>

                        {task.priority && (
                          <Chip
                            variant={getPriorityChipVariant(task.priority) as any}
                            size="sm"
                          >
                            {getPriorityText(task.priority)}
                          </Chip>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="flex items-center space-x-1">
                    <Tooltip content={t('todolist:task.status.todo', 'To Do')}>
                      <IconCard
                        icon="clock"
                        size="sm"
                        variant={task.status === TaskStatus.PENDING ? 'primary' : 'default'}
                        onClick={() =>
                          handleStatusChange(task.id, TaskStatus.PENDING, {} as React.MouseEvent)
                        }
                      />
                    </Tooltip>
                    <Tooltip content={t('todolist:task.status.inProgress', 'In Progress')}>
                      <IconCard
                        icon="loading"
                        size="sm"
                        variant={task.status === TaskStatus.IN_PROGRESS ? 'primary' : 'default'}
                        onClick={() =>
                          handleStatusChange(task.id, TaskStatus.IN_PROGRESS, {} as React.MouseEvent)
                        }
                      />
                    </Tooltip>
                    <Tooltip content={t('todolist:task.status.done', 'Done')}>
                      <IconCard
                        icon="check-circle"
                        size="sm"
                        variant={task.status === TaskStatus.COMPLETED ? 'primary' : 'default'}
                        onClick={() =>
                          handleStatusChange(task.id, TaskStatus.COMPLETED, {} as React.MouseEvent)
                        }
                      />
                    </Tooltip>
                    <Tooltip content={t('common:delete', 'Delete')}>
                      <IconCard
                        icon="trash"
                        size="sm"
                        variant="default"
                        className="text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20"
                        onClick={() => handleDeleteTask(task.id, {} as React.MouseEvent)}
                      />
                    </Tooltip>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default VirtualizedTaskList;
