import React, { useEffect, useState, useRef } from 'react';

import { useAuth } from '@/modules/auth/hooks/useAuth';
import { Loading } from '@/shared/components/common';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * Component để khôi phục trạng thái authentication khi ứng dụng khởi động
 * Sử dụng useAuth hook để kiểm tra và khôi phục token
 */
const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { isAuthenticated, isTokenValid, getToken, clearAuth } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const hasInitialized = useRef(false);

  useEffect(() => {
    // Chỉ khởi tạo một lần để tránh vòng lặp vô hạn
    if (hasInitialized.current) return;

    const initializeAuth = async () => {
      try {
        // Sử dụng useAuth để kiểm tra token hiện tại
        const currentToken = getToken();

        if (currentToken) {
          // Kiểm tra token có hợp lệ không
          if (!isTokenValid()) {
            // Nếu token hết hạn, xóa auth
            clearAuth();
          }
          // Nếu token hợp lệ, không cần làm gì thêm
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Nếu có lỗi, xóa auth
        clearAuth();
      } finally {
        setIsLoading(false);
        hasInitialized.current = true;
      }
    };

    initializeAuth();
  }, []); // Chỉ chạy một lần khi component mount

  // Hiển thị loading trong khi kiểm tra auth state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loading />
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
