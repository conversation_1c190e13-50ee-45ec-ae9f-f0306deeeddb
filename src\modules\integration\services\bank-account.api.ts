import { apiClient } from '@/shared/api';
import { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';

import {
  BankAccountDto,
  BankAccountQueryDto,
  CreateBankAccountDto,
  UpdateBankAccountDto,
  OTPVerificationDto,
  CreateVirtualAccountDto,
  BankAccountStatsDto,
} from '../types/bank-account.types';

/**
 * Bank Account API Services
 * Các service để gọi API liên quan đến tài khoản ngân hàng
 */

const BASE_URL = '/integration/bank-accounts';

/**
 * L<PERSON>y danh sách tài khoản ngân hàng
 */
export const getBankAccounts = async (
  params: BankAccountQueryDto = {}
): Promise<ApiResponseDto<PaginatedResult<BankAccountDto>>> => {
  return await apiClient.get<PaginatedResult<BankAccountDto>>(BASE_URL, {
    params,
  });
};

/**
 * L<PERSON>y chi tiết tài khoản ngân hàng
 */
export const getBankAccountDetail = async (
  id: string
): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.get<BankAccountDto>(`${BASE_URL}/${id}`);
};

/**
 * Tạo tài khoản ngân hàng mới
 */
export const createBankAccount = async (
  data: CreateBankAccountDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.post<BankAccountDto>(BASE_URL, data);
};

/**
 * Cập nhật tài khoản ngân hàng
 */
export const updateBankAccount = async (
  id: string,
  data: UpdateBankAccountDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.put<BankAccountDto>(`${BASE_URL}/${id}`, data);
};

/**
 * Xóa tài khoản ngân hàng
 */
export const deleteBankAccount = async (id: string): Promise<ApiResponseDto<void>> => {
  return await apiClient.delete<void>(`${BASE_URL}/${id}`);
};

/**
 * Xóa nhiều tài khoản ngân hàng
 */
export const deleteManyBankAccounts = async (ids: string[]): Promise<ApiResponseDto<void>> => {
  return await apiClient.delete<void>(`${BASE_URL}/bulk`, {
    data: { ids },
  });
};

/**
 * Gửi OTP để xác thực tài khoản ngân hàng
 */
export const sendOTP = async (bankAccountId: string): Promise<ApiResponseDto<{ message: string }>> => {
  return await apiClient.post<{ message: string }>(`${BASE_URL}/${bankAccountId}/send-otp`);
};

/**
 * Xác thực OTP
 */
export const verifyOTP = async (
  data: OTPVerificationDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.post<BankAccountDto>(`${BASE_URL}/verify-otp`, data);
};

/**
 * Tạo tài khoản ảo (Virtual Account) - chỉ dành cho OCB
 */
export const createVirtualAccount = async (
  data: CreateVirtualAccountDto
): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.post<BankAccountDto>(`${BASE_URL}/create-virtual-account`, data);
};

/**
 * Lấy thống kê tài khoản ngân hàng
 */
export const getBankAccountStats = async (): Promise<ApiResponseDto<BankAccountStatsDto>> => {
  return await apiClient.get<BankAccountStatsDto>(`${BASE_URL}/stats`);
};

/**
 * Kích hoạt tài khoản ngân hàng
 */
export const activateBankAccount = async (id: string): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.post<BankAccountDto>(`${BASE_URL}/${id}/activate`);
};

/**
 * Vô hiệu hóa tài khoản ngân hàng
 */
export const deactivateBankAccount = async (id: string): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.post<BankAccountDto>(`${BASE_URL}/${id}/deactivate`);
};

/**
 * Kiểm tra trạng thái kết nối tài khoản ngân hàng
 */
export const checkBankAccountConnection = async (
  id: string
): Promise<ApiResponseDto<{ isConnected: boolean; lastChecked: string }>> => {
  return await apiClient.get<{ isConnected: boolean; lastChecked: string }>(
    `${BASE_URL}/${id}/check-connection`
  );
};

/**
 * Đồng bộ thông tin tài khoản ngân hàng
 */
export const syncBankAccount = async (id: string): Promise<ApiResponseDto<BankAccountDto>> => {
  return await apiClient.post<BankAccountDto>(`${BASE_URL}/${id}/sync`);
};
