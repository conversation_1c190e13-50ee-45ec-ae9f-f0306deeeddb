import { ComponentType } from 'react';

import { ProtectedRoute } from '@/shared/components/auth';

/**
 * HOC (Higher-Order Component) để bảo vệ component với authentication
 * @param WrappedComponent - Component cầ<PERSON> đư<PERSON><PERSON> bảo vệ
 * @param redirectTo - Đường dẫn redirect khi chưa đăng nhập (mặc định: '/auth/login')
 * @returns Component đã được wrap với ProtectedRoute
 */
export const withAuth = <P extends object>(
  WrappedComponent: ComponentType<P>,
  redirectTo: string = '/auth'
) => {
  const WithAuthComponent = (props: P) => {
    return (
      <ProtectedRoute redirectTo={redirectTo}>
        <WrappedComponent {...props} />
      </ProtectedRoute>
    );
  };

  // Đặt displayName để dễ debug
  WithAuthComponent.displayName = `withAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithAuthComponent;
};

/**
 * HOC để bảo vệ page component với authentication
 * Tương tự withAuth nhưng được tối ưu cho page components
 */
export const withAuthPage = <P extends object>(
  PageComponent: ComponentType<P>,
  redirectTo: string = '/auth'
) => {
  return withAuth(PageComponent, redirectTo);
};
