/**
 * Utility functions cho việc tạo mã nhân viên
 */

/**
 * Loại bỏ dấu tiếng Việt từ chuỗi
 * @param str Chuỗi cần loại bỏ dấu
 * @returns Chuỗi đã loại bỏ dấu
 */
export const removeVietnameseTones = (str: string): string => {
  const accentsMap: Record<string, string> = {
    à: 'a', á: 'a', ạ: 'a', ả: 'a', ã: 'a', â: 'a', ầ: 'a', ấ: 'a', ậ: 'a', ẩ: 'a', ẫ: 'a', ă: 'a', ằ: 'a', ắ: 'a', ặ: 'a', ẳ: 'a', ẵ: 'a',
    è: 'e', é: 'e', ẹ: 'e', ẻ: 'e', ẽ: 'e', ê: 'e', ề: 'e', ế: 'e', ệ: 'e', ể: 'e', ễ: 'e',
    ì: 'i', í: 'i', ị: 'i', ỉ: 'i', ĩ: 'i',
    ò: 'o', ó: 'o', ọ: 'o', ỏ: 'o', õ: 'o', ô: 'o', ồ: 'o', ố: 'o', ộ: 'o', ổ: 'o', ỗ: 'o', ơ: 'o', ờ: 'o', ớ: 'o', ợ: 'o', ở: 'o', ỡ: 'o',
    ù: 'u', ú: 'u', ụ: 'u', ủ: 'u', ũ: 'u', ư: 'u', ừ: 'u', ứ: 'u', ự: 'u', ử: 'u', ữ: 'u',
    ỳ: 'y', ý: 'y', ỵ: 'y', ỷ: 'y', ỹ: 'y',
    đ: 'd',
    À: 'A', Á: 'A', Ạ: 'A', Ả: 'A', Ã: 'A', Â: 'A', Ầ: 'A', Ấ: 'A', Ậ: 'A', Ẩ: 'A', Ẫ: 'A', Ă: 'A', Ằ: 'A', Ắ: 'A', Ặ: 'A', Ẳ: 'A', Ẵ: 'A',
    È: 'E', É: 'E', Ẹ: 'E', Ẻ: 'E', Ẽ: 'E', Ê: 'E', Ề: 'E', Ế: 'E', Ệ: 'E', Ể: 'E', Ễ: 'E',
    Ì: 'I', Í: 'I', Ị: 'I', Ỉ: 'I', Ĩ: 'I',
    Ò: 'O', Ó: 'O', Ọ: 'O', Ỏ: 'O', Õ: 'O', Ô: 'O', Ồ: 'O', Ố: 'O', Ộ: 'O', Ổ: 'O', Ỗ: 'O', Ơ: 'O', Ờ: 'O', Ớ: 'O', Ợ: 'O', Ở: 'O', Ỡ: 'O',
    Ù: 'U', Ú: 'U', Ụ: 'U', Ủ: 'U', Ũ: 'U', Ư: 'U', Ừ: 'U', Ứ: 'U', Ự: 'U', Ử: 'U', Ữ: 'U',
    Ỳ: 'Y', Ý: 'Y', Ỵ: 'Y', Ỷ: 'Y', Ỹ: 'Y',
    Đ: 'D'
  };

  return str.replace(/[ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặẸẹẺẻẼẽẾếỀềỂểỄễỆệỈỉỊịỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợỤụỦủỨứỪừỬửỮữỰựỲỳỴỵỶỷỸỹ]/g, (match) => accentsMap[match] || match);
};

/**
 * Lấy từ cuối cùng từ họ và tên
 * @param fullName Họ và tên đầy đủ
 * @returns Từ cuối cùng đã loại bỏ dấu và chuyển thành chữ hoa
 */
export const getLastNameFromFullName = (fullName: string): string => {
  if (!fullName || fullName.trim() === '') {
    return 'USER';
  }

  const words = fullName.trim().split(/\s+/);
  const lastName = words[words.length - 1];
  
  // Loại bỏ dấu và chuyển thành chữ hoa
  return removeVietnameseTones(lastName).toUpperCase();
};

/**
 * Tạo 4 số ngẫu nhiên
 * @returns Chuỗi 4 số ngẫu nhiên
 */
export const generateRandomNumbers = (): string => {
  return Math.floor(1000 + Math.random() * 9000).toString();
};

/**
 * Tạo mã nhân viên theo mẫu: NV + ngày tháng năm + từ cuối cùng của họ và tên (bỏ dấu) + 4 số ngẫu nhiên
 * Ví dụ: NV20241215NGUYEN1234
 * @param fullName Họ và tên đầy đủ
 * @param date Ngày tạo (mặc định là ngày hiện tại)
 * @returns Mã nhân viên
 */
export const generateEmployeeCode = (fullName: string, date?: Date): string => {
  const currentDate = date || new Date();
  
  // Format ngày: YYYYMMDD
  const year = currentDate.getFullYear();
  const month = String(currentDate.getMonth() + 1).padStart(2, '0');
  const day = String(currentDate.getDate()).padStart(2, '0');
  const dateString = `${year}${month}${day}`;
  
  // Lấy từ cuối cùng của họ và tên
  const lastName = getLastNameFromFullName(fullName);
  
  // Tạo 4 số ngẫu nhiên
  const randomNumbers = generateRandomNumbers();
  
  // Kết hợp tất cả: NV + ngày + họ + số ngẫu nhiên
  return `NV${dateString}${lastName}${randomNumbers}`;
};

/**
 * Kiểm tra tính hợp lệ của mã nhân viên
 * @param employeeCode Mã nhân viên cần kiểm tra
 * @returns true nếu hợp lệ, false nếu không hợp lệ
 */
export const isValidEmployeeCode = (employeeCode: string): boolean => {
  // Mẫu: NV + 8 số (ngày) + chữ cái (họ) + 4 số
  // Ví dụ: NV20241215NGUYEN1234
  const pattern = /^NV\d{8}[A-Z]+\d{4}$/;
  return pattern.test(employeeCode);
};

/**
 * Tạo mã nhân viên duy nhất với retry logic
 * @param fullName Họ và tên đầy đủ
 * @param checkExistence Function để kiểm tra mã đã tồn tại chưa
 * @param maxRetries Số lần thử lại tối đa (mặc định: 10)
 * @returns Mã nhân viên duy nhất
 */
export const generateUniqueEmployeeCode = async (
  fullName: string,
  checkExistence: (code: string) => Promise<boolean>,
  maxRetries = 10
): Promise<string> => {
  let attempts = 0;
  
  while (attempts < maxRetries) {
    const employeeCode = generateEmployeeCode(fullName);
    
    // Kiểm tra xem mã đã tồn tại chưa
    const exists = await checkExistence(employeeCode);
    
    if (!exists) {
      return employeeCode;
    }
    
    attempts++;
  }
  
  // Nếu không tạo được mã duy nhất sau maxRetries lần thử
  throw new Error(`Không thể tạo mã nhân viên duy nhất sau ${maxRetries} lần thử`);
};
