/**
 * Markdown Message Component
 * Renders markdown content with syntax highlighting
 */

import DOMPurify from 'dompurify';
import React from 'react';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';

import { MarkdownContentData } from '@/shared/websocket/types/chat-message.types';

// Props interface
export interface MarkdownMessageProps {
  data: MarkdownContentData;
  className?: string;
  theme?: 'light' | 'dark';
}

/**
 * Markdown Message Component
 */
const MarkdownMessage: React.FC<MarkdownMessageProps> = ({ 
  data, 
  className = '',
  theme = 'light'
}) => {
  const { markdown, allowHtml = false, sanitize = true } = data;

  // Sanitize content if needed
  const sanitizedMarkdown = sanitize ? DOMPurify.sanitize(markdown) : markdown;

  // Custom components for markdown rendering
  const components = {
    // Code blocks with syntax highlighting
    code: ({ node, inline, className: codeClassName, children, ...props }: any) => {
      const match = /language-(\w+)/.exec(codeClassName || '');
      const language = match ? match[1] : '';

      if (!inline && language) {
        return (
          <SyntaxHighlighter
            style={theme === 'dark' ? oneDark : oneLight}
            language={language}
            PreTag="div"
            className="rounded-md"
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </SyntaxHighlighter>
        );
      }

      return (
        <code 
          className="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono"
          {...props}
        >
          {children}
        </code>
      );
    },

    // Custom link rendering
    a: ({ href, children, ...props }: any) => (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-600 dark:text-blue-400 hover:underline"
        {...props}
      >
        {children}
      </a>
    ),

    // Custom blockquote styling
    blockquote: ({ children, ...props }: any) => (
      <blockquote
        className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 py-2 my-2 bg-gray-50 dark:bg-gray-800/50 rounded-r"
        {...props}
      >
        {children}
      </blockquote>
    ),

    // Custom table styling
    table: ({ children, ...props }: any) => (
      <div className="overflow-x-auto my-4">
        <table
          className="min-w-full border border-gray-200 dark:border-gray-700 rounded-lg"
          {...props}
        >
          {children}
        </table>
      </div>
    ),

    thead: ({ children, ...props }: any) => (
      <thead className="bg-gray-50 dark:bg-gray-800" {...props}>
        {children}
      </thead>
    ),

    th: ({ children, ...props }: any) => (
      <th
        className="px-4 py-2 text-left font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700"
        {...props}
      >
        {children}
      </th>
    ),

    td: ({ children, ...props }: any) => (
      <td
        className="px-4 py-2 text-gray-700 dark:text-gray-300 border-b border-gray-200 dark:border-gray-700"
        {...props}
      >
        {children}
      </td>
    ),

    // Custom heading styling
    h1: ({ children, ...props }: any) => (
      <h1 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h1>
    ),

    h2: ({ children, ...props }: any) => (
      <h2 className="text-xl font-bold mb-3 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h2>
    ),

    h3: ({ children, ...props }: any) => (
      <h3 className="text-lg font-bold mb-2 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h3>
    ),

    h4: ({ children, ...props }: any) => (
      <h4 className="text-base font-bold mb-2 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h4>
    ),

    h5: ({ children, ...props }: any) => (
      <h5 className="text-sm font-bold mb-1 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h5>
    ),

    h6: ({ children, ...props }: any) => (
      <h6 className="text-xs font-bold mb-1 text-gray-900 dark:text-gray-100" {...props}>
        {children}
      </h6>
    ),

    // Custom list styling
    ul: ({ children, ...props }: any) => (
      <ul className="list-disc list-inside mb-4 space-y-1" {...props}>
        {children}
      </ul>
    ),

    ol: ({ children, ...props }: any) => (
      <ol className="list-decimal list-inside mb-4 space-y-1" {...props}>
        {children}
      </ol>
    ),

    li: ({ children, ...props }: any) => (
      <li className="text-gray-700 dark:text-gray-300" {...props}>
        {children}
      </li>
    ),

    // Custom paragraph styling
    p: ({ children, ...props }: any) => (
      <p className="mb-3 text-gray-700 dark:text-gray-300 leading-relaxed" {...props}>
        {children}
      </p>
    ),

    // Custom horizontal rule
    hr: ({ ...props }: any) => (
      <hr className="my-6 border-gray-200 dark:border-gray-700" {...props} />
    ),

    // Custom image styling
    img: ({ src, alt, ...props }: any) => (
      <img
        src={src}
        alt={alt}
        className="max-w-full h-auto rounded-lg my-4"
        loading="lazy"
        {...props}
      />
    ),
  };

  return (
    <div className={`p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}>
      <div className="prose prose-sm dark:prose-invert max-w-none">
        <ReactMarkdown
          components={components}
          allowedElements={allowHtml ? undefined : [
            'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'p', 'br', 'strong', 'em', 'u', 's',
            'code', 'pre', 'blockquote',
            'ul', 'ol', 'li',
            'a', 'img',
            'table', 'thead', 'tbody', 'tr', 'th', 'td',
            'hr'
          ]}
          skipHtml={!allowHtml}
        >
          {sanitizedMarkdown}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export default MarkdownMessage;
