import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  FormItem,
  FormGrid,
  Input,
  Select,
  Toggle,
  Button,
  Typography,
  Card,
  Icon,
  Textarea,
  IconName,
} from '@/shared/components/common';

import { SMS_PROVIDER_TEMPLATES, DEFAULT_RATE_LIMITS, DEFAULT_RETRY_CONFIG } from '../constants';
import { smsProviderFormSchema } from '../schemas';
import { SmsProviderFormData } from '../types';
import { SmsProviderType, SmsProviderConfig } from '../types';

interface SmsProviderFormProps {
  /**
   * Initial data for editing
   */
  initialData?: Partial<SmsProviderConfig>;

  /**
   * Callback when form is submitted
   */
  onSubmit: (data: SmsProviderFormData) => void | Promise<void>;

  /**
   * Callback when form is cancelled
   */
  onCancel?: () => void;

  /**
   * Loading state
   */
  loading?: boolean;

  /**
   * Form mode
   */
  mode?: 'create' | 'edit';
}

/**
 * Form component để tạo/chỉnh sửa SMS Provider
 */
const SmsProviderForm: React.FC<SmsProviderFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
  mode = 'create',
}) => {
  const { t } = useTranslation(['integration', 'common']);

  // Form state
  const [selectedType, setSelectedType] = useState<SmsProviderType>(
    initialData?.type || 'twilio'
  );

  // Default form values
  const defaultValues: Partial<SmsProviderFormData> = {
    name: initialData?.name || '',
    type: initialData?.type || 'twilio',
    displayName: initialData?.displayName || '',
    description: initialData?.description || '',
    credentials: initialData?.credentials || {},
    settings: {
      rateLimits: initialData?.settings?.rateLimits || DEFAULT_RATE_LIMITS,
      retryConfig: initialData?.settings?.retryConfig || DEFAULT_RETRY_CONFIG,
      enableDeliveryReports: initialData?.settings?.enableDeliveryReports ?? true,
      enableOptOut: initialData?.settings?.enableOptOut ?? true,
      timezone: initialData?.settings?.timezone || 'Asia/Ho_Chi_Minh',
      fromNumber: initialData?.settings?.fromNumber || '',
      fromName: initialData?.settings?.fromName || '',
      webhookUrl: initialData?.settings?.webhookUrl || '',
    },
    isDefault: initialData?.isDefault || false,
  };

  // Provider options for select
  const providerOptions = Object.values(SMS_PROVIDER_TEMPLATES).map(template => ({
    value: template.type,
    label: template.displayName,
    description: template.description,
    icon: template.icon,
  }));

  // Get current provider template
  const currentTemplate = SMS_PROVIDER_TEMPLATES[selectedType];

  // Handle form submission
  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    
    const formData = new FormData(event.currentTarget);
    const data = Object.fromEntries(formData.entries());
    
    // Parse form data
    const parsedData: SmsProviderFormData = {
      name: data.name as string,
      type: data.type as SmsProviderType,
      displayName: data.displayName as string,
      description: data.description as string || undefined,
      credentials: {
        // Parse credentials based on provider type
        ...(data.accountSid && { accountSid: data.accountSid as string }),
        ...(data.authToken && { authToken: data.authToken as string }),
        ...(data.accessKeyId && { accessKeyId: data.accessKeyId as string }),
        ...(data.secretAccessKey && { secretAccessKey: data.secretAccessKey as string }),
        ...(data.region && { region: data.region as string }),
        ...(data.apiKey && { apiKey: data.apiKey as string }),
        ...(data.apiSecret && { apiSecret: data.apiSecret as string }),
        ...(data.username && { username: data.username as string }),
        ...(data.password && { password: data.password as string }),
        ...(data.endpoint && { endpoint: data.endpoint as string }),
      },
      settings: {
        fromNumber: data.fromNumber as string || undefined,
        fromName: data.fromName as string || undefined,
        webhookUrl: data.webhookUrl as string || undefined,
        rateLimits: {
          perSecond: Number(data.perSecond) || DEFAULT_RATE_LIMITS.perSecond,
          perMinute: Number(data.perMinute) || DEFAULT_RATE_LIMITS.perMinute,
          perHour: Number(data.perHour) || DEFAULT_RATE_LIMITS.perHour,
          perDay: Number(data.perDay) || DEFAULT_RATE_LIMITS.perDay,
          perMonth: Number(data.perMonth) || DEFAULT_RATE_LIMITS.perMonth,
        },
        retryConfig: {
          maxRetries: Number(data.maxRetries) || DEFAULT_RETRY_CONFIG.maxRetries,
          retryDelay: Number(data.retryDelay) || DEFAULT_RETRY_CONFIG.retryDelay,
          backoffMultiplier: Number(data.backoffMultiplier) || DEFAULT_RETRY_CONFIG.backoffMultiplier,
        },
        enableDeliveryReports: data.enableDeliveryReports === 'on',
        enableOptOut: data.enableOptOut === 'on',
        timezone: data.timezone as string || 'Asia/Ho_Chi_Minh',
      },
      isDefault: data.isDefault === 'on',
    };

    try {
      // Validate data
      const validatedData = smsProviderFormSchema.parse(parsedData);
      await onSubmit(validatedData as SmsProviderFormData);
    } catch (error: unknown) {
      console.error('Form validation error:', error);
      // Handle validation errors here if needed
    }
  };

  // Handle provider type change
  const handleProviderTypeChange = (value: string | number | string[] | number[]) => {
    setSelectedType(value as SmsProviderType);
  };

  // Render credentials fields based on provider type
  const renderCredentialsFields = () => {
    const template = SMS_PROVIDER_TEMPLATES[selectedType];
    
    return (
      <Card className="p-4">
        <Typography variant="h6" className="mb-4 flex items-center">
          <Icon name="key" size="sm" className="mr-2" />
          {t('integration:sms.credentials', 'Thông tin xác thực')}
        </Typography>
        
        <FormGrid columns={2} columnsSm={1} gap="md">
          {template.requiredCredentials.includes('accountSid') && (
            <FormItem
              name="accountSid"
              label="Account SID"
              required
              helpText={t('integration:sms.accountSidHelp', 'Account SID từ Twilio Console')}
            >
              <Input
                type="text"
                placeholder="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                defaultValue={defaultValues.credentials?.accountSid}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('authToken') && (
            <FormItem
              name="authToken"
              label="Auth Token"
              required
              helpText={t('integration:sms.authTokenHelp', 'Auth Token từ Twilio Console')}
            >
              <Input
                type="password"
                placeholder="••••••••••••••••••••••••••••••••"
                defaultValue={defaultValues.credentials?.authToken}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('accessKeyId') && (
            <FormItem
              name="accessKeyId"
              label="Access Key ID"
              required
              helpText={t('integration:sms.accessKeyIdHelp', 'AWS Access Key ID')}
            >
              <Input
                type="text"
                placeholder="AKIAIOSFODNN7EXAMPLE"
                defaultValue={defaultValues.credentials?.accessKeyId}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('secretAccessKey') && (
            <FormItem
              name="secretAccessKey"
              label="Secret Access Key"
              required
              helpText={t('integration:sms.secretAccessKeyHelp', 'AWS Secret Access Key')}
            >
              <Input
                type="password"
                placeholder="••••••••••••••••••••••••••••••••"
                defaultValue={defaultValues.credentials?.secretAccessKey}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('region') && (
            <FormItem
              name="region"
              label="Region"
              required
              helpText={t('integration:sms.regionHelp', 'AWS Region (vd: us-east-1)')}
            >
              <Input
                type="text"
                placeholder="us-east-1"
                defaultValue={defaultValues.credentials?.region}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('apiKey') && (
            <FormItem
              name="apiKey"
              label="API Key"
              required
              helpText={t('integration:sms.apiKeyHelp', 'API Key từ nhà cung cấp')}
            >
              <Input
                type="text"
                placeholder="your-api-key"
                defaultValue={defaultValues.credentials?.apiKey}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('apiSecret') && (
            <FormItem
              name="apiSecret"
              label="API Secret"
              required
              helpText={t('integration:sms.apiSecretHelp', 'API Secret từ nhà cung cấp')}
            >
              <Input
                type="password"
                placeholder="••••••••••••••••••••••••••••••••"
                defaultValue={defaultValues.credentials?.apiSecret}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('username') && (
            <FormItem
              name="username"
              label="Username"
              required
              helpText={t('integration:sms.usernameHelp', 'Username tài khoản')}
            >
              <Input
                type="text"
                placeholder="your-username"
                defaultValue={defaultValues.credentials?.username}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('password') && (
            <FormItem
              name="password"
              label="Password"
              required
              helpText={t('integration:sms.passwordHelp', 'Password tài khoản')}
            >
              <Input
                type="password"
                placeholder="••••••••••••••••••••••••••••••••"
                defaultValue={defaultValues.credentials?.password}
                fullWidth
              />
            </FormItem>
          )}

          {template.requiredCredentials.includes('endpoint') && (
            <FormItem
              name="endpoint"
              label="API Endpoint"
              required
              helpText={t('integration:sms.endpointHelp', 'URL endpoint của API')}
            >
              <Input
                type="url"
                placeholder="https://api.example.com/sms"
                defaultValue={defaultValues.credentials?.endpoint}
                fullWidth
              />
            </FormItem>
          )}
        </FormGrid>
      </Card>
    );
  };

  return (
    <form
      onSubmit={handleSubmit}
      className="space-y-6"
    >
      {/* Basic Information */}
      <Card className="p-4">
        <Typography variant="h6" className="mb-4 flex items-center">
          <Icon name="info" size="sm" className="mr-2" />
          {t('integration:sms.basicInfo', 'Thông tin cơ bản')}
        </Typography>
        
        <FormGrid columns={2} columnsSm={1} gap="md">
          <FormItem
            name="name"
            label={t('integration:sms.name', 'Tên cấu hình')}
            required
            helpText={t('integration:sms.nameHelp', 'Tên để nhận diện cấu hình này')}
          >
            <Input
              type="text"
              placeholder={t('integration:sms.namePlaceholder', 'VD: Twilio Production')}
              defaultValue={defaultValues.name}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="type"
            label={t('integration:sms.providerType', 'Loại nhà cung cấp')}
            required
          >
            <Select
              options={providerOptions}
              placeholder={t('integration:sms.selectProvider', 'Chọn nhà cung cấp')}
              value={selectedType}
              onChange={handleProviderTypeChange}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="displayName"
            label={t('integration:sms.displayName', 'Tên hiển thị')}
            required
            helpText={t('integration:sms.displayNameHelp', 'Tên hiển thị trong giao diện')}
          >
            <Input
              type="text"
              placeholder={t('integration:sms.displayNamePlaceholder', 'VD: SMS Chính')}
              defaultValue={defaultValues.displayName}
              fullWidth
            />
          </FormItem>

          <FormItem
            name="isDefault"
            label={t('integration:sms.isDefault', 'Đặt làm mặc định')}
            inline
          >
            <Toggle checked={defaultValues.isDefault} />
          </FormItem>
        </FormGrid>

        <FormItem
          name="description"
          label={t('integration:sms.description', 'Mô tả')}
          helpText={t('integration:sms.descriptionHelp', 'Mô tả chi tiết về cấu hình này')}
        >
          <Textarea
            placeholder={t('integration:sms.descriptionPlaceholder', 'Mô tả cấu hình...')}
            defaultValue={defaultValues.description}
            rows={3}
            fullWidth
          />
        </FormItem>
      </Card>

      {/* Provider Information */}
      {currentTemplate && (
        <Card className="p-4 bg-muted/30">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon name={currentTemplate.icon as IconName} size="lg" className="text-primary" />
              </div>
            </div>
            <div className="flex-1">
              <Typography variant="h6" className="font-semibold">
                {currentTemplate.displayName}
              </Typography>
              <Typography variant="body2" className="text-muted-foreground mb-3">
                {currentTemplate.description}
              </Typography>
              
              {currentTemplate.configurationSteps.length > 0 && (
                <div>
                  <Typography variant="body2" className="font-medium mb-2">
                    {t('integration:sms.configurationSteps', 'Các bước cấu hình')}:
                  </Typography>
                  <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
                    {currentTemplate.configurationSteps.map((step, index) => (
                      <li key={index}>{step}</li>
                    ))}
                  </ol>
                </div>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Credentials */}
      {renderCredentialsFields()}

      {/* Actions */}
      <div className="flex items-center justify-end space-x-3 pt-6 border-t">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            {t('common:cancel', 'Hủy')}
          </Button>
        )}
        
        <Button
          type="submit"
          variant="primary"
          isLoading={loading}
          disabled={loading}
        >
          <Icon name="save" size="sm" className="mr-2" />
          {mode === 'create' 
            ? t('integration:sms.create', 'Tạo cấu hình')
            : t('integration:sms.update', 'Cập nhật')
          }
        </Button>
      </div>
    </form>
  );
};

export default SmsProviderForm;
