/**
 * Provider cho Task Queue
 */
import React from 'react';

import { useTaskQueue } from '@/shared/hooks/common/useTaskQueue';

import { TaskQueueContext } from './taskQueueContext.context';
import { TaskQueueProviderProps } from './taskQueueContext.types';

/**
 * Provider cho Task Queue
 */
export const TaskQueueProvider: React.FC<TaskQueueProviderProps> = ({ options, children }) => {
  const taskQueue = useTaskQueue(options);

  return <TaskQueueContext.Provider value={taskQueue}>{children}</TaskQueueContext.Provider>;
};

// Không re-export hook ở đây để tránh lỗi fast refresh
export default TaskQueueProvider;
