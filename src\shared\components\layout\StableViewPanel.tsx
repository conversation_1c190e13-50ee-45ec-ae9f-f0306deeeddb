import { ReactNode, memo } from 'react';

import ViewPanel from './view-panel/ViewPanel';

interface StableViewPanelProps {
  title: string;
  children: ReactNode;
  actions?: ReactNode;
}

/**
 * Wrapper component để tối ưu hóa ViewPanel
 * Sử dụng memo với shallow comparison để tránh re-render không cần thiết
 */
const StableViewPanel = memo<StableViewPanelProps>(({ title, children, actions }) => {
  console.log('[StableViewPanel] Rendering with title:', title);
  
  return (
    <ViewPanel title={title} actions={actions}>
      {children}
    </ViewPanel>
  );
});

StableViewPanel.displayName = 'StableViewPanel';

export default StableViewPanel;
