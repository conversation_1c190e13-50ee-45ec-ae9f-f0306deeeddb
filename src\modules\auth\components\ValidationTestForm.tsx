import React from 'react';
import { useTranslation } from 'react-i18next';

import { Button, Form, FormGrid, FormItem, Input } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { PasswordInput } from '@/shared/components/common/PasswordInput';
import { useFormErrors } from '@/shared/hooks';

import { CompanyRegisterFormValues, createCompanyRegisterSchema } from '../schemas/auth.schema';

/**
 * Test form để kiểm tra validation behavior
 */
const ValidationTestForm: React.FC = () => {
  const { t } = useTranslation('auth');
  const { formRef, setFormErrors } = useFormErrors<CompanyRegisterFormValues>();

  // Create company register schema with translations
  const registerSchema = createCompanyRegisterSchema(t);

  // Handle form submission
  const handleSubmit = (values: unknown) => {
    const registerValues = values as CompanyRegisterFormValues;
    console.log('✅ Form validation passed! Data:', registerValues);
    
    // Reset form errors
    setFormErrors({});
    
    // Simulate API success
    alert('Form submitted successfully! Check console for data.');
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6 bg-background">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-foreground mb-2">
          Validation Test Form
        </h2>
        <p className="text-muted">
          Test form để kiểm tra cơ chế validation. Hãy thử submit form trống và sau đó nhập dữ liệu không hợp lệ.
        </p>
      </div>

      <Form
        ref={formRef as unknown as React.RefObject<FormRef<any>>}
        schema={registerSchema}
        onSubmit={handleSubmit}
        className="space-y-6"
        autoComplete="off"
      >
        <FormItem name="companyName" label={t('companyName')} required>
          <Input fullWidth autoComplete="off" placeholder="Nhập tên công ty (tối thiểu 3 ký tự)" />
        </FormItem>

        <FormItem name="companyEmail" label={t('email')} required>
          <Input type="email" fullWidth autoComplete="off" placeholder="Nhập email công ty" />
        </FormItem>

        <FormItem name="phoneNumber" label={t('phone')} required>
          <Input fullWidth placeholder="Nhập số điện thoại (10-15 chữ số)" />
        </FormItem>

        <FormGrid columns={2}>
          <FormItem name="password" label={t('password')} required>
            <PasswordInput fullWidth autoComplete="new-password" placeholder="Tối thiểu 8 ký tự" />
          </FormItem>

          <FormItem name="confirmPassword" label={t('confirmPassword')} required>
            <PasswordInput fullWidth autoComplete="new-password" placeholder="Nhập lại mật khẩu" />
          </FormItem>
        </FormGrid>

        <div className="pt-4">
          <Button type="submit" variant="primary" fullWidth>
            Test Validation
          </Button>
        </div>

        <div className="mt-6 p-4 bg-card rounded-lg border">
          <h3 className="font-semibold text-foreground mb-2">Test Cases:</h3>
          <ul className="text-sm text-muted space-y-1">
            <li>• <strong>Trường trống:</strong> Bây giờ sẽ hiện message cụ thể thay vì "Required"</li>
            <li>• <strong>Tên công ty:</strong> Nhập &lt; 3 ký tự → "Tên công ty phải có ít nhất 3 ký tự"</li>
            <li>• <strong>Email:</strong> Nhập email không hợp lệ → "Email không hợp lệ"</li>
            <li>• <strong>Số điện thoại:</strong> Nhập không phải số → "Số điện thoại phải có 10-15 chữ số"</li>
            <li>• <strong>Mật khẩu:</strong> Nhập &lt; 8 ký tự → "Mật khẩu phải có ít nhất 8 ký tự"</li>
            <li>• <strong>Xác nhận mật khẩu:</strong> Nhập khác mật khẩu → "Mật khẩu và xác nhận mật khẩu không khớp"</li>
          </ul>
        </div>
      </Form>
    </div>
  );
};

export default ValidationTestForm;
